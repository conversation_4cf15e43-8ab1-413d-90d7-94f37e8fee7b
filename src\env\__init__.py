"""
SPACE-DMPO Environment Package

This package contains the SPACE-DMPO simulation environment including:
- Core OAAL environment components
- Satellite system simulation
- Task management system
"""

# Core components
from .satellite import SatelliteNode, SatelliteStatus
from .task import Task, TaskState
from .communication import CommunicationManager
from .orbital_updater import OrbitalUpdater
from .adapters import SatelliteAdapter, TaskAdapter

# Environment wrappers
try:
    from .satellite_env import SatelliteParallelEnv, SatelliteAECEnv, parallel_env, aec_env
except ImportError:
    pass

__all__ = [
    # Core components
    'SatelliteNode', 'SatelliteStatus',
    'Task', 'TaskState',
    'CommunicationManager',
    'OrbitalUpdater',
    'SatelliteAdapter', 'TaskAdapter',
    # Environment wrappers
    'SatelliteParallelEnv', 'SatelliteAECEnv', 'parallel_env', 'aec_env'
]