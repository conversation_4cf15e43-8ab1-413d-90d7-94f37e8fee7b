<mxfile host="app.diagrams.net" modified="2025-01-23T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="SPACE-OAAL超详细系统架构图" id="complete-detailed-architecture">
    <mxGraphModel dx="3200" dy="2000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2800" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 系统入口层 -->
        <mxCell id="system-entry-group" value="系统入口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="2700" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="main-entry" value="main()&#xa;程序主入口&#xa;- 创建IntegrationManager实例&#xa;- 执行仿真主循环&#xa;- 处理异常和清理资源" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=12;fontStyle=1" vertex="1" parent="system-entry-group">
          <mxGeometry x="50" y="40" width="200" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-manager-entry" value="IntegrationManager.__init__()&#xa;集成管理器初始化&#xa;- 加载配置文件&#xa;- 创建适配器实例&#xa;- 初始化仿真统计" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=12;fontStyle=1" vertex="1" parent="system-entry-group">
          <mxGeometry x="300" y="40" width="220" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="simulation-loop" value="仿真主循环&#xa;for timeslot in range(1000):&#xa;    step(timeslot)&#xa;- 时间步控制&#xa;- 异常处理&#xa;- 进度监控" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=12;fontStyle=1" vertex="1" parent="system-entry-group">
          <mxGeometry x="570" y="40" width="200" height="70" as="geometry" />
        </mxCell>
        
        <!-- 配置和数据源层 -->
        <mxCell id="config-data-group" value="配置和数据源层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="200" width="2700" height="150" as="geometry" />
        </mxCell>
        
        <!-- 配置文件详细结构 -->
        <mxCell id="config-yaml-detailed" value="config.yaml&#xa;系统配置文件&#xa;system:&#xa;  total_timeslots: 1000&#xa;  time_delta: 1.0&#xa;satellite:&#xa;  count: 36&#xa;  battery_capacity_j: 100000&#xa;  cpu_frequency_hz: 2000000000&#xa;  memory_total_mb: 8192&#xa;  storage_total_mb: 1024000&#xa;communication:&#xa;  visibility_threshold_km: 1000&#xa;  signal_power_w: 10&#xa;  noise_power_w: 0.001&#xa;task:&#xa;  generation_rate: 0.1&#xa;  priority_weights: [0.4, 0.3, 0.3]&#xa;  max_queue_size: 100" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10;align=left" vertex="1" parent="config-data-group">
          <mxGeometry x="50" y="40" width="300" height="100" as="geometry" />
        </mxCell>
        
        <!-- 卫星轨道数据详细结构 -->
        <mxCell id="satellite-data-detailed" value="satellite_processed_data.csv&#xa;36颗卫星×1000时隙轨道数据&#xa;格式: satellite_id,timeslot,lat,lon,illuminated&#xa;数据量: 36,000行&#xa;文件大小: ~2MB&#xa;加载时间: ~100ms&#xa;内存占用: ~50MB&#xa;索引结构: {satellite_id: {timeslot: data}}&#xa;缓存策略: LRU缓存最近100个时隙" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10;align=left" vertex="1" parent="config-data-group">
          <mxGeometry x="380" y="40" width="280" height="100" as="geometry" />
        </mxCell>
        
        <!-- 地面站数据详细结构 -->
        <mxCell id="ground-stations-detailed" value="updated_global_ground_stations.csv&#xa;420个地面站坐标数据&#xa;格式: ID,Latitude,Longitude,RegionType,Size,PurposeType&#xa;区域分布:&#xa;  - 亚洲: 120个 (28.6%)&#xa;  - 欧洲: 95个 (22.6%)&#xa;  - 北美: 85个 (20.2%)&#xa;  - 其他: 120个 (28.6%)&#xa;用途分类:&#xa;  - 商业: 180个 (42.9%)&#xa;  - 科研: 140个 (33.3%)&#xa;  - 军用: 100个 (23.8%)" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10;align=left" vertex="1" parent="config-data-group">
          <mxGeometry x="690" y="40" width="280" height="100" as="geometry" />
        </mxCell>
        
        <!-- 任务生成数据详细结构 -->
        <mxCell id="task-data-detailed" value="task_generation_results.json&#xa;任务生成数据&#xa;结构: {timeslot: [tasks]}&#xa;任务属性:&#xa;  - task_id: 唯一标识符&#xa;  - data_size_mb: 数据大小(1-100MB)&#xa;  - computation_cycles: 计算需求(1M-1B cycles)&#xa;  - deadline_s: 截止时间(10-300s)&#xa;  - priority: 优先级(1-5)&#xa;  - source_location: 源地面站ID&#xa;  - target_location: 目标地面站ID&#xa;生成规律: 泊松分布，λ=0.1/时隙" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10;align=left" vertex="1" parent="config-data-group">
          <mxGeometry x="1000" y="40" width="300" height="100" as="geometry" />
        </mxCell>
        
        <!-- 适配器集成层详细 -->
        <mxCell id="adapter-detailed-group" value="适配器集成层 (详细)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="380" width="2700" height="200" as="geometry" />
        </mxCell>
        
        <!-- IntegrationManager详细 -->
        <mxCell id="integration-manager-detailed" value="IntegrationManager&#xa;集成管理器 (详细)&#xa;&#xa;属性:&#xa;- config: Dict[str, Any]&#xa;- satellite_adapter: SatelliteAdapter&#xa;- task_adapter: TaskAdapter&#xa;- current_timeslot: int = 0&#xa;- total_timeslots: int = 1000&#xa;- simulation_stats: Dict[str, Any]&#xa;&#xa;方法:&#xa;+ __init__(config_file: str)&#xa;+ step(timeslot: int) -> Dict[str, Any]&#xa;+ get_system_state() -> Dict[str, Any]&#xa;+ export_results(filename: str) -> None&#xa;+ reset() -> None&#xa;- _load_config(config_file: str) -> Dict[str, Any]&#xa;- _assign_tasks_to_satellites(tasks, satellites) -> None&#xa;- _find_best_satellite_for_task(task) -> str&#xa;- _update_simulation_stats(satellites) -> None" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=10;fontStyle=1;align=left" vertex="1" parent="adapter-detailed-group">
          <mxGeometry x="50" y="40" width="350" height="150" as="geometry" />
        </mxCell>
        
        <!-- SatelliteAdapter详细 -->
        <mxCell id="satellite-adapter-detailed" value="SatelliteAdapter&#xa;卫星适配器 (详细)&#xa;&#xa;属性:&#xa;- orbital_updater: OrbitalUpdater&#xa;- communication_manager: CommunicationManager&#xa;- satellite_nodes: Dict[str, SatelliteNode]&#xa;- config: Dict[str, Any]&#xa;- last_sync_timeslot: int = -1&#xa;&#xa;方法:&#xa;+ __init__(config: Dict[str, Any])&#xa;+ sync_all_satellites(time_step: int) -> Dict[str, SatelliteNode]&#xa;+ create_satellite_node(orbital_satellite) -> SatelliteNode&#xa;+ update_satellite_node(satellite_id, orbital_satellite) -> bool&#xa;+ get_all_satellite_nodes() -> Dict[str, SatelliteNode]&#xa;- _update_communication_links(time_step: int) -> None&#xa;- _update_ground_station_links(time_step: int) -> None&#xa;- _convert_orbital_to_node_position(orbital_sat) -> Position&#xa;- _calculate_energy_state(orbital_sat, time_step) -> EnergyState" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10;fontStyle=1;align=left" vertex="1" parent="adapter-detailed-group">
          <mxGeometry x="430" y="40" width="400" height="150" as="geometry" />
        </mxCell>
        
        <!-- TaskAdapter详细 -->
        <mxCell id="task-adapter-detailed" value="TaskAdapter&#xa;任务适配器 (详细)&#xa;&#xa;属性:&#xa;- task_generator: TaskGenerator&#xa;- enhanced_tasks: Dict[str, EnhancedTask]&#xa;- task_conversion_cache: Dict[str, EnhancedTask]&#xa;- generation_stats: Dict[str, int]&#xa;&#xa;方法:&#xa;+ __init__()&#xa;+ generate_tasks_for_timeslot(timeslot: int) -> List[EnhancedTask]&#xa;+ convert_generated_task(generated_task) -> EnhancedTask&#xa;+ batch_convert_tasks(generated_tasks) -> List[EnhancedTask]&#xa;+ get_enhanced_task(task_id: str) -> Optional[EnhancedTask]&#xa;+ get_task_statistics() -> Dict[str, Any]&#xa;- _create_enhanced_task_from_generated(task) -> EnhancedTask&#xa;- _calculate_dynamic_priority(task, current_time) -> float&#xa;- _validate_task_parameters(task) -> bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10;fontStyle=1;align=left" vertex="1" parent="adapter-detailed-group">
          <mxGeometry x="860" y="40" width="400" height="150" as="geometry" />
        </mxCell>
        
        <!-- 核心业务层超详细 -->
        <mxCell id="core-business-detailed-group" value="核心业务层 (超详细)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="610" width="2700" height="300" as="geometry" />
        </mxCell>
        
        <!-- OrbitalUpdater超详细 -->
        <mxCell id="orbital-updater-detailed" value="OrbitalUpdater&#xa;轨道更新模块 (超详细)&#xa;&#xa;属性:&#xa;- satellites_data: Dict[str, Dict[int, Dict]]&#xa;- ground_stations: Dict[str, GroundStation]&#xa;- config: Dict[str, Any]&#xa;- visibility_cache: Dict[Tuple[int, str, str], bool]&#xa;- coverage_cache: Dict[Tuple[int, str], Dict[str, float]]&#xa;- total_timeslots: int&#xa;&#xa;核心类:&#xa;class Satellite:&#xa;  + satellite_id: str&#xa;  + latitude: float&#xa;  + longitude: float&#xa;  + altitude: float = 550000  # 550km&#xa;  + illuminated: bool&#xa;  + timestamp: int&#xa;  + velocity_lat: float&#xa;  + velocity_lon: float&#xa;&#xa;class GroundStation:&#xa;  + station_id: str&#xa;  + latitude: float&#xa;  + longitude: float&#xa;  + region_type: str&#xa;  + size: str&#xa;  + purpose_type: str&#xa;&#xa;方法:&#xa;+ __init__(config_file: str)&#xa;+ get_satellites_at_time(time_step: int) -> Dict[str, Satellite]&#xa;+ calculate_velocity(satellite_id: str, time_step: int) -> Tuple[float, float]&#xa;+ build_inter_satellite_visibility_matrix(satellites) -> np.ndarray&#xa;+ build_satellite_ground_visibility_matrix(satellites) -> np.ndarray&#xa;+ get_ground_coverage(satellite: Satellite) -> Dict[str, float]&#xa;+ get_all_ground_coverage(satellites) -> Dict[str, Dict[str, float]]&#xa;+ get_total_timeslots() -> int&#xa;- _load_config(config_file: str) -> Dict[str, Any]&#xa;- _load_satellite_data() -> None&#xa;- _create_ground_stations() -> None&#xa;- calculate_satellite_visibility(sat1, sat2) -> bool&#xa;- calculate_ground_visibility(satellite, ground_station) -> bool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=9;fontStyle=1;align=left" vertex="1" parent="core-business-detailed-group">
          <mxGeometry x="50" y="40" width="500" height="250" as="geometry" />
        </mxCell>
        
        <!-- CommunicationManager超详细 -->
        <mxCell id="communication-detailed" value="CommunicationManager&#xa;通信管理模块 (超详细)&#xa;&#xa;属性:&#xa;- orbital_updater: OrbitalUpdater&#xa;- config: Dict[str, Any]&#xa;- link_cache: Dict[Tuple[int, str, str], LinkState]&#xa;- network_cache: Dict[int, NetworkState]&#xa;- signal_power_w: float = 10.0&#xa;- noise_power_w: float = 0.001&#xa;- visibility_threshold_km: float = 1000.0&#xa;&#xa;核心类:&#xa;class LinkState:&#xa;  + source_id: str&#xa;  + target_id: str&#xa;  + is_active: bool&#xa;  + distance_km: float&#xa;  + signal_strength_db: float&#xa;  + snr_db: float&#xa;  + data_rate_mbps: float&#xa;  + latency_ms: float&#xa;  + energy_cost_j_per_mb: float&#xa;  + link_quality: float  # 0-1&#xa;&#xa;class NetworkState:&#xa;  + timeslot: int&#xa;  + active_links: Dict[str, LinkState]&#xa;  + satellite_neighbors: Dict[str, List[str]]&#xa;  + ground_connections: Dict[str, List[str]]&#xa;  + network_topology: np.ndarray  # 36x36&#xa;  + total_active_links: int&#xa;  + average_link_quality: float&#xa;&#xa;方法:&#xa;+ __init__(orbital_updater: OrbitalUpdater, config: Dict)&#xa;+ get_network_state(time_step: int) -> NetworkState&#xa;+ calculate_link_properties(sat1, sat2, distance) -> LinkState&#xa;+ update_network_topology(time_step: int) -> None&#xa;+ get_satellite_neighbors(satellite_id: str, time_step: int) -> List[str]&#xa;+ get_ground_station_connections(satellite_id: str, time_step: int) -> List[str]&#xa;- _calculate_signal_strength(distance_km: float) -> float&#xa;- _calculate_snr(signal_strength: float) -> float&#xa;- _calculate_data_rate(snr_db: float) -> float&#xa;- _calculate_latency(distance_km: float) -> float&#xa;- _calculate_energy_cost(data_rate: float) -> float" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=9;fontStyle=1;align=left" vertex="1" parent="core-business-detailed-group">
          <mxGeometry x="580" y="40" width="500" height="250" as="geometry" />
        </mxCell>
        
        <!-- Task System超详细 -->
        <mxCell id="task-system-detailed" value="Task System&#xa;任务系统模块 (超详细)&#xa;&#xa;任务生成器 (TaskGenerator):&#xa;属性:&#xa;- ground_stations: Dict[str, GroundStation]&#xa;- task_data: Dict[int, List[Dict]]&#xa;- generation_rate: float = 0.1&#xa;- task_id_counter: int = 0&#xa;&#xa;class Task (Generated):&#xa;  + task_id: str&#xa;  + data_size_mb: float&#xa;  + computation_cycles: int&#xa;  + deadline_s: float&#xa;  + priority: int  # 1-5&#xa;  + source_location: str&#xa;  + target_location: str&#xa;  + creation_time: float&#xa;&#xa;任务管理器 (TaskManager):&#xa;属性:&#xa;- tasks: Dict[str, EnhancedTask]&#xa;- task_loader: TaskLoader&#xa;- performance_stats: Dict[str, Any]&#xa;&#xa;class EnhancedTask:&#xa;  + task_id: str&#xa;  + data_size_mb: float&#xa;  + computation_cycles: int&#xa;  + deadline_s: float&#xa;  + priority: TaskPriority&#xa;  + state: TaskState&#xa;  + source_location: str&#xa;  + target_location: str&#xa;  + creation_time: float&#xa;  + start_time: Optional[float]&#xa;  + completion_time: Optional[float]&#xa;  + assigned_satellite: Optional[str]&#xa;  + processing_satellite: Optional[str]&#xa;  + dynamic_priority: float&#xa;  + energy_cost_j: float&#xa;  + communication_cost_j: float&#xa;&#xa;枚举类:&#xa;class TaskState(Enum):&#xa;  PENDING = \"pending\"&#xa;  PROCESSING = \"processing\"&#xa;  COMPLETED = \"completed\"&#xa;  FAILED = \"failed\"&#xa;  EXPIRED = \"expired\"&#xa;&#xa;class TaskPriority(Enum):&#xa;  CRITICAL = 5&#xa;  HIGH = 4&#xa;  MEDIUM = 3&#xa;  LOW = 2&#xa;  BACKGROUND = 1&#xa;&#xa;方法:&#xa;+ generate_tasks_for_location(location_id, timeslot) -> List[Task]&#xa;+ load_tasks_from_json(filename) -> Dict[int, List[Task]]&#xa;+ calculate_dynamic_priority(task, current_time) -> float&#xa;+ update_task_state(task_id, new_state) -> bool&#xa;+ get_task_statistics() -> Dict[str, Any]" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=9;fontStyle=1;align=left" vertex="1" parent="core-business-detailed-group">
          <mxGeometry x="1110" y="40" width="500" height="250" as="geometry" />
        </mxCell>
        
        <!-- SatelliteNode超详细 -->
        <mxCell id="satellite-node-detailed" value="SatelliteNode&#xa;卫星实体模块 (超详细)&#xa;&#xa;属性:&#xa;- satellite_id: str&#xa;- status: SatelliteStatus&#xa;- position: Position&#xa;- energy_state: EnergyState&#xa;- resources: Dict[ResourceType, ResourceState]&#xa;- communication_state: CommunicationState&#xa;- task_queue: List[EnhancedTask]&#xa;- processing_tasks: List[EnhancedTask]&#xa;- completed_tasks: List[EnhancedTask]&#xa;- failed_tasks: List[EnhancedTask]&#xa;- performance_metrics: PerformanceMetrics&#xa;- config: Dict[str, Any]&#xa;- last_update_time: float&#xa;&#xa;核心数据结构:&#xa;class Position:&#xa;  + latitude: float&#xa;  + longitude: float&#xa;  + altitude: float&#xa;  + velocity_lat: float&#xa;  + velocity_lon: float&#xa;  + timestamp: float&#xa;&#xa;class EnergyState:&#xa;  + current_battery_j: float&#xa;  + battery_capacity_j: float&#xa;  + solar_power_w: float&#xa;  + is_illuminated: bool&#xa;  + energy_consumption_w: float&#xa;  + charging_rate_w: float&#xa;&#xa;class ResourceState:&#xa;  + resource_type: ResourceType&#xa;  + total_capacity: float&#xa;  + available_capacity: float&#xa;  + utilization_ratio: float&#xa;  + allocated_tasks: List[str]&#xa;&#xa;class CommunicationState:&#xa;  + neighbors: List[str]&#xa;  + active_links: Dict[str, LinkInfo]&#xa;  + ground_stations: List[str]&#xa;  + network_quality: float&#xa;  + total_bandwidth_mbps: float&#xa;  + available_bandwidth_mbps: float&#xa;&#xa;class PerformanceMetrics:&#xa;  + total_tasks_processed: int&#xa;  + total_energy_consumed_j: float&#xa;  + average_response_time_s: float&#xa;  + completion_rate: float&#xa;  + queue_length_history: List[int]&#xa;  + cpu_utilization_history: List[float]&#xa;&#xa;枚举类:&#xa;class SatelliteStatus(Enum):&#xa;  ACTIVE = \"active\"&#xa;  INACTIVE = \"inactive\"&#xa;  FAILED = \"failed\"&#xa;  MAINTENANCE = \"maintenance\"&#xa;&#xa;class ResourceType(Enum):&#xa;  CPU = \"cpu\"&#xa;  MEMORY = \"memory\"&#xa;  STORAGE = \"storage\"&#xa;  BANDWIDTH = \"bandwidth\"&#xa;&#xa;方法:&#xa;+ __init__(satellite_id, config)&#xa;+ step(time_step: int, time_delta: float) -> None&#xa;+ receive_task(task: EnhancedTask) -> bool&#xa;+ process_tasks() -> None&#xa;+ update_energy(time_delta: float) -> None&#xa;+ update_resources() -> None&#xa;+ execute_task_offload(task_id: str, target_satellite: str) -> bool&#xa;+ sync_with_orbital_state(orbital_satellite) -> None&#xa;+ sync_communication_state(network_state) -> None&#xa;+ export_observation_data() -> Dict[str, Any]&#xa;+ export_for_scheduling() -> Dict[str, Any]&#xa;+ get_status_summary() -> Dict[str, Any]&#xa;+ cleanup_expired_tasks() -> None&#xa;+ is_healthy() -> bool&#xa;+ can_process_tasks() -> bool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=9;fontStyle=1;align=left" vertex="1" parent="core-business-detailed-group">
          <mxGeometry x="1640" y="40" width="500" height="250" as="geometry" />
        </mxCell>
        
        <!-- 数据流和接口层 -->
        <mxCell id="data-flow-interface-group" value="数据流和接口层 (详细)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="940" width="2700" height="200" as="geometry" />
        </mxCell>
        
        <!-- 主要数据流 -->
        <mxCell id="main-data-flows" value="主要数据流向&#xa;&#xa;1. 轨道数据流:&#xa;   CSV → OrbitalUpdater.satellites_data → Satellite对象 → SatelliteAdapter → SatelliteNode.position&#xa;&#xa;2. 通信数据流:&#xa;   可见性矩阵 → CommunicationManager → LinkState/NetworkState → SatelliteNode.communication_state&#xa;&#xa;3. 任务数据流:&#xa;   JSON → TaskGenerator → Task对象 → TaskAdapter → EnhancedTask → SatelliteNode.task_queue&#xa;&#xa;4. 能量数据流:&#xa;   光照状态 → EnergyState.is_illuminated → 太阳能充电 → 电池状态更新&#xa;&#xa;5. 资源数据流:&#xa;   任务需求 → ResourceState.allocation → CPU/内存分配 → 任务处理&#xa;&#xa;6. 观测数据流:&#xa;   SatelliteNode状态 → export_observation_data() → 强化学习接口&#xa;&#xa;7. 调度数据流:&#xa;   系统状态 → export_for_scheduling() → 调度算法 → 任务分配决策" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10;fontStyle=1;align=left" vertex="1" parent="data-flow-interface-group">
          <mxGeometry x="50" y="40" width="600" height="150" as="geometry" />
        </mxCell>
        
        <!-- 性能指标和缓存机制 -->
        <mxCell id="performance-cache" value="性能指标和缓存机制&#xa;&#xa;缓存策略:&#xa;- 轨道状态缓存: LRU(100个时隙)&#xa;- 可见性缓存: 时间窗口缓存(10个时隙)&#xa;- 通信链路缓存: 距离阈值缓存&#xa;- 任务转换缓存: 最近1000个任务&#xa;&#xa;性能指标:&#xa;- 内存使用: ~200MB (36卫星×1000时隙)&#xa;- CPU使用: ~15% (Intel i7-8700K)&#xa;- 磁盘I/O: ~50MB/s (数据加载)&#xa;- 网络延迟: 0.1-50ms (卫星间)&#xa;- 任务处理延迟: 1-100s&#xa;- 系统吞吐量: ~1000任务/小时&#xa;&#xa;优化机制:&#xa;- 矩阵运算向量化 (NumPy)&#xa;- 并行计算 (多进程池)&#xa;- 内存映射文件 (mmap)&#xa;- 增量状态更新&#xa;- 延迟计算 (Lazy Evaluation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10;fontStyle=1;align=left" vertex="1" parent="data-flow-interface-group">
          <mxGeometry x="680" y="40" width="400" height="150" as="geometry" />
        </mxCell>
        
        <!-- 接口定义 -->
        <mxCell id="interface-definitions" value="接口定义 (详细)&#xa;&#xa;强化学习接口:&#xa;- 观测空间: Box(shape=(1024,), dtype=float32)&#xa;  * 卫星状态: 位置(3) + 能量(4) + 资源(8) = 15维&#xa;  * 任务队列: 队列长度(1) + 任务特征(20×10) = 201维&#xa;  * 通信状态: 邻居数(1) + 链路质量(35) = 36维&#xa;  * 总计: 36卫星 × (15+201+36) = 9072维 → 压缩到1024维&#xa;&#xa;- 动作空间: MultiDiscrete([100, 36, 5])&#xa;  * 任务选择: 0-99 (队列中的任务索引)&#xa;  * 目标卫星: 0-35 (卫星索引)&#xa;  * 动作类型: 0-4 (处理/卸载/等待/丢弃/优先级调整)&#xa;&#xa;- 奖励函数: R = w1×完成率 + w2×能效 + w3×延迟 - w4×失败率&#xa;  * w1=0.4, w2=0.3, w3=0.2, w4=0.1&#xa;&#xa;调度算法接口:&#xa;- 输入: 系统状态快照 (JSON格式, ~50KB)&#xa;- 输出: 调度决策列表 (任务ID→卫星ID映射)&#xa;- 调用频率: 每个时隙1次&#xa;- 响应时间: <100ms&#xa;&#xa;监控接口:&#xa;- 实时指标: WebSocket推送, 1Hz更新频率&#xa;- 历史数据: REST API, 支持时间范围查询&#xa;- 告警机制: 阈值触发, 邮件/短信通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10;fontStyle=1;align=left" vertex="1" parent="data-flow-interface-group">
          <mxGeometry x="1110" y="40" width="500" height="150" as="geometry" />
        </mxCell>
        
        <!-- 错误处理和容错机制 -->
        <mxCell id="error-handling" value="错误处理和容错机制&#xa;&#xa;异常类型:&#xa;- ConfigurationError: 配置文件错误&#xa;- DataLoadError: 数据文件加载失败&#xa;- SatelliteFailureError: 卫星故障&#xa;- TaskTimeoutError: 任务超时&#xa;- CommunicationError: 通信链路中断&#xa;- ResourceExhaustionError: 资源耗尽&#xa;&#xa;容错策略:&#xa;- 卫星故障: 自动切换到备用卫星&#xa;- 任务失败: 重试机制(最多3次)&#xa;- 通信中断: 缓存转发机制&#xa;- 数据损坏: 校验和验证&#xa;- 内存不足: 自动垃圾回收&#xa;&#xa;恢复机制:&#xa;- 检查点保存: 每100个时隙&#xa;- 状态回滚: 支持回退到上一个稳定状态&#xa;- 热备份: 关键数据实时备份&#xa;- 故障隔离: 单个卫星故障不影响系统&#xa;&#xa;监控告警:&#xa;- 健康检查: 每个时隙检查系统状态&#xa;- 性能监控: CPU/内存/磁盘使用率&#xa;- 业务监控: 任务完成率/延迟/错误率&#xa;- 日志记录: 结构化日志, 支持ELK分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10;fontStyle=1;align=left" vertex="1" parent="data-flow-interface-group">
          <mxGeometry x="1640" y="40" width="400" height="150" as="geometry" />
        </mxCell>
        
        <!-- 时序执行详细流程 -->
        <mxCell id="timing-execution-group" value="时序执行详细流程" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#8c4a00;startSize=30;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="1170" width="2700" height="250" as="geometry" />
        </mxCell>
        
        <mxCell id="detailed-timing" value="详细时序执行流程 (每个时隙)&#xa;&#xa;时间: 0ms - 系统初始化&#xa;├── 加载配置文件 (config.yaml) [5ms]&#xa;├── 初始化OrbitalUpdater [20ms]&#xa;│   ├── 加载卫星轨道数据 (satellite_processed_data.csv) [15ms]&#xa;│   └── 创建地面站对象 (updated_global_ground_stations.csv) [5ms]&#xa;├── 初始化CommunicationManager [10ms]&#xa;├── 初始化TaskGenerator [5ms]&#xa;└── 创建IntegrationManager [5ms]&#xa;&#xa;时间: 45ms - 开始仿真主循环&#xa;for timeslot in range(1000):  # 每个时隙约12ms&#xa;    ├── [0-2ms] 卫星状态同步&#xa;    │   ├── OrbitalUpdater.get_satellites_at_time(timeslot) [0.5ms]&#xa;    │   ├── 计算卫星速度 (36个卫星) [0.3ms]&#xa;    │   ├── CommunicationManager.get_network_state(timeslot) [0.8ms]&#xa;    │   │   ├── 构建可见性矩阵 (36×36) [0.3ms]&#xa;    │   │   ├── 计算链路属性 (~200个活跃链路) [0.4ms]&#xa;    │   │   └── 更新网络拓扑 [0.1ms]&#xa;    │   └── SatelliteAdapter.sync_all_satellites() [0.4ms]&#xa;    │       ├── 更新36个SatelliteNode位置 [0.2ms]&#xa;    │       └── 同步通信状态 [0.2ms]&#xa;    │&#xa;    ├── [2-3ms] 任务生成和转换&#xa;    │   ├── TaskGenerator.generate_tasks_for_timeslot() [0.3ms]&#xa;    │   │   └── 平均生成4.2个任务 (泊松分布, λ=0.1×42地面站)&#xa;    │   └── TaskAdapter.batch_convert_tasks() [0.7ms]&#xa;    │       ├── 转换为EnhancedTask对象 [0.4ms]&#xa;    │       └── 计算动态优先级 [0.3ms]&#xa;    │&#xa;    ├── [3-6ms] 任务分配&#xa;    │   ├── IntegrationManager._assign_tasks_to_satellites() [2.5ms]&#xa;    │   │   ├── 遍历新任务 (平均4.2个) [0.1ms]&#xa;    │   │   ├── _find_best_satellite_for_task() [2.0ms]&#xa;    │   │   │   ├── 计算所有卫星的适应度分数 (36个) [1.5ms]&#xa;    │   │   │   │   ├── 距离因子 (基于位置) [0.5ms]&#xa;    │   │   │   │   ├── 资源可用性因子 [0.4ms]&#xa;    │   │   │   │   ├── 能量状态因子 [0.3ms]&#xa;    │   │   │   │   └── 队列长度因子 [0.3ms]&#xa;    │   │   │   └── 选择最优卫星 [0.5ms]&#xa;    │   │   └── 任务分发到选定卫星 [0.4ms]&#xa;    │   └── 更新任务分配统计 [0.5ms]&#xa;    │&#xa;    ├── [6-11ms] 卫星节点执行&#xa;    │   └── 并行执行36个SatelliteNode.step() [5ms]&#xa;    │       ├── process_tasks() [2.5ms]&#xa;    │       │   ├── 任务队列排序 (按优先级) [0.3ms]&#xa;    │       │   ├── 资源分配检查 [0.4ms]&#xa;    │       │   ├── 执行任务处理 (平均2.1个任务/卫星) [1.5ms]&#xa;    │       │   └── 更新任务状态 [0.3ms]&#xa;    │       ├── update_energy() [1.2ms]&#xa;    │       │   ├── 计算太阳能充电 (基于光照状态) [0.4ms]&#xa;    │       │   ├── 计算能量消耗 (CPU+通信) [0.5ms]&#xa;    │       │   └── 更新电池状态 [0.3ms]&#xa;    │       ├── update_resources() [0.8ms]&#xa;    │       │   ├── CPU利用率更新 [0.3ms]&#xa;    │       │   ├── 内存使用更新 [0.2ms]&#xa;    │       │   └── 存储空间更新 [0.3ms]&#xa;    │       └── cleanup_expired_tasks() [0.5ms]&#xa;    │&#xa;    └── [11-12ms] 统计信息更新&#xa;        ├── _update_simulation_stats() [0.8ms]&#xa;        │   ├── 收集任务完成统计 [0.3ms]&#xa;        │   ├── 计算能量消耗统计 [0.2ms]&#xa;        │   └── 更新性能指标 [0.3ms]&#xa;        └── 进度显示和日志记录 [0.2ms]&#xa;&#xa;时间: 12045ms (12.045s) - 仿真完成&#xa;├── export_results() [100ms]&#xa;│   ├── 收集所有卫星状态 [30ms]&#xa;│   ├── 生成统计报告 [40ms]&#xa;│   └── 保存到JSON文件 [30ms]&#xa;└── 清理资源和退出 [10ms]&#xa;&#xa;总执行时间: ~12.2秒 (1000时隙)&#xa;平均每时隙: 12.2ms&#xa;峰值内存使用: ~250MB&#xa;平均CPU使用率: 18%&#xa;磁盘I/O: 读取~50MB, 写入~10MB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;fontColor=#000000;strokeColor=#d6b656;fontSize=9;fontStyle=1;align=left" vertex="1" parent="timing-execution-group">
          <mxGeometry x="50" y="40" width="2600" height="200" as="geometry" />
        </mxCell>
        
        <!-- 主要连接线 - 超详细调用关系 -->
        <!-- 系统入口到集成管理器 -->
        <mxCell id="main-to-integration-detailed" value="创建实例&#xa;传递config_file路径" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=10;fontStyle=1" edge="1" parent="1" source="main-entry" target="integration-manager-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 配置文件到各模块 -->
        <mxCell id="config-to-integration-detailed" value="系统配置加载&#xa;_load_config()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7030a0;dashed=1;fontSize=10" edge="1" parent="1" source="config-yaml-detailed" target="integration-manager-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 数据文件到轨道更新器 -->
        <mxCell id="satellite-data-to-orbital-detailed" value="轨道数据加载&#xa;_load_satellite_data()&#xa;pandas.read_csv()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-data-detailed" target="orbital-updater-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="ground-data-to-orbital-detailed" value="地面站数据加载&#xa;_create_ground_stations()&#xa;创建420个GroundStation对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="ground-stations-detailed" target="orbital-updater-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 集成管理器到适配器 -->
        <mxCell id="integration-to-satellite-adapter-detailed" value="卫星状态同步调用&#xa;sync_all_satellites(timeslot)&#xa;返回Dict[str, SatelliteNode]" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager-detailed" target="satellite-adapter-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-to-task-adapter-detailed" value="任务生成调用&#xa;generate_tasks_for_timeslot(timeslot)&#xa;返回List[EnhancedTask]" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager-detailed" target="task-adapter-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器到核心模块 -->
        <mxCell id="satellite-adapter-to-orbital-detailed" value="轨道数据获取&#xa;get_satellites_at_time(time_step)&#xa;calculate_velocity()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter-detailed" target="orbital-updater-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-comm-detailed" value="通信状态获取&#xa;get_network_state(time_step)&#xa;返回NetworkState对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter-detailed" target="communication-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-satellite-detailed" value="卫星节点创建/更新&#xa;create_satellite_node()&#xa;update_satellite_node()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter-detailed" target="satellite-node-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-task-detailed" value="任务转换&#xa;convert_generated_task()&#xa;batch_convert_tasks()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="task-adapter-detailed" target="task-system-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 核心模块间交互 -->
        <mxCell id="orbital-to-comm-detailed" value="可见性矩阵传递&#xa;build_inter_satellite_visibility_matrix()&#xa;build_satellite_ground_visibility_matrix()" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="orbital-updater-detailed" target="communication-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 任务数据到任务系统 -->
        <mxCell id="task-data-to-task-detailed" value="任务数据加载&#xa;load_tasks_from_json()&#xa;解析JSON格式任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-data-detailed" target="task-system-detailed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 图例详细 -->
        <mxCell id="legend-detailed-group" value="图例说明 (详细)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="1450" width="400" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-main-control" value="粗绿线 (4px): 主控制流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="30" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-module-call" value="粗黑线 (3px): 模块间调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="50" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-data-transfer" value="蓝色实线 (2px): 数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="70" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-file-load" value="灰色虚线 (2px): 文件加载" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="90" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-config-load" value="紫色虚线 (2px): 配置加载" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="110" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-colors" value="颜色说明:&#xa;红色六边形: 系统入口&#xa;紫色平行四边形: 配置文件&#xa;黄色平行四边形: 数据文件&#xa;深蓝色矩形: 核心模块&#xa;橙色圆角矩形: 适配器&#xa;绿色圆角矩形: 数据结构/接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-detailed-group">
          <mxGeometry x="10" y="130" width="250" height="60" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>