"""
GAT性能测试 - 验证修改效果
测试修改前后的性能差异
"""

import torch
import time
import numpy as np
from typing import Dict, List, Tuple
import logging
import sys
import os

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from graph_attention import GraphAttentionLayer, GraphAttentionCoordination, CoordinationManager, LinkInfo

logger = logging.getLogger(__name__)


class GATPerformanceTester:
    """图注意力网络性能测试器"""
    
    def __init__(self, num_satellites: int = 36):
        self.num_satellites = num_satellites
        self.test_results = {}
        
    def generate_test_data(self) -> Tu<PERSON>[torch.Tensor, torch.Tensor, torch.Tensor]:
        """生成测试数据"""
        node_features = torch.randn(self.num_satellites, 15)  # 15维节点特征
        edge_features = torch.randn(self.num_satellites, self.num_satellites, 5)  # 5维边特征
        
        # 生成稀疏邻接矩阵（模拟卫星通信链路）
        adj_matrix = torch.zeros(self.num_satellites, self.num_satellites)
        # 每个卫星平均连接5-8个邻居
        for i in range(self.num_satellites):
            num_neighbors = torch.randint(5, 9, (1,)).item()
            neighbors = torch.randperm(self.num_satellites)[:num_neighbors]
            for j in neighbors:
                if i != j:
                    adj_matrix[i, j] = 1
                    adj_matrix[j, i] = 1
        
        return node_features, edge_features, adj_matrix
    
    def generate_pyg_test_data(self) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """生成PyG格式测试数据"""
        node_features = torch.randn(self.num_satellites, 15)
        
        # 生成边索引和边特征
        edge_list = []
        edge_attrs = []
        
        for i in range(self.num_satellites):
            num_neighbors = torch.randint(5, 9, (1,)).item()
            neighbors = torch.randperm(self.num_satellites)[:num_neighbors]
            for j in neighbors:
                if i != j:
                    edge_list.extend([[i, j], [j, i]])
                    edge_attrs.extend([torch.randn(5), torch.randn(5)])
        
        edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
        edge_attr = torch.stack(edge_attrs)
        
        return node_features, edge_index, edge_attr
    
    def test_gat_layer_performance(self, num_iterations: int = 100) -> Dict[str, float]:
        """测试GAT层性能"""
        logger.info(f"Testing GAT Layer performance with {self.num_satellites} satellites")
        
        # 准备测试数据
        node_features, edge_features, adj_matrix = self.generate_test_data()
        node_features_pyg, edge_index, edge_attr = self.generate_pyg_test_data()
        
        # 创建GAT层
        gat_layer = GraphAttentionLayer(
            in_features=15,
            out_features=128,
            heads=4,
            edge_features=5
        )
        
        results = {}
        
        # 测试传统实现（回退模式）
        if hasattr(gat_layer, '_forward_fallback'):
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            start_time = time.time()
            for _ in range(num_iterations):
                with torch.no_grad():
                    output = gat_layer._forward_fallback(node_features, edge_features, adj_matrix)
            end_time = time.time()
            
            results['fallback_time'] = (end_time - start_time) / num_iterations
            results['fallback_output_shape'] = output.shape
        
        # 测试高效实现（PyG模式）
        if gat_layer.use_efficient:
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            start_time = time.time()
            for _ in range(num_iterations):
                with torch.no_grad():
                    output = gat_layer(node_features_pyg, edge_index=edge_index, edge_attr=edge_attr)
            end_time = time.time()
            
            results['efficient_time'] = (end_time - start_time) / num_iterations
            results['efficient_output_shape'] = output.shape
        
        # 计算性能提升
        if 'fallback_time' in results and 'efficient_time' in results:
            speedup = results['fallback_time'] / results['efficient_time']
            results['speedup_ratio'] = speedup
            logger.info(f"Performance improvement: {speedup:.2f}x faster")
        
        return results
    
    def test_coordination_manager_performance(self, num_iterations: int = 50) -> Dict[str, float]:
        """测试协同管理器性能"""
        logger.info(f"Testing CoordinationManager performance")
        
        # 创建协同管理器
        manager = CoordinationManager(num_satellites=self.num_satellites)
        
        # 生成测试数据
        satellite_states = {}
        communication_links = {}
        
        for i in range(self.num_satellites):
            satellite_states[i] = {
                'observation': np.random.randn(15),
                'queue_length': np.random.randint(0, 10),
                'energy_level': np.random.random()
            }
        
        # 生成通信链路
        for i in range(self.num_satellites):
            for j in range(i+1, self.num_satellites):
                if np.random.random() < 0.3:  # 30%的链路连接概率
                    link_info = LinkInfo(
                        source_id=i,
                        target_id=j,
                        distance_km=np.random.uniform(500, 2000),
                        data_rate_mbps=np.random.uniform(50, 100),
                        delay_ms=np.random.uniform(5, 50),
                        energy_cost_j=np.random.uniform(0.1, 1.0),
                        link_quality=np.random.uniform(0.6, 1.0)
                    )
                    communication_links[(i, j)] = link_info
        
        results = {}
        
        # 测试协同决策性能
        start_time = time.time()
        for _ in range(num_iterations):
            decisions = manager.coordinate_satellites(satellite_states, communication_links)
        end_time = time.time()
        
        results['coordination_time'] = (end_time - start_time) / num_iterations
        results['decisions_per_iteration'] = len(decisions) if decisions else 0
        results['total_satellites'] = self.num_satellites
        results['total_links'] = len(communication_links)
        
        logger.info(f"Coordination time per iteration: {results['coordination_time']:.4f}s")
        
        return results
    
    def test_memory_usage(self) -> Dict[str, float]:
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 测试前内存
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大规模测试
        results = self.test_gat_layer_performance(num_iterations=10)
        
        # 测试后内存
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'memory_before_mb': memory_before,
            'memory_after_mb': memory_after,
            'memory_usage_mb': memory_after - memory_before
        }
    
    def run_comprehensive_test(self) -> Dict[str, any]:
        """运行综合性能测试"""
        logger.info("Starting comprehensive GAT performance test")
        
        comprehensive_results = {
            'test_config': {
                'num_satellites': self.num_satellites,
                'timestamp': time.strftime('%Y%m%d_%H%M%S')
            }
        }
        
        # GAT层性能测试
        try:
            gat_results = self.test_gat_layer_performance()
            comprehensive_results['gat_layer_performance'] = gat_results
        except Exception as e:
            logger.error(f"GAT layer test failed: {e}")
            comprehensive_results['gat_layer_performance'] = {'error': str(e)}
        
        # 协同管理器性能测试
        try:
            manager_results = self.test_coordination_manager_performance()
            comprehensive_results['coordination_manager_performance'] = manager_results
        except Exception as e:
            logger.error(f"Coordination manager test failed: {e}")
            comprehensive_results['coordination_manager_performance'] = {'error': str(e)}
        
        # 内存使用测试
        try:
            memory_results = self.test_memory_usage()
            comprehensive_results['memory_usage'] = memory_results
        except Exception as e:
            logger.error(f"Memory usage test failed: {e}")
            comprehensive_results['memory_usage'] = {'error': str(e)}
        
        # 生成性能报告
        self._generate_performance_report(comprehensive_results)
        
        return comprehensive_results
    
    def _generate_performance_report(self, results: Dict) -> None:
        """生成性能测试报告"""
        logger.info("=== GAT Performance Test Report ===")
        
        # GAT层性能
        if 'gat_layer_performance' in results:
            gat_perf = results['gat_layer_performance']
            if 'speedup_ratio' in gat_perf:
                logger.info(f"GAT Layer Speedup: {gat_perf['speedup_ratio']:.2f}x")
            if 'efficient_time' in gat_perf:
                logger.info(f"Efficient GAT Time: {gat_perf['efficient_time']:.4f}s per forward pass")
            if 'fallback_time' in gat_perf:
                logger.info(f"Fallback GAT Time: {gat_perf['fallback_time']:.4f}s per forward pass")
        
        # 协同管理器性能
        if 'coordination_manager_performance' in results:
            coord_perf = results['coordination_manager_performance']
            if 'coordination_time' in coord_perf:
                logger.info(f"Coordination Time: {coord_perf['coordination_time']:.4f}s per iteration")
        
        # 内存使用
        if 'memory_usage' in results:
            mem_usage = results['memory_usage']
            if 'memory_usage_mb' in mem_usage:
                logger.info(f"Memory Usage: {mem_usage['memory_usage_mb']:.2f}MB")
        
        logger.info("=== End of Report ===")


def main():
    """主测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 测试不同规模
    test_sizes = [36, 72, 108]  # 36, 72, 108个卫星
    
    all_results = {}
    
    for size in test_sizes:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing with {size} satellites")
        logger.info(f"{'='*50}")
        
        tester = GATPerformanceTester(num_satellites=size)
        results = tester.run_comprehensive_test()
        all_results[f'satellites_{size}'] = results
    
    # 保存所有结果
    import json
    import os
    
    os.makedirs('../../../results/performance', exist_ok=True)
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    with open(f'../../../results/performance/gat_performance_{timestamp}.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    logger.info(f"Performance test completed. Results saved to results/performance/gat_performance_{timestamp}.json")


if __name__ == "__main__":
    main()