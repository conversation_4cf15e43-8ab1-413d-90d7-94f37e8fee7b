import math
import numpy as np
from typing import Dict, List, Tuple, Optional
import yaml
import logging

# 导入orbital_updater模块的类
try:
    from .orbital_updater import Satellite, GroundStation, OrbitalUpdater
except ImportError:
    from orbital_updater import Satellite, GroundStation, OrbitalUpdater


class CommunicationManager:
    """通信管理器 - 基于orbital_updater的可见性矩阵进行链路状态计算"""
    
    def __init__(self, config_file: str = "src/env/config.yaml"):
        """
        初始化通信管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.orbital_updater = None  # 将在外部设置
        
        # 从配置文件读取通信参数
        comm_config = self.config['communication']
        
        # 基础物理参数
        self.light_speed = comm_config['light_speed_ms']  # 光速 (m/s)
        self.frequency = comm_config['rf_carrier_freq_hz']  # 频率 (Hz)
        
        # 发射功率参数
        self.user_transmit_power = comm_config['p_u_w']  # 用户发射功率 (W)
        self.satellite_to_user_power = comm_config['p_su_w']  # 卫星对用户发射功率 (W)
        self.satellite_to_cloud_power = comm_config['p_sc_w']  # 卫星对云发射功率 (W)
        self.cloud_transmit_power = comm_config['p_c_w']  # 云中心发射功率 (W)
        
        # 带宽参数
        self.user_to_satellite_bandwidth = comm_config['b_us_hz']  # 用户-卫星上行带宽 (Hz)
        self.satellite_to_user_bandwidth = comm_config['b_su_hz']  # 卫星-用户下行带宽 (Hz)
        self.satellite_to_cloud_bandwidth = comm_config['b_sc_hz']  # 卫星-云下行带宽 (Hz)
        self.cloud_to_satellite_bandwidth = comm_config['b_cs_hz']  # 云-卫星上行带宽 (Hz)
        self.isl_data_rate = comm_config['isl_tra_rate']  # 星间链路速率 (bps)
        
        # 链路预算参数
        self.antenna_gain = comm_config['antenna_gain_db']  # 天线增益 (dB)
        self.system_noise = comm_config['system_noise_dbm_hz']  # 系统噪声 (dBm/Hz)
        self.implementation_loss = comm_config['implementation_loss_db']  # 实现损耗 (dB)
        self.coding_efficiency = comm_config['coding_efficiency']  # 编码效率
        
        # 处理延迟
        self.processing_delay = comm_config['processing_delay_ms']  # ms
        
    def _load_config(self, config_file: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.critical(f"致命错误：加载通信配置文件失败: {config_file} - {e}")
            raise
    
    def set_orbital_updater(self, orbital_updater: 'OrbitalUpdater'):
        """设置轨道更新器"""
        self.orbital_updater = orbital_updater
    
    def calculate_channel_gain(self, distance: float) -> float:
        """
        计算信道增益 |h|² - 根据Paper公式 L_pl = (c/(4πd*f))²
        
        Args:
            distance: 距离 (km)
            
        Returns:
            float: 信道增益 (线性值)
        """
        if distance <= 0:
            return 0.0
            
        distance_m = distance * 1000  # 转换为米
        # Paper中的路径损耗公式: L_pl = (c/(4πd*f))²
        path_loss_linear = (self.light_speed / (4 * math.pi * distance_m * self.frequency)) ** 2
        
        # 信道增益包括路径损耗和天线增益
        antenna_gain_linear = 10 ** (self.antenna_gain / 10)
        channel_gain = path_loss_linear * antenna_gain_linear
        
        return channel_gain
    
    def calculate_signal_strength_and_snr(self, distance: float, power: float, bandwidth: float, interference_power: float = 0.0) -> Tuple[float, float]:
        """
        计算信号强度和信噪比
        
        Args:
            distance: 距离 (km)
            power: 发射功率 (W)
            bandwidth: 带宽 (Hz)
            interference_power: 干扰功率 (W)
            
        Returns:
            Tuple[float, float]: (信号强度 dBm, 信噪比 dB)
        """
        if distance <= 0:
            return 0.0, 0.0
        
        # 使用Paper中的信道增益计算
        channel_gain = self.calculate_channel_gain(distance)
        
        # 接收功率 = 发射功率 × 信道增益
        received_power = power * channel_gain
        received_power_dbm = 10 * math.log10(received_power * 1000) if received_power > 0 else -float('inf')
        
        # 噪声功率 (W)
        noise_power_dbm_hz = self.system_noise
        noise_power_w = (10 ** (noise_power_dbm_hz / 10)) * 1e-3 * bandwidth
        
        # 总干扰和噪声功率
        total_interference_noise = interference_power + noise_power_w
        
        # 信噪比计算
        if total_interference_noise > 0 and received_power > 0:
            snr_linear = received_power / total_interference_noise
            snr_db = 10 * math.log10(snr_linear)
        else:
            snr_db = -float('inf')
        
        return received_power_dbm, snr_db
    
    def calculate_data_rate(self, distance: float, power: float, bandwidth: float = None, interference_power: float = 0.0) -> float:
        """
        计算链路数据传输速率 - 根据Paper公式 R = B*log2(1 + P*|h|²/(干扰+噪声))
        
        Args:
            distance: 距离 (km)
            power: 发射功率 (W)
            bandwidth: 带宽 (Hz), 如果为None则使用默认下行带宽
            interference_power: 同频干扰功率 (W)
            
        Returns:
            float: 数据传输速率 (Mbps)
        """
        if distance <= 0:
            return 0.0
        
        if bandwidth is None:
            bandwidth = self.satellite_to_user_bandwidth
            
        _, snr_db = self.calculate_signal_strength_and_snr(distance, power, bandwidth, interference_power)
        
        # 使用Paper中的香农公式计算数据速率
        if snr_db == -float('inf'):
            return 0.0
            
        snr_linear = 10 ** (snr_db / 10)
        capacity_bps = bandwidth * math.log2(1 + snr_linear)
        capacity_mbps = capacity_bps / 1e6
        
        return max(0, capacity_mbps * self.coding_efficiency)
    
    def calculate_transmission_delay(self, distance: float, data_size: float, data_rate: float = None) -> float:
        """
        计算传输延迟 - 根据Paper公式: T_comm = T_tx + T_prop
        
        Args:
            distance: 距离 (km)
            data_size: 数据大小 (MB)
            data_rate: 数据速率 (Mbps), 如果为None则计算
            
        Returns:
            float: 传输延迟 (ms)
        """
        # 传播延迟: T_prop = d/c (Paper公式)
        propagation_delay = (distance * 1000) / self.light_speed * 1000  # ms
        
        # 计算数据速率
        if data_rate is None:
            data_rate = self.calculate_data_rate(distance, self.satellite_to_user_power)
        
        # 传输延迟: T_tx = S_k / R_us (Paper公式)
        if data_rate <= 0:
            return float('inf')
        
        # 修复单位错误：(data_size * 8) / data_rate 的结果是秒，需要转换为毫秒
        transmission_time_s = (data_size * 8) / data_rate  # 秒
        transmission_delay_ms = transmission_time_s * 1000  # 转换为毫秒
        
        # 总通信延迟: T_comm = T_tx + T_prop (Paper公式)
        total_delay = propagation_delay + transmission_delay_ms
        
        return total_delay
    
    def calculate_transmission_energy(self, distance: float, data_size: float, power: float, data_rate: float = None) -> float:
        """
        计算传输能耗 - 根据Paper公式: E_comm = P * T_tx = P * (S_k / R_us)
        
        Args:
            distance: 距离 (km)
            data_size: 数据大小 (MB)
            power: 发射功率 (W) - 根据Paper使用固定功率
            data_rate: 数据速率 (Mbps), 如果为None则计算
            
        Returns:
            float: 传输能耗 (J)
        """
        # 计算数据速率
        if data_rate is None:
            data_rate = self.calculate_data_rate(distance, power)
        
        if data_rate <= 0:
            return float('inf')
        
        # 修复单位错误：计算传输时间（秒）用于能耗计算
        transmission_time_s = (data_size * 8) / data_rate  # 正确的传输时间（秒）
        
        # 计算能耗: E_comm = P * T_tx (Paper公式，使用固定功率)
        energy = power * transmission_time_s
        
        return energy
    
    def _compute_link_state(self, source_id: str, target_id: str, distance: float, 
                           power: float, bandwidth: float, link_type: str) -> Dict[str, float]:
        """
        计算单个链路状态的辅助函数，消除重复代码
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
            distance: 距离 (km)
            power: 发射功率 (W)
            bandwidth: 带宽 (Hz)
            link_type: 链路类型
            
        Returns:
            Dict[str, float]: 链路状态字典
        """
        # 星间链路使用固定参数
        if link_type == 'inter_satellite':
            data_rate = self.isl_data_rate / 1e6  # 转换为Mbps
            signal_strength = 0.0  # 激光通信信号强度稳定
            snr = 30.0  # dB, 激光通信信噪比高
        else:
            # 其他链路使用动态计算
            data_rate = self.calculate_data_rate(distance, power, bandwidth)
            signal_strength, snr = self.calculate_signal_strength_and_snr(distance, power, bandwidth)
        
        return {
            'distance_km': distance,
            'data_rate_mbps': data_rate,
            'transmission_delay_ms': self.calculate_transmission_delay(distance, 1.0, data_rate),
            'transmission_energy_j': self.calculate_transmission_energy(distance, 1.0, power, data_rate),
            'signal_strength_dbm': signal_strength,
            'snr_db': snr,
            'link_type': link_type
        }
    
    def get_all_link_states(self, time_step: int) -> Dict[Tuple[str, str], Dict[str, float]]:
        """
        获取所有链路状态 - 缓存优化版本
        
        Args:
            time_step: 时间步索引
            
        Returns:
            Dict: {(source_id, target_id): {distance, data_rate, delay, energy, signal_strength, snr}}
        """
        # 性能优化：检查缓存
        cache_key = f"all_links_{time_step}"
        if hasattr(self, '_link_cache') and cache_key in self._link_cache:
            return self._link_cache[cache_key]
        
        if not hasattr(self, '_link_cache'):
            self._link_cache = {}
            
        if self.orbital_updater is None:
            return {}
            
        # 获取卫星和地面站数据
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        if not satellites:
            return {}
            
        ground_stations = self.orbital_updater.ground_stations
        cloud_stations = self.orbital_updater.cloud_stations
        
        # 获取可见性矩阵 - 修复：传递time_step参数以利用缓存
        inter_satellite_matrix = self.orbital_updater.build_inter_satellite_visibility_matrix(satellites, time_step)
        satellite_ground_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(satellites, time_step)
        satellite_cloud_matrix = self.orbital_updater.build_satellite_cloud_visibility_matrix(satellites, time_step)
        
        link_states = {}
        satellite_list = list(satellites.values())
        ground_station_list = list(ground_stations.values())
        
        # 卫星间链路
        for i, sat1 in enumerate(satellite_list):
            for j, sat2 in enumerate(satellite_list):
                if i != j and inter_satellite_matrix[i, j]:
                    distance = self.orbital_updater._calculate_distance(
                        sat1.latitude, sat1.longitude,
                        sat2.latitude, sat2.longitude,
                        self.orbital_updater.satellite_altitude,
                        self.orbital_updater.satellite_altitude
                    )
                    
                    # 使用辅助函数计算链路状态
                    link_states[(sat1.satellite_id, sat2.satellite_id)] = self._compute_link_state(
                        sat1.satellite_id, sat2.satellite_id, distance,
                        self.satellite_to_user_power, None, 'inter_satellite'
                    )
        
        # 用户-卫星双向链路（消除重复距离计算）
        for i, satellite in enumerate(satellite_list):
            for j, ground_station in enumerate(ground_station_list):
                if satellite_ground_matrix[i, j]:
                    # 只计算一次距离，双向链路复用
                    distance = self.orbital_updater._calculate_distance(
                        ground_station.latitude, ground_station.longitude,
                        satellite.latitude, satellite.longitude,
                        0, self.orbital_updater.satellite_altitude
                    )
                    
                    # 用户到卫星链路 (上行)
                    link_states[(ground_station.station_id, satellite.satellite_id)] = self._compute_link_state(
                        ground_station.station_id, satellite.satellite_id, distance,
                        self.user_transmit_power, self.user_to_satellite_bandwidth, 'user_to_satellite'
                    )
                    
                    # 卫星到用户链路 (下行)
                    link_states[(satellite.satellite_id, ground_station.station_id)] = self._compute_link_state(
                        satellite.satellite_id, ground_station.station_id, distance,
                        self.satellite_to_user_power, self.satellite_to_user_bandwidth, 'satellite_to_user'
                    )
        
        # 卫星-云中心双向链路（使用预计算的可见性矩阵，消除重复计算）
        cloud_station_list = list(cloud_stations.values())
        for i, satellite in enumerate(satellite_list):
            for j, cloud_station in enumerate(cloud_station_list):
                if satellite_cloud_matrix[i, j]:
                    # 只计算一次距离，双向链路复用
                    distance = self.orbital_updater._calculate_distance(
                        satellite.latitude, satellite.longitude,
                        cloud_station.latitude, cloud_station.longitude,
                        self.orbital_updater.satellite_altitude, 0
                    )
                    
                    # 卫星到云中心链路 (下行)
                    link_states[(satellite.satellite_id, cloud_station.station_id)] = self._compute_link_state(
                        satellite.satellite_id, cloud_station.station_id, distance,
                        self.satellite_to_cloud_power, self.satellite_to_cloud_bandwidth, 'satellite_to_cloud'
                    )
                    
                    # 云中心到卫星链路 (上行)
                    link_states[(cloud_station.station_id, satellite.satellite_id)] = self._compute_link_state(
                        cloud_station.station_id, satellite.satellite_id, distance,
                        self.cloud_transmit_power, self.cloud_to_satellite_bandwidth, 'cloud_to_satellite'
                    )
        
        # 性能优化：缓存结果
        self._link_cache[cache_key] = link_states
        return link_states
    
    def get_link_state(self, source_id: str, target_id: str, time_step: int) -> Optional[Dict[str, float]]:
        """获取特定链路状态"""
        all_links = self.get_all_link_states(time_step)
        return all_links.get((source_id, target_id))
    
    def get_neighbors(self, node_id: str, time_step: int) -> List[str]:
        """获取节点的所有卫星邻居 - 高效版本，只计算星间链路"""
        # 性能优化：只计算星间可见性，不计算完整链路状态
        cache_key = f"neighbors_{node_id}_{time_step}"
        if hasattr(self, '_neighbor_cache') and cache_key in self._neighbor_cache:
            return self._neighbor_cache[cache_key]
            
        if not hasattr(self, '_neighbor_cache'):
            self._neighbor_cache = {}
            
        if self.orbital_updater is None:
            return []
            
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        if not satellites or node_id not in satellites:
            return []
        
        # 只计算星间可见性矩阵 - 修复：传递time_step参数以利用缓存
        inter_satellite_matrix = self.orbital_updater.build_inter_satellite_visibility_matrix(satellites, time_step)
        satellite_list = list(satellites.keys())
        
        neighbors = []
        if node_id in satellite_list:
            node_idx = satellite_list.index(node_id)
            for i, sat_id in enumerate(satellite_list):
                if i != node_idx and inter_satellite_matrix[node_idx, i]:
                    neighbors.append(sat_id)
        
        # 缓存结果
        self._neighbor_cache[cache_key] = neighbors
        return neighbors