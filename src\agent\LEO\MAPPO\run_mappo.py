#!/usr/bin/env python3
"""
MAPPO Training Script for SPACE-OAAL Satellite Edge Computing

标准MAPPO训练执行程序，包含：
- 环境初始化和配置
- 完整的训练循环
- 模型保存和加载
- 性能监控和日志记录

Author: SPACE-OAAL Team
Date: 2025-07-28
"""

import os
import sys
import time
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

try:
    # 修复：适配新的环境接口
    from env.satellite_env import parallel_env, SatelliteEnvironmentCore
    from agent.LEO.MAPPO.mappo import MAPPOTrainer
    # 先暂时注释不存在的工具类
    # from utils.monitor import TrainingMonitor, TaskFailureTracker
    # from utils.performance import PerformanceCollector, EnvironmentStatsCollector, GPUUtilizationTracker
except ImportError as e:
    print(f"Import error: {e}")
    print("请确保你在正确的目录下运行")
    sys.exit(1)


class MAPPOConfig:
    """MAPPO训练配置 - 修复：适配新环境接口"""
    
    def __init__(self):
        # 环境配置 - 修复：支持命令行指定配置文件
        self.config_file = "src/env/config.yaml"  # 默认配置文件路径
        self.enable_rl = True
        
        # 训练配置 (性能优化 - 修改：增加训练数据量)
        self.total_episodes = 2000         # 增加总episodes数以充分利用GPU
        self.max_episode_steps = 500       # 增加每episode步数，与run_PPO一致
        self.eval_episodes = 5             # 减少评估episodes
        self.eval_interval = 25            # 更频繁的评估
        self.save_interval = 50            # 更频繁的保存
        self.update_frequency = 2048       # 新增：每N步更新一次，与run_PPO一致
        
        # MAPPO超参数 - 修复：与run_PPO保持一致
        self.lr_actor = 3e-4
        self.lr_critic = 3e-4  # 修复：与Actor保持一致
        self.gamma = 0.99
        self.gae_lambda = 0.95
        self.clip_ratio = 0.2
        self.entropy_coef = 0.01
        self.value_coef = 0.5
        self.max_grad_norm = 0.5
        self.ppo_epochs = 10               # 修复：与run_PPO一致
        self.batch_size = 4096             # 修改：增大批量以充分利用GPU
        self.mini_batch_size = 128         # 修改：相应增加小批量大小
        self.hidden_dim = 64               # 修复：与run_PPO一致
        
        # PPO技巧开关 - 新增：集成10大技巧
        self.use_state_norm = True         # Trick 2
        self.use_reward_norm = False       # Trick 3
        self.use_reward_scaling = True     # Trick 4  
        self.use_lr_decay = True           # Trick 6
        self.use_orthogonal_init = True    # Trick 8
        self.use_tanh = True               # Trick 10
        self.adam_eps = 1e-5               # Trick 9
        
        # 设备配置
        import torch
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 日志和保存配置
        self.log_dir = "logs/mappo"
        self.model_dir = "models/mappo"
        self.exp_name = f"mappo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"


class TrainingLogger:
    """训练日志记录器"""
    
    def __init__(self, log_dir: str, exp_name: str):
        self.log_dir = os.path.join(log_dir, exp_name)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 训练指标存储
        self.metrics = {
            'episode_rewards': [],
            'episode_lengths': [],
            'actor_losses': [],
            'critic_losses': [],
            'entropies': [],
            'eval_rewards': [],
            'eval_episodes': []
        }
    
    def log_episode(self, episode: int, reward: float, length: int, 
                   actor_loss: float, critic_loss: float, entropy: float):
        """记录episode数据"""
        self.metrics['episode_rewards'].append(reward)
        self.metrics['episode_lengths'].append(length)
        self.metrics['actor_losses'].append(actor_loss)
        self.metrics['critic_losses'].append(critic_loss)
        self.metrics['entropies'].append(entropy)
        
        # 简化的episode信息
        if episode % 10 == 0:
            print(f"Episode {episode}: 奖励={reward:.1f}, 长度={length}, Loss={actor_loss:.3f}")
    
    def log_evaluation(self, episode: int, eval_reward: float):
        """记录评估结果"""
        self.metrics['eval_rewards'].append(eval_reward)
        self.metrics['eval_episodes'].append(episode)
        
        print(f"=== Episode {episode} 评估: 平均奖励={eval_reward:.1f} ===")
    
    def save_metrics(self):
        """保存指标到文件"""
        metrics_file = os.path.join(self.log_dir, 'training_metrics.json')
        with open(metrics_file, 'w') as f:
            json.dump(self.metrics, f, indent=2)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # Episode奖励
        axes[0, 0].plot(self.metrics['episode_rewards'])
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        
        # Episode长度
        axes[0, 1].plot(self.metrics['episode_lengths'])
        axes[0, 1].set_title('Episode Lengths')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Steps')
        
        # Actor损失
        axes[0, 2].plot(self.metrics['actor_losses'])
        axes[0, 2].set_title('Actor Loss')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Loss')
        
        # Critic损失
        axes[1, 0].plot(self.metrics['critic_losses'])
        axes[1, 0].set_title('Critic Loss')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Loss')
        
        # 熵
        axes[1, 1].plot(self.metrics['entropies'])
        axes[1, 1].set_title('Policy Entropy')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Entropy')
        
        # 评估奖励
        if len(self.metrics['eval_rewards']) > 0:
            axes[1, 2].plot(self.metrics['eval_episodes'], self.metrics['eval_rewards'], 'ro-')
            axes[1, 2].set_title('Evaluation Rewards')
            axes[1, 2].set_xlabel('Episode')
            axes[1, 2].set_ylabel('Avg Reward')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.log_dir, 'training_curves.png'))
        plt.close()


def train_mappo(config: MAPPOConfig):
    """MAPPO训练主函数 - 修复：适配新环境接口"""
    
    # 暂时移除监控系统，专注于核心训练
    # monitor = TrainingMonitor(config.log_dir, config.exp_name)
    # perf_collector = PerformanceCollector()
    # env_stats_collector = EnvironmentStatsCollector()
    # gpu_tracker = GPUUtilizationTracker()
    # failure_tracker = TaskFailureTracker()
    
    print("=== MAPPO Training for SPACE-OAAL (修复版) ===")
    print(f"Experiment: {config.exp_name}")
    print(f"Device: {config.device}")
    print(f"Total Episodes: {config.total_episodes} (增加至2000)")
    print(f"Max Episode Steps: {config.max_episode_steps} (增加至500)")
    print(f"Update Frequency: {config.update_frequency} (每{config.update_frequency}步更新)")
    print(f"Batch Size: {config.batch_size} (增加至4096)")
    print(f"PPO Epochs: {config.ppo_epochs} (与run_PPO一致)")
    print(f"Hidden Dim: {config.hidden_dim} (与run_PPO一致)")
    print(f"PPO技巧: 状态归一化={config.use_state_norm}, 奖励缩放={config.use_reward_scaling}")
    print("=" * 60)
    
    # 创建环境 - 修复：使用新的环境接口
    print("Initializing environment...")
    
    # 修复问题4: 通过参数传递配置文件路径，提高健壮性
    config_path = getattr(config, 'config_file', 'src/env/config.yaml')
    if not os.path.exists(config_path):
        # 尝试备选路径
        alt_paths = [
            'src/env/config.yaml',
            os.path.join(os.path.dirname(__file__), '../../../env/config.yaml'),
            'config.yaml'
        ]
        config_path = None
        for path in alt_paths:
            if os.path.exists(path):
                config_path = path
                break
        
        if config_path is None:
            raise FileNotFoundError("Cannot find config.yaml file. Please specify path with --config_file")
    
    env = parallel_env(config_file=config_path)
    
    # 重置环境获取初始信息
    observations, infos = env.reset()
    
    # 获取环境信息 - 修复：从实际环境获取信息
    num_agents = len(observations)
    obs_dim = len(list(observations.values())[0])  # 观测维度
    # 全局状态维度暂时与观测维度相同，后续可以优化
    state_dim = obs_dim * num_agents  # 简单的全局状态：所有智能体观测的拼接
    action_dim = env.action_space.n
    
    print(f"Environment Info:")
    print(f"  Agents: {num_agents}")
    print(f"  Observation Dim: {obs_dim}")
    print(f"  Global State Dim: {state_dim}")
    print(f"  Action Dim: {action_dim}")
    
    # 创建MAPPO训练器 - 修复：传递所有新参数
    print("Initializing MAPPO trainer...")
    trainer = MAPPOTrainer(
        num_agents=num_agents,
        obs_dim=obs_dim,
        global_state_dim=state_dim,
        action_dim=action_dim,
        lr_actor=config.lr_actor,
        lr_critic=config.lr_critic,
        gamma=config.gamma,
        gae_lambda=config.gae_lambda,
        clip_ratio=config.clip_ratio,
        entropy_coef=config.entropy_coef,
        value_coef=config.value_coef,
        max_grad_norm=config.max_grad_norm,
        ppo_epochs=config.ppo_epochs,
        batch_size=config.batch_size,
        mini_batch_size=config.mini_batch_size,  # 新增
        hidden_dim=config.hidden_dim,  # 新增
        # PPO技巧参数
        use_state_norm=config.use_state_norm,
        use_reward_norm=config.use_reward_norm,
        use_reward_scaling=config.use_reward_scaling,
        use_lr_decay=config.use_lr_decay,
        use_orthogonal_init=config.use_orthogonal_init,
        use_tanh=config.use_tanh,
        adam_eps=config.adam_eps,
        device=config.device
    )
    
    # 创建日志记录器
    logger = TrainingLogger(config.log_dir, config.exp_name)
    
    # 创建模型保存目录
    model_save_dir = os.path.join(config.model_dir, config.exp_name)
    os.makedirs(model_save_dir, exist_ok=True)
    
    best_eval_reward = float('-inf')
    total_steps = 0  # 添加全局步数计数器
    
    # 训练循环
    print("Starting training...")
    start_time = time.time()
    
    for episode in range(config.total_episodes):
        # 重置环境
        observations, infos = env.reset()
        episode_reward = 0
        episode_length = 0
        
        # === 数据收集阶段 ===
        # 修复问题1: 改为每N步更新而非每episode更新
        for step in range(config.max_episode_steps):
            # 修复问题2: 创建全局状态（拼接所有智能体观测）
            global_state = np.concatenate([obs for obs in observations.values()])
            valid_actions_mask = {}
            for agent_id in observations.keys():
                valid_actions_mask[agent_id] = infos[agent_id].get('valid_actions', list(range(action_dim)))
            
            # 选择动作
            actions = trainer.select_actions(observations, valid_actions_mask)
            
            # 执行动作
            next_observations, rewards, dones, next_infos = env.step(actions)
            next_global_state = np.concatenate([obs for obs in next_observations.values()])
            
            # 计算步骤奖励
            step_reward = sum(rewards.values())
            
            # 收集数据
            trainer.store_transition(
                observations, global_state, actions, rewards,
                next_observations, next_global_state, dones, valid_actions_mask
            )
            
            # 更新状态
            observations = next_observations
            infos = next_infos
            episode_reward += step_reward
            episode_length += 1
            total_steps += 1
            
            # === 高频更新机制 ===
            # 修改：每N步更新一次，与run_PPO保持一致
            if total_steps % config.update_frequency == 0:
                update_info = trainer.update()
                # 减少更新信息输出
                pass  # 不再输出每步更新信息
            
            # 检查是否结束
            if any(dones.values()):
                break
        
        # === episode结束时的处理 ===
        # 不再在此处进行更新，改为按步数更新
        update_info = {'actor_loss': 0.0, 'critic_loss': 0.0, 'entropy': 0.0}  # 默认值
        
        # === 日志记录阶段 ===
        # 修复问题3: 记录整个episode的累积指标
        logger.log_episode(
            episode, 
            episode_reward,  # 整个episode的总奖励
            episode_length,  # 整个episode的总步数
            update_info.get('actor_loss', 0.0),
            update_info.get('critic_loss', 0.0),
            update_info.get('entropy', 0.0)
        )
        
        # 评估
        if episode % config.eval_interval == 0 and episode > 0:
            eval_reward = evaluate_mappo(trainer, env, config.eval_episodes, config.max_episode_steps)
            logger.log_evaluation(episode, eval_reward)
            
            # 保存最佳模型
            if eval_reward > best_eval_reward:
                best_eval_reward = eval_reward
                best_model_path = os.path.join(model_save_dir, 'best_model.pth')
                trainer.save_models(best_model_path)
                print(f"New best model saved! Reward: {eval_reward:.2f}")
        
        # 定期保存模型
        if episode % config.save_interval == 0 and episode > 0:
            checkpoint_path = os.path.join(model_save_dir, f'checkpoint_episode_{episode}.pth')
            trainer.save_models(checkpoint_path)
    
    # 训练结束
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")
    print(f"Best evaluation reward: {best_eval_reward:.2f}")
    
    # 保存最终模型和训练指标
    final_model_path = os.path.join(model_save_dir, 'final_model.pth')
    trainer.save_models(final_model_path)
    logger.save_metrics()
    logger.plot_training_curves()
    
    # 关闭环境
    env.close()
    
    print(f"Models saved to: {model_save_dir}")
    print(f"Training logs saved to: {logger.log_dir}")
    print("Training completed successfully!")
    
    # 返回训练指标供外部脚本使用
    return {
        'rewards': logger.metrics['episode_rewards'],
        'episode_lengths': logger.metrics['episode_lengths'], 
        'actor_losses': logger.metrics['actor_losses'],
        'critic_losses': logger.metrics['critic_losses'],
        'entropies': logger.metrics['entropies'],
        'eval_rewards': logger.metrics['eval_rewards'],
        'eval_episodes': logger.metrics['eval_episodes'],
        'config': config
    }


def evaluate_mappo(trainer: MAPPOTrainer, env, num_episodes: int, max_steps: int) -> float:
    """评估MAPPO性能"""
    total_rewards = []
    
    for episode in range(num_episodes):
        observations, infos = env.reset()
        episode_reward = 0
        
        for step in range(max_steps):
            # 获取有效动作
            valid_actions_mask = {}
            for agent_id in observations.keys():
                valid_actions_mask[agent_id] = infos[agent_id].get('valid_actions', list(range(env.action_space.n)))
            
            # 选择动作（评估模式）
            actions = trainer.select_actions(observations, valid_actions_mask)
            
            # 执行动作
            next_observations, rewards, dones, next_infos = env.step(actions)
            
            observations = next_observations
            infos = next_infos
            episode_reward += sum(rewards.values())
            
            if any(dones.values()):
                break
        
        total_rewards.append(episode_reward)
    
    return np.mean(total_rewards)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MAPPO Training for SPACE-OAAL')
    parser.add_argument('--episodes', type=int, default=1000, help='Total training episodes')
    parser.add_argument('--max_steps', type=int, default=200, help='Max steps per episode')
    parser.add_argument('--eval_interval', type=int, default=50, help='Evaluation interval')
    parser.add_argument('--save_interval', type=int, default=100, help='Model save interval')
    parser.add_argument('--device', type=str, default='auto', help='Training device (cuda/cpu/auto)')
    parser.add_argument('--exp_name', type=str, default=None, help='Experiment name')
    parser.add_argument('--config_file', type=str, default='src/env/config.yaml', help='Path to config.yaml file')
    
    args = parser.parse_args()
    
    # 创建配置
    config = MAPPOConfig()
    
    # 更新配置
    if args.episodes:
        config.total_episodes = args.episodes
    if args.max_steps:
        config.max_episode_steps = args.max_steps
    if args.eval_interval:
        config.eval_interval = args.eval_interval
    if args.save_interval:
        config.save_interval = args.save_interval
    if args.device and args.device != 'auto':
        config.device = args.device
    if args.exp_name:
        config.exp_name = args.exp_name
    if args.config_file:
        config.config_file = args.config_file
    
    # 开始训练
    try:
        train_mappo(config)
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
    except Exception as e:
        print(f"\nTraining failed with error: {e}")
        raise


if __name__ == "__main__":
    main()