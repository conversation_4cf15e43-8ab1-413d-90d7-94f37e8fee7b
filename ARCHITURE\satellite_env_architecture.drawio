<mxfile host="65bd71144e">
    <diagram name="卫星环境模块架构图" id="satellite-env-architecture">
        <mxGraphModel dx="2836" dy="2285" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="main-function" value="main()&#xa;测试入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="790" y="40" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="data-group" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="250" y="1460" width="1000" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="250" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-adapter" value="SatelliteAdapter&#xa;卫星适配器数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="480" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="task-adapter" value="TaskAdapter&#xa;任务适配器数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="710" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="controller-group" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="417" y="150" width="800" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-environment" value="SatelliteEnvironment&#xa;卫星仿真环境&#xa;- 环境状态管理&#xa;- 时间步控制&#xa;- RL接口协调" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="controller-group" vertex="1">
                    <mxGeometry x="310" y="50" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="wrapper-group" value="算法包装层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffe6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="300" y="320" width="1000" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="independent-wrapper" value="IndependentWrapper&#xa;独立学习包装器&#xa;- 观测隔离&#xa;- 独立奖励计算&#xa;- 智能体独立性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9673a6;fontColor=#ffffff;strokeColor=#9673a6;fontSize=11" parent="wrapper-group" vertex="1">
                    <mxGeometry x="220" y="40" width="200" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="centralized-wrapper" value="CentralizedWrapper&#xa;集中式训练包装器&#xa;- 全局状态提供&#xa;- 协作奖励计算&#xa;- 网络拓扑信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9673a6;fontColor=#ffffff;strokeColor=#9673a6;fontSize=11" parent="wrapper-group" vertex="1">
                    <mxGeometry x="580" y="40" width="200" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="adapter-group" value="适配器层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="420" y="500" width="840" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-adapter-class" value="SatelliteAdapter&#xa;卫星适配器&#xa;- create_satellite_nodes()&#xa;- sync_orbital_states()&#xa;- sync_communication_states()&#xa;- get_satellite_nodes()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="adapter-group" vertex="1">
                    <mxGeometry x="160" y="40" width="200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="task-adapter-class" value="TaskAdapter&#xa;任务适配器&#xa;- load_tasks_for_timeslot()&#xa;- assign_tasks_to_satellites()&#xa;- task flow management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="adapter-group" vertex="1">
                    <mxGeometry x="480" y="55" width="200" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="core-function-group" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="127" y="720" width="1400" height="120" as="geometry">
                        <mxRectangle x="127" y="720" width="100" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="initialize" value="initialize()&#xa;环境初始化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="60" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="reset" value="reset()&#xa;环境重置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="220" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="step" value="step()&#xa;时间步执行" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="380" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-system-state" value="get_system_state()&#xa;系统状态获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="540" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-global-state" value="get_global_state()&#xa;全局状态向量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="720" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-valid-actions" value="get_valid_actions()&#xa;有效动作获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="900" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="render" value="render()&#xa;环境渲染" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="1080" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="close" value="close()&#xa;环境关闭" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="1240" y="40" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="compute-service-group" value="计算服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="180" y="880" width="1200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="get-agent-observation" value="_get_agent_observation()&#xa;智能体观测获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="60" y="40" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="calculate-rewards" value="_calculate_rewards()&#xa;奖励计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="280" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="collect-step-results" value="_collect_step_results()&#xa;时间步结果收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="480" y="40" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-network-topology" value="_get_network_topology()&#xa;网络拓扑获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="680" y="40" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="calculate-cooperation-bonus" value="_calculate_cooperation_bonus()&#xa;协作奖励计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="900" y="40" width="180" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="utility-group" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="250" y="1100" width="1000" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="setup-rl-interface" value="_setup_rl_interface()&#xa;RL接口设置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="80" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-reward-breakdown" value="_get_reward_breakdown()&#xa;奖励分解信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="280" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-cooperation-metrics" value="_get_cooperation_metrics()&#xa;协作指标计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="480" y="40" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="get-environment-info" value="get_environment_info()&#xa;环境信息获取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="700" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="config-group" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="450" y="-130" width="800" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="num-satellites" value="num_satellites&#xa;卫星数量" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="50" y="35" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="total-timeslots" value="total_timeslots&#xa;总时间步数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="180" y="35" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="timeslot-duration" value="timeslot_duration&#xa;时隙持续时间" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="330" y="35" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="enable-rl" value="enable_rl&#xa;启用强化学习" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="480" y="35" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="obs-size" value="obs_size&#xa;观测维度" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="630" y="35" width="100" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="rl-interface-group" value="RL接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff0f0;strokeColor=#d62728;fontColor=#8b0000;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="200" y="1280" width="1200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="oaal-env-interface" value="OAALEnvironmentInterface&#xa;OAAL环境接口&#xa;- 动作空间管理&#xa;- RL状态转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d62728;fontColor=#ffffff;strokeColor=#d62728;fontSize=11" parent="rl-interface-group" vertex="1">
                    <mxGeometry x="500" y="40" width="200" height="60" as="geometry"/>
                </mxCell>
                <!-- 连接关系 -->
                <mxCell id="main-to-env" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="main-function" target="satellite-environment" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="env-to-independent" value="包装" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-environment" target="independent-wrapper" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="env-to-centralized" value="包装" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-environment" target="centralized-wrapper" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="env-to-satellite-adapter" value="使用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-environment" target="satellite-adapter-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="env-to-task-adapter" value="使用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-environment" target="task-adapter-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="env-to-rl-interface" value="集成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-environment" target="oaal-env-interface" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="827" y="1200"/>
                            <mxPoint x="1000" y="1200"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="initialize-to-reset" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="initialize" target="reset" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="reset-to-step" value="循环调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="reset" target="step" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="step-to-collect" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="step" target="collect-step-results" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="wrapper-to-observation" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="independent-wrapper" target="get-agent-observation" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="620" y="840"/>
                            <mxPoint x="440" y="840"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="wrapper-to-rewards" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="independent-wrapper" target="calculate-rewards" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="620" y="850"/>
                            <mxPoint x="640" y="850"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="centralized-to-global" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="centralized-wrapper" target="get-global-state" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="centralized-to-network" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="centralized-wrapper" target="get-network-topology" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="centralized-to-cooperation" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="centralized-wrapper" target="calculate-cooperation-bonus" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="data-flow-config" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="config-yaml" target="satellite-environment" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="590" y="1200"/>
                            <mxPoint x="827" y="1200"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="data-flow-sat-adapter" value="适配器数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="satellite-adapter" target="satellite-adapter-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="data-flow-task-adapter" value="适配器数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="task-adapter" target="task-adapter-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="main-data-flow" value="主仿真循环&#xa;(1000 timeslots)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="satellite-environment" target="reset" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="827" y="650"/>
                            <mxPoint x="507" y="650"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="legend-group" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="-30" y="-120" width="200" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="legend-solid" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="40" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-dashed" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="65" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-thick" value="粗线：主要仿真流程" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="90" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-blue" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="115" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-purple" value="紫色：算法包装器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="140" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-green" value="绿色：适配器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="165" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-orange" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="190" width="120" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>