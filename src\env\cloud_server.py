#!/usr/bin/env python3
"""
CloudServer.py - SPACE-DMPO1 云服务器模块

实现云服务器的任务处理逻辑，包括：
- 20个任务队列管理
- 0.2秒固定处理时间
- 任务标签标识
- 与最近卫星的转发逻辑

Author: SPACE-DMPO1 Team
Date: 2025-07-29
"""

from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Optional, Any, Tuple
import time
import heapq
from collections import deque

# 导入核心模块
try:
    from .task import Task, TaskState
    from .orbital_updater import OrbitalUpdater, GroundStation
except ImportError:
    from task import Task, TaskState
    from orbital_updater import OrbitalUpdater, GroundStation


@dataclass
class CloudProcessingRecord:
    """云服务器处理记录"""
    task_id: str
    cloud_center_id: str
    start_time: float
    end_time: float
    processing_duration: float
    forward_satellite_id: Optional[str] = None


class CloudTaskState(Enum):
    """云服务器任务状态"""
    QUEUED = "queued"           # 排队中
    PROCESSING = "processing"   # 处理中
    COMPLETED = "completed"     # 处理完成
    FORWARDING = "forwarding"   # 转发中
    DELIVERED = "delivered"     # 已交付


@dataclass
class CloudTask:
    """云服务器任务包装"""
    task: Task
    arrival_time: float
    cloud_state: CloudTaskState
    processing_start_time: Optional[float] = None
    processing_end_time: Optional[float] = None
    assigned_cloud_center: Optional[str] = None
    

class CloudCenter:
    """单个云中心"""
    
    def __init__(self, center_id: str, ground_station: GroundStation):
        self.center_id = center_id
        self.ground_station = ground_station
        self.latitude = ground_station.latitude
        self.longitude = ground_station.longitude
        
        # 任务队列管理
        self.task_queue: deque[CloudTask] = deque()  # 等待队列
        self.processing_tasks: Dict[str, CloudTask] = {}  # 正在处理的任务
        self.completed_tasks: deque[CloudTask] = deque()  # 完成待转发的任务
        
        # 配置参数
        self.max_queue_size = 20  # 最大队列长度
        self.processing_time = 0.2  # 固定处理时间(秒)
        
        # 统计信息
        self.total_tasks_received = 0
        self.total_tasks_processed = 0
        self.total_tasks_forwarded = 0
        self.total_processing_time = 0.0
        
    def can_accept_task(self) -> bool:
        """检查是否可以接受新任务"""
        current_load = len(self.task_queue) + len(self.processing_tasks)
        return current_load < self.max_queue_size
    
    def receive_task(self, task: Task, current_time: float) -> bool:
        """接收任务"""
        if not self.can_accept_task():
            return False
        
        # 创建云任务包装
        cloud_task = CloudTask(
            task=task,
            arrival_time=current_time,
            cloud_state=CloudTaskState.QUEUED,
            assigned_cloud_center=self.center_id
        )
        
        # 添加到队列
        self.task_queue.append(cloud_task)
        self.total_tasks_received += 1
        
        # 更新原任务状态和标签
        task.update_state(TaskState.PROCESSING, self.center_id)
        if hasattr(task, 'processing_location'):
            task.processing_location = 'cloud_server'
        if hasattr(task, 'cloud_processing_record'):
            task.cloud_processing_record = CloudProcessingRecord(
                task_id=task.task_id,
                cloud_center_id=self.center_id,
                start_time=current_time,
                end_time=0.0,
                processing_duration=0.0
            )
        
        return True
    
    def start_processing(self, current_time: float) -> int:
        """开始处理任务"""
        started_count = 0
        
        # 只要有队列中的任务就开始处理（云服务器处理能力强）
        while self.task_queue and len(self.processing_tasks) < 5:  # 假设可以并行处理5个任务
            cloud_task = self.task_queue.popleft()
            
            # 开始处理
            cloud_task.cloud_state = CloudTaskState.PROCESSING
            cloud_task.processing_start_time = current_time
            cloud_task.processing_end_time = current_time + self.processing_time
            
            self.processing_tasks[cloud_task.task.task_id] = cloud_task
            started_count += 1
        
        return started_count
    
    def update_processing(self, current_time: float) -> List[CloudTask]:
        """更新处理进度，返回完成的任务"""
        completed_tasks = []
        
        # 检查所有正在处理的任务
        finished_task_ids = []
        for task_id, cloud_task in self.processing_tasks.items():
            if cloud_task.processing_end_time and current_time >= cloud_task.processing_end_time:
                # 任务处理完成
                cloud_task.cloud_state = CloudTaskState.COMPLETED
                
                # 更新原任务状态 - 标记为云服务器处理完成
                task = cloud_task.task
                if hasattr(task, 'complete_processing'):
                    task.complete_processing(current_time, False)
                
                # 更新云处理记录
                if hasattr(task, 'cloud_processing_record') and task.cloud_processing_record:
                    task.cloud_processing_record.end_time = current_time
                    task.cloud_processing_record.processing_duration = self.processing_time
                
                # 设置云服务器处理标签
                if hasattr(task, 'processed_by'):
                    task.processed_by = 'cloud_server'
                if hasattr(task, 'processing_satellite_id'):
                    task.processing_satellite_id = self.center_id
                
                self.completed_tasks.append(cloud_task)
                completed_tasks.append(cloud_task)
                finished_task_ids.append(task_id)
                
                self.total_tasks_processed += 1
                self.total_processing_time += self.processing_time
        
        # 从正在处理的任务中移除已完成的任务
        for task_id in finished_task_ids:
            del self.processing_tasks[task_id]
        
        return completed_tasks
    
    def get_completed_tasks_for_forwarding(self) -> List[CloudTask]:
        """获取需要转发的已完成任务"""
        tasks_to_forward = []
        while self.completed_tasks:
            tasks_to_forward.append(self.completed_tasks.popleft())
        return tasks_to_forward
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取云中心状态摘要"""
        return {
            'center_id': self.center_id,
            'location': {'latitude': self.latitude, 'longitude': self.longitude},
            'queue_length': len(self.task_queue),
            'processing_tasks': len(self.processing_tasks),
            'completed_tasks': len(self.completed_tasks),
            'total_received': self.total_tasks_received,
            'total_processed': self.total_tasks_processed,
            'total_forwarded': self.total_tasks_forwarded,
            'utilization': len(self.processing_tasks) / 5.0,  # 假设最大并行5个
            'queue_utilization': (len(self.task_queue) + len(self.processing_tasks)) / self.max_queue_size
        }


class CloudServerManager:
    """云服务器管理器"""
    
    def __init__(self, orbital_updater: OrbitalUpdater):
        self.orbital_updater = orbital_updater
        self.cloud_centers: Dict[str, CloudCenter] = {}
        self._initialize_cloud_centers()
        
    def _initialize_cloud_centers(self):
        """初始化云中心"""
        for center_id, ground_station in self.orbital_updater.cloud_stations.items():
            self.cloud_centers[center_id] = CloudCenter(center_id, ground_station)
        
        print(f"云服务器管理器初始化完成: {len(self.cloud_centers)} 个云中心")
    
    def get_visible_cloud_centers(self, satellite_id: str, time_step: int) -> List[str]:
        """获取卫星可见的云中心列表"""
        satellites = self.orbital_updater.get_satellites_at_time(time_step)
        if satellite_id not in satellites:
            return []
        
        satellite = satellites[satellite_id]
        visible_centers = []
        
        for center_id, cloud_center in self.cloud_centers.items():
            if self.orbital_updater.calculate_satellite_cloud_visibility(satellite, cloud_center.ground_station):
                visible_centers.append(center_id)
        
        return visible_centers
    
    def find_best_cloud_center(self, satellite_id: str, time_step: int, task: Task) -> Optional[str]:
        """为任务找到最佳云中心"""
        visible_centers = self.get_visible_cloud_centers(satellite_id, time_step)
        
        if not visible_centers:
            return None
        
        # 选择队列最短的云中心
        best_center = None
        min_load = float('inf')
        
        for center_id in visible_centers:
            cloud_center = self.cloud_centers[center_id]
            if cloud_center.can_accept_task():
                current_load = len(cloud_center.task_queue) + len(cloud_center.processing_tasks)
                if current_load < min_load:
                    min_load = current_load
                    best_center = center_id
        
        return best_center
    
    def send_task_to_cloud(self, satellite_id: str, task: Task, time_step: int, current_time: float) -> bool:
        """将任务发送到云服务器"""
        best_center = self.find_best_cloud_center(satellite_id, time_step, task)
        
        if not best_center:
            return False
        
        cloud_center = self.cloud_centers[best_center]
        success = cloud_center.receive_task(task, current_time)
        
        if success:
            print(f"DEBUG: Task {task.task_id} sent to cloud center {best_center} by {satellite_id}")
        
        return success
    
    def find_nearest_satellite_for_forwarding(self, cloud_center_id: str, satellites: Dict[str, Any], time_step: int) -> Optional[str]:
        """找到距离云中心最近的可见卫星用于转发"""
        if cloud_center_id not in self.cloud_centers:
            return None
        
        cloud_center = self.cloud_centers[cloud_center_id]
        visible_satellites = []
        
        # 找到所有可见的卫星
        for satellite_id, satellite in satellites.items():
            if self.orbital_updater.calculate_satellite_cloud_visibility(satellite, cloud_center.ground_station):
                # 计算距离
                distance = self.orbital_updater._calculate_distance(
                    satellite.latitude, satellite.longitude,
                    cloud_center.latitude, cloud_center.longitude,
                    self.orbital_updater.satellite_altitude, 0
                )
                visible_satellites.append((satellite_id, distance))
        
        if not visible_satellites:
            return None
        
        # 返回距离最近的卫星
        nearest_satellite = min(visible_satellites, key=lambda x: x[1])
        return nearest_satellite[0]
    
    def step(self, time_step: int, time_delta: float, satellites: Dict[str, Any]) -> Dict[str, Any]:
        """执行一个时间步"""
        current_time = time_step * time_delta
        forwarding_results = {}
        
        for center_id, cloud_center in self.cloud_centers.items():
            # 开始处理新任务
            cloud_center.start_processing(current_time)
            
            # 更新处理进度
            completed_tasks = cloud_center.update_processing(current_time)
            
            # 处理需要转发的任务
            tasks_to_forward = cloud_center.get_completed_tasks_for_forwarding()
            
            if tasks_to_forward:
                nearest_satellite = self.find_nearest_satellite_for_forwarding(center_id, satellites, time_step)
                
                if nearest_satellite:
                    forwarding_results[center_id] = {
                        'nearest_satellite': nearest_satellite,
                        'tasks': [ct.task for ct in tasks_to_forward]
                    }
                    
                    # 更新转发统计
                    cloud_center.total_tasks_forwarded += len(tasks_to_forward)
                    
                    # 标记任务为转发状态
                    for cloud_task in tasks_to_forward:
                        cloud_task.cloud_state = CloudTaskState.FORWARDING
                        if hasattr(cloud_task.task, 'cloud_processing_record'):
                            cloud_task.task.cloud_processing_record.forward_satellite_id = nearest_satellite
        
        return forwarding_results
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取整个云服务器系统状态"""
        status = {
            'total_centers': len(self.cloud_centers),
            'centers': {}
        }
        
        total_received = 0
        total_processed = 0
        total_forwarded = 0
        total_queue_length = 0
        total_processing = 0
        
        for center_id, cloud_center in self.cloud_centers.items():
            center_status = cloud_center.get_status_summary()
            status['centers'][center_id] = center_status
            
            total_received += center_status['total_received']
            total_processed += center_status['total_processed']
            total_forwarded += center_status['total_forwarded']
            total_queue_length += center_status['queue_length']
            total_processing += center_status['processing_tasks']
        
        status.update({
            'total_tasks_received': total_received,
            'total_tasks_processed': total_processed,
            'total_tasks_forwarded': total_forwarded,
            'total_queue_length': total_queue_length,
            'total_processing_tasks': total_processing,
            'system_utilization': total_processing / (len(self.cloud_centers) * 5) if self.cloud_centers else 0
        })
        
        return status


def main():
    """简单测试"""
    print("=== 云服务器模块测试 ===")
    
    # 这里可以添加简单的测试逻辑
    print("云服务器模块加载成功")


if __name__ == "__main__":
    main()