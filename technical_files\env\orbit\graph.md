我将根据 `orbital_updater.py` 模块为您生成详细的程序架构图。

## Markdown 格式架构图

```
orbital_updater.py 程序架构图
│
├── 📦 导入依赖模块
│   ├── pandas (数据处理)
│   ├── numpy (数值计算)
│   ├── yaml (配置文件读取)
│   ├── datetime (时间处理)
│   ├── typing (类型注解)
│   ├── math (数学计算)
│   └── os (文件操作)
│
├── 🏗️ 核心类定义
│   │
│   ├── 📡 Class: Satellite
│   │   ├── 🔧 __init__(satellite_id, longitude, latitude, illuminated, timestamp, total_timeslots)
│   │   │   ├── self.satellite_id: str (卫星唯一标识)
│   │   │   ├── self.longitude: float (经度坐标)
│   │   │   ├── self.latitude: float (纬度坐标)
│   │   │   ├── self.illuminated: bool (光照状态)
│   │   │   ├── self.timestamp: datetime (时间戳)
│   │   │   ├── self.total_timeslots: int (总时隙数)
│   │   │   └── self.velocity: Optional[Tuple] (速度向量)
│   │   └── 📄 __repr__() → str (对象字符串表示)
│   │
│   ├── 🏢 Class: GroundStation
│   │   ├── 🔧 __init__(station_id, longitude, latitude, name, type)
│   │   │   ├── self.station_id: str (地面站ID)
│   │   │   ├── self.longitude: float (经度坐标)
│   │   │   ├── self.latitude: float (纬度坐标)
│   │   │   ├── self.name: Optional[str] (地面站名称)
│   │   │   └── self.type: Optional[str] (地面站类型)
│   │   └── 📄 __repr__() → str (对象字符串表示)
│   │
│   └── 🛰️ Class: OrbitalUpdater (主控制器)
│       │
│       ├── 🔧 初始化方法
│       │   └── __init__(data_file, config_file)
│       │       ├── 加载配置参数
│       │       ├── 初始化数据文件路径
│       │       ├── 创建地面站网络
│       │       └── 设置系统参数
│       │
│       ├── 🔒 私有方法组 (内部数据处理)
│       │   ├── _load_config() → dict
│       │   │   └── 📁 读取 config.yaml 配置文件
│       │   ├── _load_satellite_data() → pd.DataFrame
│       │   │   └── 📁 读取 satellite_processed_data.csv
│       │   └── _create_ground_stations() → Dict[str, GroundStation]
│       │       └── 📁 读取 updated_global_ground_stations.csv
│       │
│       ├── 🌍 轨道状态管理方法
│       │   ├── get_satellites_at_time(time_step) → Dict[str, Satellite]
│       │   │   ├── 输入: 时间步索引 (0-999)
│       │   │   ├── 处理: 从数据中提取指定时间的卫星状态
│       │   │   └── 输出: 卫星ID到Satellite对象的映射
│       │   │
│       │   └── calculate_velocity(satellite_id, time_step) → Optional[Tuple[float, float]]
│       │       ├── 输入: 卫星ID + 时间步
│       │       ├── 处理: 计算相邻时间步的位置差分
│       │       └── 输出: (经度速度, 纬度速度) 度/秒
│       │
│       ├── 🔗 可见性计算方法
│       │   ├── calculate_satellite_visibility(sat1, sat2) → bool
│       │   │   ├── 输入: 两个Satellite对象
│       │   │   ├── 处理: 3D距离计算 + 阈值判断
│       │   │   └── 输出: 是否可见的布尔值
│       │   │
│       │   ├── build_inter_satellite_visibility_matrix(satellites) → np.ndarray
│       │   │   ├── 输入: 卫星字典
│       │   │   ├── 处理: 构建N×N对称可见性矩阵
│       │   │   └── 输出: 布尔矩阵 (36×36)
│       │   │
│       │   ├── calculate_ground_visibility(satellite, ground_station) → bool
│       │   │   ├── 输入: Satellite对象 + GroundStation对象
│       │   │   ├── 处理: 卫星-地面站距离计算
│       │   │   └── 输出: 是否可见的布尔值
│       │   │
│       │   └── build_satellite_ground_visibility_matrix(satellites) → np.ndarray
│       │       ├── 输入: 卫星字典
│       │       ├── 处理: 构建卫星-地面站可见性矩阵
│       │       └── 输出: 布尔矩阵 (36×420)
│       │
│       ├── 📊 地面覆盖计算方法
│       │   ├── get_ground_coverage(satellite) → Dict[str, float]
│       │   │   ├── 输入: Satellite对象
│       │   │   ├── 处理: 几何计算覆盖半径和面积
│       │   │   └── 输出: 覆盖信息字典
│       │   │
│       │   └── get_all_ground_coverage(satellites) → Dict[str, Dict[str, float]]
│       │       ├── 输入: 卫星字典
│       │       ├── 处理: 批量计算所有卫星覆盖
│       │       └── 输出: 嵌套字典结构
│       │
│       └── 🔧 工具方法
│           └── get_total_timeslots() → int
│               └── 返回配置中的总时隙数
│
├── 📁 数据文件依赖
│   ├── config.yaml (系统配置参数)
│   ├── satellite_processed_data.csv (36颗卫星×1000时隙轨道数据)
│   └── updated_global_ground_stations.csv (420个地面站坐标)
│
└── 🚀 主函数入口
    └── main()
        ├── 创建OrbitalUpdater实例
        ├── 遍历所有时隙 (0 to total_timeslots-1)
        │   ├── 获取当前时隙卫星状态
        │   ├── 计算卫星间可见性矩阵
        │   ├── 计算卫星-地面站可见性矩阵
        │   ├── 计算地面覆盖信息
        │   └── 输出统计信息
        └── 完成演示测试
```

## Mermaid 格式架构图

````mermaid path=orbital_updater_architecture.mmd mode=EDIT
graph TB
    %% 外部依赖
    subgraph "📦 External Dependencies"
        pandas[pandas<br/>数据处理]
        numpy[numpy<br/>数值计算]
        yaml[yaml<br/>配置读取]
        datetime[datetime<br/>时间处理]
        typing[typing<br/>类型注解]
        math[math<br/>数学计算]
        os[os<br/>文件操作]
    end

    %% 数据文件
    subgraph "📁 Data Files"
        config_yaml[config.yaml<br/>系统配置]
        sat_data[satellite_processed_data.csv<br/>卫星轨道数据]
        ground_data[updated_global_ground_stations.csv<br/>地面站数据]
    end

    %% 核心类定义
    subgraph "🏗️ Core Classes"
        %% Satellite类
        subgraph "📡 Satellite Class"
            sat_init[__init__<br/>satellite_id, longitude, latitude<br/>illuminated, timestamp, total_timeslots]
            sat_repr[__repr__<br/>字符串表示]
            sat_attrs[属性:<br/>• satellite_id: str<br/>• longitude: float<br/>• latitude: float<br/>• illuminated: bool<br/>• timestamp: datetime<br/>• total_timeslots: int<br/>• velocity: Optional[Tuple]]
        end

        %% GroundStation类
        subgraph "🏢 GroundStation Class"
            gs_init[__init__<br/>station_id, longitude, latitude<br/>name, type]
            gs_repr[__repr__<br/>字符串表示]
            gs_attrs[属性:<br/>• station_id: str<br/>• longitude: float<br/>• latitude: float<br/>• name: Optional[str]<br/>• type: Optional[str]]
        end

        %% OrbitalUpdater主类
        subgraph "🛰️ OrbitalUpdater Class"
            %% 初始化
            ou_init[__init__<br/>data_file, config_file]
            
            %% 私有方法
            subgraph "🔒 Private Methods"
                load_config[_load_config<br/>→ dict]
                load_sat_data[_load_satellite_data<br/>→ pd.DataFrame]
                create_gs[_create_ground_stations<br/>→ Dict[str, GroundStation]]
            end

            %% 轨道状态管理
            subgraph "🌍 Orbital State Management"
                get_sats[get_satellites_at_time<br/>time_step → Dict[str, Satellite]]
                calc_velocity[calculate_velocity<br/>satellite_id, time_step → Tuple[float, float]]
            end

            %% 可见性计算
            subgraph "🔗 Visibility Calculations"
                calc_sat_vis[calculate_satellite_visibility<br/>sat1, sat2 → bool]
                build_inter_vis[build_inter_satellite_visibility_matrix<br/>satellites → np.ndarray]
                calc_ground_vis[calculate_ground_visibility<br/>satellite, ground_station → bool]
                build_ground_vis[build_satellite_ground_visibility_matrix<br/>satellites → np.ndarray]
            end

            %% 地面覆盖计算
            subgraph "📊 Ground Coverage"
                get_coverage[get_ground_coverage<br/>satellite → Dict[str, float]]
                get_all_coverage[get_all_ground_coverage<br/>satellites → Dict[str, Dict[str, float]]]
            end

            %% 工具方法
            get_timeslots[get_total_timeslots<br/>→ int]
        end
    end

    %% 主函数
    main_func[🚀 main<br/>演示函数]

    %% 连接关系
    %% 数据依赖
    config_yaml --> load_config
    sat_data --> load_sat_data
    ground_data --> create_gs

    %% 外部依赖
    pandas --> load_sat_data
    pandas --> create_gs
    numpy --> build_inter_vis
    numpy --> build_ground_vis
    yaml --> load_config
    datetime --> sat_init
    math --> calc_sat_vis
    os --> create_gs

    %% 初始化流程
    ou_init --> load_config
    ou_init --> load_sat_data
    ou_init --> create_gs

    %% 方法调用关系
    get_sats --> sat_init
    calc_velocity --> get_sats
    build_inter_vis --> calc_sat_vis
    build_ground_vis --> calc_ground_vis
    get_all_coverage --> get_coverage
    get_timeslots --> config_yaml

    %% 主函数调用
    main_func --> ou_init
    main_func --> get_sats
    main_func --> build_inter_vis
    main_func --> build_ground_vis
    main_func --> get_all_coverage
    main_func --> get_timeslots

    %% 样式定义
    classDef classBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef methodBox fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef dataBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef mainBox fill:#fff3e0,stroke:#e65100,stroke-width:3px

    class sat_init,gs_init,ou_init classBox
    class load_config,load_sat_data,create_gs,get_sats,calc_velocity,calc_sat_vis,build_inter_vis,calc_ground_vis,build_ground_vis,get_coverage,get_all_coverage,get_timeslots methodBox
    class config_yaml,sat_data,ground_data dataBox
    class main_func mainBox
````

## 架构关系说明

### 🔄 数据流向
1. **初始化阶段**: `OrbitalUpdater.__init__()` → 私有方法组 → 加载配置和数据
2. **运行时查询**: `get_satellites_at_time()` → 创建 `Satellite` 实例
3. **可见性计算**: 卫星实例 → 可见性计算方法 → 矩阵输出
4. **覆盖计算**: 卫星实例 → 覆盖计算方法 → 覆盖信息

### 🎯 核心职责分工
- **Satellite类**: 卫星状态数据容器
- **GroundStation类**: 地面站信息容器  
- **OrbitalUpdater类**: 轨道动力学计算引擎
- **私有方法**: 数据加载和预处理
- **公有方法**: 对外提供计算服务接口

### 🔗 模块间依赖
- **配置驱动**: 所有参数从 `config.yaml` 统一管理
- **数据驱动**: 轨道数据和地面站数据从CSV文件加载
- **面向对象**: 清晰的类职责分离和接口设计



