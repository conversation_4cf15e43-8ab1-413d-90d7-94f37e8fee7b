#!/usr/bin/env python3
"""
随机策略基准测试模块

提供完全随机策略下的系统性能基准，包括：
1. Episode级别的详细性能记录
2. 指定卫星的能量变化追踪
3. 任务生命周期完整追踪
4. 详细的调试信息和数据导出

Author: SPACE-OAAL Team
Date: 2025-08-02
"""

import os
import sys
import json
import csv
import time
import numpy as np
import random
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 导入环境模块
try:
    from src.env.satellite_env import SatelliteParallelEnv
    from src.env.task import Task, TaskState
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


@dataclass
class EnergyRecord:
    """卫星能量记录"""
    timeslot: int
    timestamp: float
    battery_level_j: float
    battery_ratio: float
    is_illuminated: bool
    solar_charging_w: float
    base_consumption_w: float
    task_processing_consumption_w: float
    communication_consumption_w: float
    net_energy_change_j: float
    active_tasks_count: int


@dataclass
class TaskStage:
    """任务处理阶段记录"""
    stage: str
    satellite_id: Optional[str]
    start_time: float
    end_time: float
    energy_consumed_j: float
    action_taken: str
    success: bool
    details: Dict[str, Any]


@dataclass
class TaskJourney:
    """任务完整生命周期记录"""
    task_id: str
    source_timeslot: int
    source_location_id: int
    data_size_mb: float
    priority: int
    deadline: float
    journey_stages: List[TaskStage]
    total_energy_cost_j: float
    completion_status: str
    end_to_end_latency_s: float


class RandomPolicy:
    """随机策略实现"""
    
    def __init__(self, policy_type: str = "uniform"):
        """
        初始化随机策略
        
        Args:
            policy_type: 策略类型 - "uniform", "weighted", "constrained"
        """
        self.policy_type = policy_type
        self.action_weights = {
            "local": 0.7,      # 本地处理权重较高
            "satellite": 0.2,   # 卫星卸载
            "cloud": 0.1       # 云端卸载
        }
    
    def get_action(self, agent_id: str, valid_actions: List[int], 
                  observation: np.ndarray = None) -> int:
        """
        获取随机动作
        
        Args:
            agent_id: 智能体ID
            valid_actions: 有效动作列表
            observation: 观测（暂未使用）
            
        Returns:
            int: 选择的动作ID
        """
        if not valid_actions:
            return 0  # 默认本地处理
        
        if self.policy_type == "uniform":
            return random.choice(valid_actions)
        elif self.policy_type == "weighted":
            return self._weighted_choice(valid_actions)
        elif self.policy_type == "constrained":
            return self._constrained_choice(valid_actions, observation)
        else:
            return random.choice(valid_actions)
    
    def _weighted_choice(self, valid_actions: List[int]) -> int:
        """加权随机选择"""
        if 0 in valid_actions:  # 如果本地处理可用，给予更高权重
            weights = []
            for action in valid_actions:
                if action == 0:
                    weights.append(self.action_weights["local"])
                elif action <= 36:  # 卫星卸载
                    weights.append(self.action_weights["satellite"])
                else:  # 云端卸载
                    weights.append(self.action_weights["cloud"])
            
            return random.choices(valid_actions, weights=weights)[0]
        else:
            return random.choice(valid_actions)
    
    def _constrained_choice(self, valid_actions: List[int], 
                          observation: np.ndarray) -> int:
        """约束随机选择（考虑观测信息）"""
        if observation is None:
            return random.choice(valid_actions)
        
        # 如果能量较低（电量比例 < 0.3），优先选择本地处理
        energy_ratio = observation[4] if len(observation) > 4 else 1.0
        if energy_ratio < 0.3 and 0 in valid_actions:
            return 0
        
        return random.choice(valid_actions)


class SatelliteEnergyTracker:
    """卫星能量变化追踪器"""
    
    def __init__(self, target_satellite: str = "Satellite111"):
        """
        初始化能量追踪器
        
        Args:
            target_satellite: 目标追踪卫星ID
        """
        self.target_satellite = target_satellite
        self.energy_timeline: List[EnergyRecord] = []
        self.previous_energy: Optional[float] = None
    
    def record_energy_state(self, timeslot: int, timestamp: float, 
                          satellite_node) -> EnergyRecord:
        """
        记录卫星能量状态
        
        Args:
            timeslot: 当前时隙
            timestamp: 当前时间戳
            satellite_node: 卫星节点对象
            
        Returns:
            EnergyRecord: 能量记录
        """
        energy_state = satellite_node.energy_state
        current_energy = energy_state.current_battery_j
        
        # 计算能量变化
        net_change = 0.0
        if self.previous_energy is not None:
            net_change = current_energy - self.previous_energy
        
        # 估算不同类型的能耗
        base_consumption = energy_state.base_power_consumption_w
        solar_charging = energy_state.solar_power_w if energy_state.is_illuminated else 0.0
        
        # 获取任务处理能耗（从步长指标中获取）
        step_energy_consumed = satellite_node.performance_metrics.step_energy_consumed_j
        task_processing_consumption = step_energy_consumed / 10.0 if step_energy_consumed > 0 else 0.0  # 转换为功率
        
        record = EnergyRecord(
            timeslot=timeslot,
            timestamp=timestamp,
            battery_level_j=current_energy,
            battery_ratio=energy_state.battery_ratio,
            is_illuminated=energy_state.is_illuminated,
            solar_charging_w=solar_charging,
            base_consumption_w=base_consumption,
            task_processing_consumption_w=task_processing_consumption,
            communication_consumption_w=0.0,  # 暂时简化
            net_energy_change_j=net_change,
            active_tasks_count=len(satellite_node.running_tasks)
        )
        
        self.energy_timeline.append(record)
        self.previous_energy = current_energy
        
        return record
    
    def get_energy_summary(self) -> Dict[str, Any]:
        """获取能量追踪总结"""
        if not self.energy_timeline:
            return {}
        
        energy_levels = [r.battery_ratio for r in self.energy_timeline]
        energy_changes = [r.net_energy_change_j for r in self.energy_timeline]
        
        return {
            'average_energy_ratio': np.mean(energy_levels),
            'min_energy_ratio': np.min(energy_levels),
            'max_energy_ratio': np.max(energy_levels),
            'energy_variance': np.var(energy_levels),
            'total_energy_change_j': sum(energy_changes),
            'low_energy_periods': len([r for r in self.energy_timeline if r.battery_ratio < 0.2]),
            'illumination_ratio': np.mean([r.is_illuminated for r in self.energy_timeline])
        }


class TaskJourneyTracker:
    """任务生命周期追踪器"""
    
    def __init__(self):
        """初始化任务追踪器"""
        self.tracked_tasks: Dict[int, str] = {}  # {timeslot: task_id}
        self.task_journeys: Dict[str, TaskJourney] = {}
        self.task_states: Dict[str, Dict] = {}  # 临时状态存储
    
    def register_first_task(self, timeslot: int, task: Task) -> bool:
        """
        注册每个时隙的第一个任务
        
        Args:
            timeslot: 时隙
            task: 任务对象
            
        Returns:
            bool: 是否成功注册
        """
        if timeslot in self.tracked_tasks:
            return False  # 已有任务注册
        
        task_id = str(task.task_id)
        self.tracked_tasks[timeslot] = task_id
        
        # 初始化任务记录
        journey = TaskJourney(
            task_id=task_id,
            source_timeslot=timeslot,
            source_location_id=getattr(task, 'source_location_id', 0),
            data_size_mb=getattr(task, 'data_size_mb', 1.0),
            priority=getattr(task, 'priority', 1),
            deadline=getattr(task, 'deadline_timestamp', 0.0),
            journey_stages=[],
            total_energy_cost_j=0.0,
            completion_status='active',
            end_to_end_latency_s=0.0
        )
        
        self.task_journeys[task_id] = journey
        self.task_states[task_id] = {
            'start_time': time.time(),
            'current_satellite': None,
            'stage_start': time.time()
        }
        
        return True
    
    def record_task_stage(self, task_id: str, stage: str, satellite_id: str,
                         start_time: float, end_time: float, energy_consumed: float,
                         action_taken: str, success: bool, details: Dict = None):
        """
        记录任务处理阶段
        
        Args:
            task_id: 任务ID
            stage: 阶段名称
            satellite_id: 处理卫星ID
            start_time: 开始时间
            end_time: 结束时间
            energy_consumed: 消耗能量
            action_taken: 执行的动作
            success: 是否成功
            details: 额外详情
        """
        if task_id not in self.task_journeys:
            return
        
        stage_record = TaskStage(
            stage=stage,
            satellite_id=satellite_id,
            start_time=start_time,
            end_time=end_time,
            energy_consumed_j=energy_consumed,
            action_taken=action_taken,
            success=success,
            details=details or {}
        )
        
        self.task_journeys[task_id].journey_stages.append(stage_record)
        self.task_journeys[task_id].total_energy_cost_j += energy_consumed
    
    def complete_task(self, task_id: str, status: str, end_time: float):
        """
        完成任务追踪
        
        Args:
            task_id: 任务ID
            status: 完成状态
            end_time: 结束时间
        """
        if task_id not in self.task_journeys:
            return
        
        journey = self.task_journeys[task_id]
        if task_id in self.task_states:
            start_time = self.task_states[task_id]['start_time']
            journey.end_to_end_latency_s = end_time - start_time
        
        journey.completion_status = status
    
    def get_journey_summary(self) -> Dict[str, Any]:
        """获取任务生命周期总结"""
        if not self.task_journeys:
            return {}
        
        completed_journeys = [j for j in self.task_journeys.values() 
                            if j.completion_status in ['completed', 'failed']]
        
        if not completed_journeys:
            return {'tracked_tasks': len(self.task_journeys), 'completed_tasks': 0}
        
        avg_satellites = np.mean([len(j.journey_stages) for j in completed_journeys])
        avg_latency = np.mean([j.end_to_end_latency_s for j in completed_journeys])
        avg_energy = np.mean([j.total_energy_cost_j for j in completed_journeys])
        completion_rate = len([j for j in completed_journeys 
                             if j.completion_status == 'completed']) / len(completed_journeys)
        
        return {
            'tracked_tasks': len(self.task_journeys),
            'completed_tasks': len(completed_journeys),
            'average_satellites_per_task': avg_satellites,
            'average_latency_s': avg_latency,
            'average_energy_cost_j': avg_energy,
            'completion_rate': completion_rate
        }


class EpisodeTracker:
    """Episode级别性能追踪器"""
    
    def __init__(self):
        """初始化Episode追踪器"""
        self.reset()
    
    def reset(self):
        """重置追踪状态"""
        self.start_time = time.time()
        self.timeslot_records = []
        self.system_metrics = {
            'tasks_generated': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_energy_consumed': 0.0,
            'total_latency': 0.0
        }
    
    def record_timeslot(self, timeslot: int, observations: Dict, 
                       actions: Dict, rewards: Dict, infos: Dict):
        """
        记录时隙信息
        
        Args:
            timeslot: 时隙编号
            observations: 观测字典
            actions: 动作字典
            rewards: 奖励字典
            infos: 信息字典
        """
        # 计算系统级指标
        total_queue_length = sum([len(obs) for obs in observations.values() if hasattr(obs, '__len__')])
        avg_energy = np.mean([obs[4] for obs in observations.values() if len(obs) > 4])
        total_reward = sum(rewards.values())
        
        record = {
            'timeslot': timeslot,
            'timestamp': time.time(),
            'active_agents': len(observations),
            'total_queue_length': total_queue_length,
            'average_energy_ratio': avg_energy,
            'total_reward': total_reward,
            'actions_taken': dict(actions),
            'system_health': all([obs[14] > 0.5 for obs in observations.values() if len(obs) > 14])
        }
        
        self.timeslot_records.append(record)
    
    def get_episode_summary(self) -> Dict[str, Any]:
        """获取Episode总结"""
        if not self.timeslot_records:
            return {}
        
        duration = time.time() - self.start_time
        total_timeslots = len(self.timeslot_records)
        
        avg_queue = np.mean([r['total_queue_length'] for r in self.timeslot_records])
        avg_energy = np.mean([r['average_energy_ratio'] for r in self.timeslot_records])
        total_reward = sum([r['total_reward'] for r in self.timeslot_records])
        
        return {
            'episode_duration_s': duration,
            'total_timeslots': total_timeslots,
            'average_queue_length': avg_queue,
            'average_energy_ratio': avg_energy,
            'total_cumulative_reward': total_reward,
            'system_stability': np.mean([r['system_health'] for r in self.timeslot_records])
        }


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, output_dir: str = "results"):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def export_energy_timeline(self, energy_tracker: SatelliteEnergyTracker,
                             episode_id: int):
        """导出能量时间线数据"""
        filename = os.path.join(self.output_dir, f"energy_timeline_ep{episode_id}.csv")
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            if energy_tracker.energy_timeline:
                writer = csv.DictWriter(f, fieldnames=energy_tracker.energy_timeline[0].__dict__.keys())
                writer.writeheader()
                for record in energy_tracker.energy_timeline:
                    writer.writerow(asdict(record))
    
    def export_task_journeys(self, task_tracker: TaskJourneyTracker,
                           episode_id: int):
        """导出任务生命周期数据"""
        filename = os.path.join(self.output_dir, f"task_journeys_ep{episode_id}.json")
        
        journeys_data = {}
        for task_id, journey in task_tracker.task_journeys.items():
            journeys_data[task_id] = {
                'basic_info': {
                    'task_id': journey.task_id,
                    'source_timeslot': journey.source_timeslot,
                    'source_location_id': journey.source_location_id,
                    'data_size_mb': journey.data_size_mb,
                    'priority': journey.priority,
                    'deadline': journey.deadline,
                    'completion_status': journey.completion_status,
                    'end_to_end_latency_s': journey.end_to_end_latency_s,
                    'total_energy_cost_j': journey.total_energy_cost_j
                },
                'stages': [asdict(stage) for stage in journey.journey_stages]
            }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(journeys_data, f, indent=2, ensure_ascii=False)
    
    def generate_episode_report(self, episode_id: int, episode_tracker: EpisodeTracker,
                              energy_tracker: SatelliteEnergyTracker,
                              task_tracker: TaskJourneyTracker) -> str:
        """生成Episode报告"""
        episode_summary = episode_tracker.get_episode_summary()
        energy_summary = energy_tracker.get_energy_summary()
        journey_summary = task_tracker.get_journey_summary()
        
        report = f"""
=== Episode {episode_id} 性能报告 ===
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 系统性能:
   - 仿真时长: {episode_summary.get('episode_duration_s', 0):.1f}s
   - 总时隙数: {episode_summary.get('total_timeslots', 0)}
   - 平均队列长度: {episode_summary.get('average_queue_length', 0):.2f}
   - 平均能量水平: {episode_summary.get('average_energy_ratio', 0):.2%}
   - 累积奖励: {episode_summary.get('total_cumulative_reward', 0):.2f}
   - 系统稳定性: {episode_summary.get('system_stability', 0):.2%}

🛰️ 追踪卫星[{energy_tracker.target_satellite}]:
   - 平均电量: {energy_summary.get('average_energy_ratio', 0):.2%}
   - 最低电量: {energy_summary.get('min_energy_ratio', 0):.2%}
   - 最高电量: {energy_summary.get('max_energy_ratio', 0):.2%}
   - 能量方差: {energy_summary.get('energy_variance', 0):.6f}
   - 低电量时段: {energy_summary.get('low_energy_periods', 0)}
   - 光照比例: {energy_summary.get('illumination_ratio', 0):.2%}

📋 任务生命周期分析:
   - 追踪任务数: {journey_summary.get('tracked_tasks', 0)}
   - 完成任务数: {journey_summary.get('completed_tasks', 0)}
   - 平均经历卫星数: {journey_summary.get('average_satellites_per_task', 0):.2f}
   - 平均端到端延迟: {journey_summary.get('average_latency_s', 0):.2f}s
   - 平均能量成本: {journey_summary.get('average_energy_cost_j', 0):.2f}J
   - 任务完成率: {journey_summary.get('completion_rate', 0):.2%}
"""
        
        # 保存报告
        report_filename = os.path.join(self.output_dir, f"episode_{episode_id}_report.txt")
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report


class RandomBaselineRunner:
    """随机策略基准测试主控制器"""
    
    def __init__(self, config_file: str = "src/env/config.yaml", 
                 target_satellite: str = "Satellite111",
                 policy_type: str = "uniform"):
        """
        初始化基准测试运行器
        
        Args:
            config_file: 配置文件路径
            target_satellite: 追踪的目标卫星
            policy_type: 随机策略类型
        """
        self.config_file = config_file
        self.target_satellite = target_satellite
        
        # 初始化组件
        self.env = None
        self.policy = RandomPolicy(policy_type)
        self.energy_tracker = SatelliteEnergyTracker(target_satellite)
        self.task_tracker = TaskJourneyTracker()
        self.episode_tracker = EpisodeTracker()
        self.reporter = PerformanceReporter()
        
        print(f"🚀 随机策略基准测试器初始化完成")
        print(f"   - 目标卫星: {target_satellite}")
        print(f"   - 策略类型: {policy_type}")
        print(f"   - 配置文件: {config_file}")
    
    def run_episodes(self, num_episodes: int = 3, max_timeslots: int = 100) -> Dict[str, Any]:
        """
        运行多个Episodes
        
        Args:
            num_episodes: Episode数量
            max_timeslots: 每个Episode最大时隙数
            
        Returns:
            Dict: 综合性能报告
        """
        print(f"\n🎯 开始运行 {num_episodes} 个Episodes，每个最大 {max_timeslots} 时隙")
        
        all_results = []
        
        for episode_id in range(1, num_episodes + 1):
            print(f"\n{'='*50}")
            print(f"🎮 Episode {episode_id}/{num_episodes} 开始")
            print(f"{'='*50}")
            
            episode_result = self.run_single_episode(episode_id, max_timeslots)
            all_results.append(episode_result)
            
            print(f"\n✅ Episode {episode_id} 完成")
        
        # 生成综合报告
        combined_report = self._generate_combined_report(all_results)
        print(f"\n🎉 所有Episodes完成！综合结果已保存到 {self.reporter.output_dir}")
        
        return combined_report
    
    def run_single_episode(self, episode_id: int, max_timeslots: int = 100) -> Dict[str, Any]:
        """
        运行单个Episode
        
        Args:
            episode_id: Episode ID
            max_timeslots: 最大时隙数
            
        Returns:
            Dict: Episode结果
        """
        # 重置环境和追踪器
        if self.env is None:
            self.env = SatelliteParallelEnv(self.config_file)
        
        self.energy_tracker = SatelliteEnergyTracker(self.target_satellite)
        self.task_tracker = TaskJourneyTracker()
        self.episode_tracker.reset()
        
        try:
            # 重置环境
            observations, infos = self.env.reset(seed=episode_id * 42)
            print(f"   环境重置完成: {len(observations)} 个卫星")
            
            timeslot = 0
            total_tasks_tracked = 0
            
            while timeslot < max_timeslots and self.env.agents:
                current_time = timeslot * 10.0  # 假设每时隙10秒
                
                # 获取动作
                actions = {}
                for agent in self.env.agents:
                    valid_actions = infos[agent].get('valid_actions', [0])
                    action = self.policy.get_action(agent, valid_actions, observations[agent])
                    actions[agent] = action
                
                # 执行步进
                observations, rewards, dones, infos = self.env.step(actions)
                
                # 追踪目标卫星能量
                if self.target_satellite in self.env.env_core.satellites:
                    target_sat = self.env.env_core.satellites[self.target_satellite]
                    energy_record = self.energy_tracker.record_energy_state(
                        timeslot, current_time, target_sat
                    )
                
                # 追踪第一个任务
                if timeslot < 50:  # 只在前50个时隙追踪，避免过多数据
                    self._track_first_task_if_available(timeslot)
                    if timeslot in self.task_tracker.tracked_tasks:
                        total_tasks_tracked += 1
                
                # 记录Episode信息
                self.episode_tracker.record_timeslot(timeslot, observations, actions, rewards, infos)
                
                # 实时输出（每10个时隙）
                if timeslot % 10 == 0:
                    self._print_timeslot_status(timeslot, current_time, target_sat if self.target_satellite in self.env.env_core.satellites else None)
                
                timeslot += 1
                
                if any(dones.values()):
                    break
            
            print(f"\n   Episode {episode_id} 执行完成: {timeslot} 时隙, 追踪 {total_tasks_tracked} 个任务")
            
            # 生成报告
            report = self.reporter.generate_episode_report(
                episode_id, self.episode_tracker, self.energy_tracker, self.task_tracker
            )
            print(report)
            
            # 导出数据
            self.reporter.export_energy_timeline(self.energy_tracker, episode_id)
            self.reporter.export_task_journeys(self.task_tracker, episode_id)
            
            return {
                'episode_id': episode_id,
                'timeslots_completed': timeslot,
                'episode_summary': self.episode_tracker.get_episode_summary(),
                'energy_summary': self.energy_tracker.get_energy_summary(),
                'journey_summary': self.task_tracker.get_journey_summary()
            }
            
        except Exception as e:
            print(f"❌ Episode {episode_id} 执行失败: {e}")
            import traceback
            traceback.print_exc()
            return {'episode_id': episode_id, 'error': str(e)}
        finally:
            if self.env:
                self.env.close()
    
    def _track_first_task_if_available(self, timeslot: int):
        """追踪时隙的第一个任务"""
        try:
            if hasattr(self.env.env_core, 'task_adapter'):
                # 尝试获取该时隙的任务
                tasks = self.env.env_core.task_adapter.load_tasks_for_timeslot(timeslot)
                if tasks:
                    first_task = tasks[0]
                    success = self.task_tracker.register_first_task(timeslot, first_task)
                    if success:
                        print(f"   📋 追踪任务: 时隙{timeslot} -> Task{first_task.task_id}")
        except Exception as e:
            pass  # 静默处理追踪失败
    
    def _print_timeslot_status(self, timeslot: int, current_time: float, target_satellite):
        """打印时隙状态"""
        if target_satellite:
            energy_ratio = target_satellite.energy_state.battery_ratio
            illuminated = "☀️" if target_satellite.energy_state.is_illuminated else "🌙"
            queue_len = len(target_satellite.task_queue)
            running_tasks = len(target_satellite.running_tasks)
            
            print(f"   🕒 时隙 {timeslot:3d} | 时间 {current_time:6.1f}s | "
                  f"🛰️ {self.target_satellite}: 电量 {energy_ratio:.1%} {illuminated} | "
                  f"队列 {queue_len:2d} | 运行 {running_tasks:2d}")
        else:
            print(f"   🕒 时隙 {timeslot:3d} | 时间 {current_time:6.1f}s | 目标卫星不可用")
    
    def _generate_combined_report(self, all_results: List[Dict]) -> Dict[str, Any]:
        """生成综合报告"""
        if not all_results:
            return {}
        
        valid_results = [r for r in all_results if 'error' not in r]
        
        if not valid_results:
            return {'error': 'No valid episodes completed'}
        
        # 聚合统计
        avg_timeslots = np.mean([r['timeslots_completed'] for r in valid_results])
        
        combined_report = {
            'total_episodes': len(all_results),
            'successful_episodes': len(valid_results),
            'average_timeslots_completed': avg_timeslots,
            'policy_type': self.policy.policy_type,
            'target_satellite': self.target_satellite
        }
        
        # 如果有有效结果，计算平均性能
        if valid_results:
            episode_summaries = [r.get('episode_summary', {}) for r in valid_results if r.get('episode_summary')]
            energy_summaries = [r.get('energy_summary', {}) for r in valid_results if r.get('energy_summary')]
            journey_summaries = [r.get('journey_summary', {}) for r in valid_results if r.get('journey_summary')]
            
            if episode_summaries:
                combined_report['average_system_performance'] = {
                    'avg_queue_length': np.mean([s.get('average_queue_length', 0) for s in episode_summaries]),
                    'avg_energy_ratio': np.mean([s.get('average_energy_ratio', 0) for s in episode_summaries]),
                    'avg_total_reward': np.mean([s.get('total_cumulative_reward', 0) for s in episode_summaries])
                }
            
            if energy_summaries:
                combined_report['average_energy_performance'] = {
                    'avg_energy_ratio': np.mean([s.get('average_energy_ratio', 0) for s in energy_summaries]),
                    'avg_min_energy': np.mean([s.get('min_energy_ratio', 0) for s in energy_summaries]),
                    'avg_illumination_ratio': np.mean([s.get('illumination_ratio', 0) for s in energy_summaries])
                }
            
            if journey_summaries:
                combined_report['average_task_performance'] = {
                    'avg_completion_rate': np.mean([s.get('completion_rate', 0) for s in journey_summaries]),
                    'avg_latency': np.mean([s.get('average_latency_s', 0) for s in journey_summaries]),
                    'avg_energy_cost': np.mean([s.get('average_energy_cost_j', 0) for s in journey_summaries])
                }
        
        # 保存综合报告
        report_filename = os.path.join(self.reporter.output_dir, "combined_report.json")
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(combined_report, f, indent=2, ensure_ascii=False)
        
        return combined_report


def main():
    """主函数"""
    print("🌟 SPACE-OAAL 随机策略基准测试")
    print("=" * 60)
    
    # 检查环境
    config_file = "src/env/config.yaml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("请确保在项目根目录下运行此脚本")
        return
    
    try:
        # 创建测试运行器
        runner = RandomBaselineRunner(
            config_file=config_file,
            target_satellite="Satellite111",
            policy_type="uniform"
        )
        
        # 运行测试
        results = runner.run_episodes(num_episodes=3, max_timeslots=50)
        
        print("\n🎯 测试完成！主要结果:")
        print(f"   - 成功Episodes: {results.get('successful_episodes', 0)}/{results.get('total_episodes', 0)}")
        print(f"   - 平均完成时隙: {results.get('average_timeslots_completed', 0):.1f}")
        print(f"   - 策略类型: {results.get('policy_type', 'unknown')}")
        print(f"   - 追踪卫星: {results.get('target_satellite', 'unknown')}")
        
        if 'average_system_performance' in results:
            perf = results['average_system_performance']
            print(f"   - 平均队列长度: {perf.get('avg_queue_length', 0):.2f}")
            print(f"   - 平均能量水平: {perf.get('avg_energy_ratio', 0):.2%}")
            print(f"   - 平均累积奖励: {perf.get('avg_total_reward', 0):.2f}")
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()