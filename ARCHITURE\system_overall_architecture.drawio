<mxfile host="app.diagrams.net" modified="2025-01-23T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="SPACE-OAAL系统整体架构图" id="system-overall-architecture">
    <mxGraphModel dx="2400" dy="1400" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1900" pageHeight="1400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 系统入口层 -->
        <mxCell id="system-entry" value="SPACE-OAAL&#xa;系统主入口&#xa;仿真控制器" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="850" y="50" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 数据源层 -->
        <mxCell id="data-source-group" value="数据源层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="200" width="1800" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-data-csv" value="satellite_processed_data.csv&#xa;36颗卫星×1000时隙轨道数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="data-source-group">
          <mxGeometry x="50" y="40" width="200" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="ground-stations-csv" value="updated_global_ground_stations.csv&#xa;420个地面站坐标数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="data-source-group">
          <mxGeometry x="280" y="40" width="220" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="task-generation-json" value="task_generation_results.json&#xa;任务生成数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="data-source-group">
          <mxGeometry x="530" y="40" width="200" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="config-yaml" value="config.yaml&#xa;系统配置参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=11" vertex="1" parent="data-source-group">
          <mxGeometry x="760" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        
        <!-- 适配器集成层 -->
        <mxCell id="adapter-integration-group" value="适配器集成层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="340" width="1100" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-manager" value="IntegrationManager&#xa;集成管理器&#xa;- 系统总控制器&#xa;- 仿真执行引擎&#xa;- 模块协调中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" vertex="1" parent="adapter-integration-group">
          <mxGeometry x="450" y="40" width="200" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter" value="SatelliteAdapter&#xa;卫星适配器&#xa;- 轨道数据适配&#xa;- 通信状态同步" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11" vertex="1" parent="adapter-integration-group">
          <mxGeometry x="50" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter" value="TaskAdapter&#xa;任务适配器&#xa;- 任务格式转换&#xa;- 时隙任务生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11" vertex="1" parent="adapter-integration-group">
          <mxGeometry x="250" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        
        <!-- 核心业务层 -->
        <mxCell id="core-business-group" value="核心业务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="500" width="1800" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-updater-module" value="OrbitalUpdater&#xa;轨道更新模块&#xa;- 卫星轨道计算&#xa;- 可见性矩阵构建&#xa;- 地面覆盖分析&#xa;- 速度计算" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="core-business-group">
          <mxGeometry x="50" y="50" width="200" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="communication-module" value="CommunicationManager&#xa;通信管理模块&#xa;- 链路状态计算&#xa;- 信号强度分析&#xa;- 网络拓扑管理&#xa;- 通信质量评估" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="core-business-group">
          <mxGeometry x="280" y="50" width="200" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="task-module" value="Task System&#xa;任务系统模块&#xa;- 任务生命周期管理&#xa;- 动态优先级计算&#xa;- 状态机控制&#xa;- 性能统计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="core-business-group">
          <mxGeometry x="510" y="50" width="200" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-module" value="SatelliteNode&#xa;卫星实体模块&#xa;- 卫星状态管理&#xa;- 行为执行引擎&#xa;- 资源分配控制&#xa;- 观测接口提供" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="core-business-group">
          <mxGeometry x="740" y="50" width="200" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="rl-interface" value="RL Interface&#xa;强化学习接口&#xa;- 观测空间定义&#xa;- 动作空间映射&#xa;- 奖励函数计算&#xa;- 环境状态导出" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11;fontStyle=1" vertex="1" parent="core-business-group">
          <mxGeometry x="970" y="50" width="200" height="90" as="geometry" />
        </mxCell>
        
        <!-- 数据实体层 -->
        <mxCell id="data-entity-group" value="数据实体层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="700" width="1700" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-entity" value="Satellite&#xa;轨道卫星实体" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="50" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="ground-station-entity" value="GroundStation&#xa;地面站实体" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="190" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="link-state-entity" value="LinkState&#xa;链路状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="330" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="network-state-entity" value="NetworkState&#xa;网络状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="470" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="task-entity" value="Task/EnhancedTask&#xa;任务实体" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="610" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-node-entity" value="SatelliteNode&#xa;卫星节点实体" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="770" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="position-entity" value="Position&#xa;位置状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="930" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="energy-entity" value="EnergyState&#xa;能量状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="data-entity-group">
          <mxGeometry x="1070" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- 接口服务层 -->
        <mxCell id="interface-service-group" value="接口服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="150" y="840" width="1600" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-interface" value="轨道计算接口&#xa;get_satellites_at_time()&#xa;calculate_velocity()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="50" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="communication-interface" value="通信管理接口&#xa;get_network_state()&#xa;calculate_link_properties()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="220" y="40" width="170" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="task-interface" value="任务管理接口&#xa;generate_tasks()&#xa;update_task_state()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="410" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-interface" value="卫星控制接口&#xa;step()&#xa;process_tasks()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="580" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="observation-interface" value="观测接口&#xa;export_observation_data()&#xa;get_system_state()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="750" y="40" width="170" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="scheduling-interface" value="调度接口&#xa;export_for_scheduling()&#xa;assign_tasks()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="interface-service-group">
          <mxGeometry x="940" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        
        <!-- 主要控制流连线 -->
        <!-- 系统入口到集成管理器 -->
        <mxCell id="entry-to-integration" value="系统启动" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" edge="1" parent="1" source="system-entry" target="integration-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 集成管理器到适配器 -->
        <mxCell id="integration-to-satellite-adapter" value="卫星状态同步" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="satellite-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-to-task-adapter" value="任务生成管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="task-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器到核心模块 -->
        <mxCell id="satellite-adapter-to-orbital" value="轨道数据获取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="orbital-updater-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-comm" value="通信状态获取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="communication-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-satellite" value="卫星节点创建" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="satellite-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-task" value="任务转换" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="task-adapter" target="task-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 核心模块间的交互 -->
        <mxCell id="orbital-to-comm" value="可见性矩阵" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="orbital-updater-module" target="communication-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-to-satellite" value="任务分配" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="task-module" target="satellite-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-to-rl" value="状态观测" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-module" target="rl-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 数据文件到模块的连接 -->
        <mxCell id="satellite-data-to-orbital" value="轨道数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-data-csv" target="orbital-updater-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="ground-data-to-orbital" value="地面站数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="ground-stations-csv" target="orbital-updater-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-data-to-task" value="任务数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-generation-json" target="task-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="config-to-integration" value="系统配置" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#7030a0;dotted=1;fontSize=10" edge="1" parent="1" source="config-yaml" target="integration-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 模块到数据实体的连接 -->
        <mxCell id="orbital-to-entities" value="创建实体" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="orbital-updater-module" target="satellite-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="comm-to-entities" value="创建状态" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="communication-module" target="link-state-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-to-entities" value="创建任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-module" target="task-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-to-entities" value="状态管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-module" target="satellite-node-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 模块到接口的连接 -->
        <mxCell id="orbital-to-interface" value="提供接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="orbital-updater-module" target="orbital-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="comm-to-interface" value="提供接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="communication-module" target="communication-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-to-interface" value="提供接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-module" target="task-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-to-interface" value="提供接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-module" target="satellite-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="rl-to-interfaces" value="提供接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="rl-interface" target="observation-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend-group" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="250" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-main-flow" value="粗绿线：主控制流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-group">
          <mxGeometry x="10" y="30" width="120" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-module-call" value="实线箭头：模块调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-group">
          <mxGeometry x="10" y="50" width="120" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-data-flow" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-group">
          <mxGeometry x="10" y="70" width="120" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-config" value="紫色点线：配置加载" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="legend-group">
          <mxGeometry x="10" y="90" width="120" height="15" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>