<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="任务模块架构图" id="task-system-architecture">
    <mxGraphModel dx="5924" dy="3256" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="main()&#xa;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="770" y="-30" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="管理控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="390" y="90" width="900" height="190" as="geometry" />
        </mxCell>
        <mxCell id="7" value="TaskLoader&#xa;任务数据加载器&#xa;- JSON数据解析&#xa;- 时隙任务提取&#xa;- Task实例创建" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="6" vertex="1">
          <mxGeometry x="150" y="50" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="8" value="TaskManager&#xa;任务管理器&#xa;- 活跃任务管理&#xa;- 状态跟踪&#xa;- 调度器接口&#xa;- 系统统计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="6" vertex="1">
          <mxGeometry x="450" y="50" width="200" height="110" as="geometry" />
        </mxCell>
        <mxCell id="9" value="数据模型层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="230" y="500" width="1150" height="200" as="geometry" />
        </mxCell>
        <mxCell id="10" value="TaskState&#xa;任务状态枚举&#xa;- GENERATED&#xa;- QUEUED&#xa;- PROCESSING&#xa;- TRANSFERRING&#xa;- RETRYING&#xa;- RETURNING&#xa;- COMPLETED&#xa;- FAILED" style="rhombus;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="9" vertex="1">
          <mxGeometry x="30" y="30" width="160" height="160" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Task&#xa;任务实体核心类&#xa;- task_id: str&#xa;- source_location_id: int&#xa;- data_size_mb: float&#xa;- complexity_cycles_per_bit: int&#xa;- deadline_timestamp: float&#xa;- priority: int&#xa;- state: TaskState&#xa;- processing_records: List&#xa;- transfer_records: List&#xa;- retry_record: RetryRecord" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="9" vertex="1">
          <mxGeometry x="220" y="40" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="12" value="ProcessingRecord&#xa;@dataclass&#xa;处理记录&#xa;- satellite_id: str&#xa;- start_time: float&#xa;- end_time: float&#xa;- cpu_cycles_processed: int&#xa;- energy_consumed: float&#xa;- completion_ratio: float&#xa;- is_partial: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="9" vertex="1">
          <mxGeometry x="490" y="40" width="180" height="150" as="geometry" />
        </mxCell>
        <mxCell id="13" value="TransferRecord&#xa;@dataclass&#xa;传输记录&#xa;- from_satellite_id: Optional[str]&#xa;- to_satellite_id: Optional[str]&#xa;- transfer_time: float&#xa;- transfer_energy: float&#xa;- success: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="9" vertex="1">
          <mxGeometry x="700" y="40" width="180" height="150" as="geometry" />
        </mxCell>
        <mxCell id="14" value="RetryRecord&#xa;@dataclass&#xa;重试记录&#xa;- retry_count: int = 0&#xa;- last_retry_time: float = 0.0&#xa;- retry_reason: str = &quot;&quot;&#xa;- max_retries: int = 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="9" vertex="1">
          <mxGeometry x="930" y="40" width="150" height="150" as="geometry" />
        </mxCell>
        <mxCell id="15" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-18" y="800" width="1690" height="100" as="geometry" />
        </mxCell>
        <mxCell id="16" value="load_tasks_for_timeslot()&#xa;按时隙加载任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="68" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="17" value="update_state()&#xa;任务状态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="318" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="18" value="calculate_dynamic_priority()&#xa;动态优先级计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="568" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="19" value="start_processing()&#xa;开始任务处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="818" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="20" value="transfer_to_satellite()&#xa;卫星间传输" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="1058" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="21" value="attempt_retry()&#xa;重试尝试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="1308" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="22" value="complete_processing()&#xa;完成处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="15" vertex="1">
          <mxGeometry x="1518" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="23" value="业务服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="227" y="1020" width="1200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="24" value="get_high_priority_tasks()&#xa;高优先级任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="50" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="25" value="get_tasks_by_satellite()&#xa;按卫星分组查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="250" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="26" value="export_tasks_for_scheduling()&#xa;调度器接口导出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="450" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="27" value="get_overdue_tasks()&#xa;超期任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="670" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="28" value="get_tasks_by_state()&#xa;按状态查询任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="850" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="29" value="get_system_statistics()&#xa;系统统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="23" vertex="1">
          <mxGeometry x="1030" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="30" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="327" y="1290" width="1000" height="100" as="geometry" />
        </mxCell>
        <mxCell id="31" value="_load_config()&#xa;配置文件加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="30" vertex="1">
          <mxGeometry x="63" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="32" value="to_dict()&#xa;字典格式转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="30" vertex="1">
          <mxGeometry x="293" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="33" value="get_statistics()&#xa;任务统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="30" vertex="1">
          <mxGeometry x="573" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="34" value="get_resource_requirements()&#xa;资源需求查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="30" vertex="1">
          <mxGeometry x="803" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="35" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="370" y="-150" width="900" height="100" as="geometry" />
        </mxCell>
        <mxCell id="36" value="w_priority&#xa;优先级权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="50" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="37" value="w_urgency&#xa;紧迫性权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="180" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="38" value="w_cost&#xa;成本权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="310" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="39" value="f_leo_hz&#xa;LEO计算频率" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="440" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="40" value="max_retries&#xa;最大重试次数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="570" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="41" value="epsilon_urgency&#xa;紧迫性防零参数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="35" vertex="1">
          <mxGeometry x="700" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="42" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="2" target="8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="927" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="44" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="940" y="1535" as="sourcePoint" />
            <mxPoint x="450" y="1370" as="targetPoint" />
            <Array as="points">
              <mxPoint x="890" y="1535" />
              <mxPoint x="890" y="1430" />
              <mxPoint x="450" y="1430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="45" value="创建任务对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="7" target="11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="46" value="管理任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="8" target="11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="360" />
              <mxPoint x="610" y="360" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="47" value="记录处理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="11" target="12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="48" value="记录传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="11" target="13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="550" y="750" />
              <mxPoint x="1020" y="750" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="49" value="记录重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="11" target="14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="600" y="780" />
              <mxPoint x="1215" y="780" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="50" value="主要数据流&#xa;(任务生命周期)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="7" target="16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="180" />
              <mxPoint x="500" y="350" />
              <mxPoint x="120" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="51" value="状态管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="16" target="17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="52" value="优先级计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="17" target="18" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="53" value="处理决策" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="18" target="19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="54" value="任务传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="19" target="20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="55" value="失败重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="20" target="21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="56" value="任务完成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="21" target="22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="57" value="查询调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="8" target="24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="60" />
              <mxPoint x="310" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="58" value="分组查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="8" target="25" edge="1">
          <mxGeometry x="-0.6566" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="940" y="950" />
              <mxPoint x="557" y="950" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="59" value="调度接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1" parent="1" source="8" target="26" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="940" y="360" />
              <mxPoint x="1690" y="360" />
              <mxPoint x="1690" y="970" />
              <mxPoint x="767" y="970" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="60" value="统计查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="8" target="29" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1330" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="61" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="18" target="34" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="650" y="1150" />
              <mxPoint x="1210" y="1150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="62" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="11" target="33" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-90" y="455" />
              <mxPoint x="-90" y="1180" />
              <mxPoint x="970" y="1180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="63" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="50" y="-100" width="200" height="220" as="geometry" />
        </mxCell>
        <mxCell id="64" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="40" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="65" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="65" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="66" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="90" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="67" value="深蓝色：管理控制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="115" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="68" value="绿色：数据实体" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="140" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="69" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="165" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="70" value="紫色：配置参数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="63" vertex="1">
          <mxGeometry x="10" y="190" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="3" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="447" y="1470" width="760" height="100" as="geometry" />
        </mxCell>
        <mxCell id="4" value="task_generation_results.json&#xa;预生成任务数据&#xa;包含1000时隙任务" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="3" vertex="1">
          <mxGeometry x="80" y="40" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="5" value="config.yaml&#xa;系统配置文件&#xa;排队/计算/通信参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="3" vertex="1">
          <mxGeometry x="473" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="43" value="数据读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="4" target="7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="406.89" y="1510" as="sourcePoint" />
            <mxPoint x="296.89" y="250" as="targetPoint" />
            <Array as="points">
              <mxPoint x="580" y="1510" />
              <mxPoint x="580" y="1225" />
              <mxPoint x="-160" y="1225" />
              <mxPoint x="-160" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
