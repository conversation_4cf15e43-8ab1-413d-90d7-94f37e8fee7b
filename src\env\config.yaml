# ============================================================================
# SPACE-OAAL 仿真环境配置文件
# ============================================================================

# ============================================================================
# 系统架构参数 (System Architecture Parameters)
# ============================================================================
system:
  # 基础系统配置
  num_users: 420                    # 地面用户终端数量
  num_leo_satellites: 36            # LEO卫星数量 (6×6 Walker Delta星座)
  num_geo_satellites: 3             # GEO卫星数量
  num_policy_domains: 24            # 策略域数量
  walker_delta_m: 6                 # Walker星座轨道数
  walker_delta_n: 6                 # 每轨道卫星数

  # 轨道参数
  leo_altitude_m: 1200000           # LEO卫星轨道高度 (m)
  geo_altitude_m: 35786000          # GEO卫星轨道高度 (m)
  earth_radius_m: 6371000           # 地球半径 (m)
  
  # 时间参数
  timeslot_duration_s: 10           # 时隙持续时间 (秒)
  total_timeslots: 1000             # 总时隙数 (恢复完整1000时隙以保证训练统一性)
  simulation_time_s: 10000          # 总仿真时间 (秒) = 1000时隙 × 10秒/时隙
  # 可见距离
  visibility_threshold_m: 5000000    # 卫星间可见性阈值距离 (m)
  visibility_earth_m: 2500000       # 地面站-卫星可见性阈值距离 (m)
  cloud_visibility_threshold_m: 3000000  # 云中心-卫星可见性阈值距离 (m)

# ============================================================================
# 通信模型参数 (Communication Model Parameters)
# ============================================================================
communication:
  # 射频通信参数
  rf_noise_power_w: 1.0e-13         # 射频信道背景噪声 (W)
  rf_carrier_freq_hz: 2400000000    # 射频载波频率 (Hz)
  rician_k_factor: 10               # 莱斯K因子

  # 用户-卫星上行链路
  b_us_hz: 100000000                # 用户-卫星上行带宽 (Hz)
  p_u_w: 5                          # 用户终端发射功率 (W)

  # 卫星-用户下行链路  
  b_su_hz: 120000000                # 卫星-用户下行带宽 (Hz)
  p_su_w: 10                        # 卫星对用户发射功率 (W)

  # 卫星-云中心下行链路
  b_sc_hz: 150000000                # 卫星-云下行带宽 (Hz)
  p_sc_w: 15                        # 卫星对云发射功率 (W)

  # 云中心-卫星上行链路
  b_cs_hz: 180000000                # 云-卫星上行带宽 (Hz) 
  p_c_w: 50                         # 云中心发射功率 (W)

  # 星间激光链路参数
  isl_tra_rate: 50000000000         # 星间链路传输速率 (bps)

  # 物理常数
  light_speed_ms: 299792458         # 光速 (m/s)

  # 单位转换
  mb_to_bits: 8388608               # MB到bits转换系数
  
  # 通信链路参数
  antenna_gain_db: 20.0             # 天线增益 (dB)
  system_noise_dbm_hz: -174.0       # 系统噪声功率密度 (dBm/Hz)
  implementation_loss_db: 2.0       # 实现损耗 (dB)
  rain_fade_margin_db: 3.0          # 雨衰余量 (dB)
  coding_efficiency: 0.7            # 编码效率
  
  # 协议参数
  max_retries: 3                    # 最大重传次数
  timeout_threshold_ms: 1000        # 通信超时阈值 (ms)
  processing_delay_ms: 5.0          # 处理延迟 (ms)

# ============================================================================
# 计算模型参数 (Computation Model Parameters)
# ============================================================================
computation:
  # LEO卫星计算资源
  f_leo_hz: 1000e9                   # LEO卫星CPU频率 (Hz) - 提升到100GHz以加快任务处理

  # 云计算资源
  f_cloud_hz: 100e9                 # 云中心CPU频率 (Hz)

  # 能效参数
  zeta_leo: 1.0e-28                 # LEO卫星能效系数
  zeta_cloud: 1.0e-30               # 云中心能效系数

  # 电池和能量约束
  leo_battery_capacity_j: 3600000   # LEO卫星电池容量 (J)
  leo_solar_power_w: 500            # LEO卫星太阳能板功率 (W)
  energy_threshold_ratio: 0.2       # 能量阈值比例

  # 并行处理参数 (Parallel Processing Parameters)
  max_parallel_tasks: 200             # 最大并行任务数
  cpu_allocation_levels: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]  # CPU分配档位(%)
  min_cpu_allocation: 10            # 最小CPU分配比例 (%)
  max_cpu_allocation: 100           # 最大CPU分配比例 (%)
  cpu_allocation_step: 10           # CPU分配步长 (%)

# ============================================================================
# 排队模型参数 (Queuing Model Parameters)
# ============================================================================
queuing:
  # 动态优先级评分权重
  w_priority: 1.0                   # 优先级因子权重
  w_urgency: 2.0                    # 紧迫性因子权重
  w_cost: 0.5                       # 成本因子权重

  # 队列管理参数
  max_queue_size: 100               # 最大队列长度
  queue_timeout_s: 300              # 队列超时时间 (秒)
  epsilon_urgency: 1.0e-6           # 紧迫性计算防零参数

  # 调度参数
  scheduling_interval_s: 1          # 调度间隔 (秒)
  preemption_enabled: true          # 是否启用抢占
  priority_aging_factor: 0.01       # 优先级老化因子

# ============================================================================
# 强化学习参数 (Reinforcement Learning Parameters)
# ============================================================================
reinforcement_learning:
  # DPPO算法超参数
  gamma: 0.99                       # 折扣因子
  lambda_gae: 0.95                  # GAE参数
  clip_ratio: 0.2                   # PPO裁剪比例
  target_kl: 0.01                   # 目标KL散度
  v_coeff: 0.5                      # 价值函数损失系数
  v_thres: 1.0e-4                   # 价值函数收敛阈值
  entropy_coeff: 0.01               # 熵正则化系数

  # 学习率
  lr_actor: 3.0e-4                  # Actor网络学习率
  lr_critic: 1.0e-3                 # Critic网络学习率
  lr_geo: 1.0e-4                    # GEO网络学习率

  # 网络更新参数
  n_update_actor: 80                # Actor网络更新次数
  n_update_critic: 80               # Critic网络更新次数
  n_minibatch: 128                  # 小批量大小 (修复：512过大，改回128确保训练触发)
  buffer_size: 50000                # 经验回放缓冲区大小 (优化：10000->50000)

  # 网络架构参数 (优化：增加网络复杂度)
  hidden_sizes_actor: [256, 256, 256, 256]    # Actor网络隐藏层 (增加到4层)
  hidden_sizes_critic: [256, 256, 256, 256]   # Critic网络隐藏层 (增加到4层)
  activation: "relu"                # 激活函数
  
  # 训练频率优化
  train_every_step: true            # 每步都训练 (新增参数)

  # GEO辅助参数
  beta_geo_guidance: 0.3            # GEO引导系数
  geo_update_interval: 100          # GEO更新间隔

# ============================================================================
# 奖励函数参数 (Reward Function Parameters)
# ============================================================================
reward:
  # 奖励函数权重
  w_delay: 1.0                      # 延迟惩罚权重
  w_energy: 0.5                     # 能耗惩罚权重
  w_penalty: 2.0                    # 失败惩罚权重
  w_success: 10.0                   # 成功奖励权重
  w_load_balance: 0.3               # 负载均衡权重

  # 惩罚参数
  task_failure_penalty: -100        # 任务失败惩罚
  deadline_miss_penalty: -50        # 截止时间错过惩罚
  energy_depletion_penalty: -200    # 能量耗尽惩罚

  # 奖励缩放参数
  reward_scale_delay: 0.01          # 延迟奖励缩放
  reward_scale_energy: 0.001        # 能耗奖励缩放



# ============================================================================
# 策略域参数 (Policy Domain Parameters)
# ============================================================================
policy_domain:
  # 策略域配置
  num_policy_domains: 42            # 策略域数量
  domain_overlap_ratio: 0.1         # 策略域重叠比例
  domain_update_interval: 200       # 策略域更新间隔

  # 师生学习参数
  teacher_learning_rate: 1.0e-4     # 教师网络学习率
  student_adaptation_rate: 1.0e-3   # 学生适应学习率
  knowledge_transfer_ratio: 0.8     # 知识迁移比例
  experience_replay_size: 5000      # 区域经验回放池大小

  # 策略初始化参数
  policy_init_noise: 0.1            # 策略初始化噪声
  teacher_update_frequency: 100     # 教师网络更新频率
  cross_domain_transfer_threshold: 0.8  # 跨域知识迁移阈值

# ============================================================================
# GNN预测模型参数 (GNN Prediction Model Parameters)
# ============================================================================
gnn:
  # GNN网络架构
  gnn_hidden_dim: 128               # GNN隐藏层维度
  gnn_num_layers: 3                 # GNN层数
  gnn_dropout_rate: 0.1             # Dropout比例
  gnn_activation: "relu"            # 激活函数

  # 预测参数
  prediction_horizon: 10            # 预测时间窗口 (时隙)
  historical_window: 50             # 历史数据窗口 (时隙)
  prediction_update_interval: 10    # 预测更新间隔

  # 缓存参数
  cache_size_bits: 8388608000       # 缓存大小 (bits)
  cache_hit_threshold: 0.7          # 缓存命中阈值
  cache_replacement_policy: "LRU"   # 缓存替换策略
  proactive_cache_ratio: 0.3        # 主动缓存比例

# ============================================================================
# 观测空间归一化参数 (Observation Space Normalization Parameters)
# ============================================================================
observation:
  # 位置信息归一化参数
  max_latitude_deg: 90.0            # 最大纬度（度）
  max_longitude_deg: 180.0          # 最大经度（度）
  max_altitude_km: 2000.0           # 最大高度（公里）
  
  # 任务信息归一化参数
  max_queue_length: 10.0            # 任务队列长度归一化基数
  max_task_priority: 10.0           # 最大任务优先级
  max_task_urgency: 100.0           # 最大任务紧急度
  
  # 通信信息归一化参数
  max_neighbor_count: 10.0          # 最大邻居数量
  max_ground_station_count: 5.0     # 最大可见地面站数量