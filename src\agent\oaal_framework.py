"""
OAAL框架集成
将所有组件集成为统一的框架
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

from .strategy_domain import DomainManager
from .transformer_scheduler import TransformerScheduler  
from .hybrid_learning import HybridLearningAgent
from .graph_attention import CoordinationManager, LinkInfo

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OAALFramework:
    """轨道感知自适应学习框架"""
    
    def __init__(self,
                 num_satellites: int = 36,
                 num_domains: int = 24,
                 hidden_dim: int = 256,
                 learning_rate: float = 3e-4,
                 device: str = 'cpu'):
        
        self.num_satellites = num_satellites
        self.num_domains = num_domains
        self.device = torch.device(device)
        
        # 初始化核心组件
        logger.info("初始化OAAL框架...")
        
        # 1. 策略域管理器 - 修复参数不匹配
        self.domain_manager = DomainManager(
            num_domains=num_domains,
            obs_dim=15,
            task_feature_dim=8,
            domain_feature_dim=11,
            d_model=hidden_dim,
            nhead=8,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        logger.info(f"✓ 策略域管理器初始化完成 ({num_domains}个域)")
        
        # 2. LEO智能体 - 修复参数不匹配
        self.agents = {}
        for i in range(num_satellites):
            self.agents[i] = HybridLearningAgent(
                agent_id=i,
                obs_dim=15,
                task_feature_dim=8,
                hidden_dim=hidden_dim,
                lr_actor=learning_rate,
                lr_critic=learning_rate * 3,  # 通常critic学习率更高
                gamma=0.99,
                gae_lambda=0.95,
                clip_ratio=0.2,
                kl_coef=0.5,
                temperature=3.0
            )
        logger.info(f"✓ LEO智能体初始化完成 ({num_satellites}个智能体)")
        
        # 3. 协同管理器 - 修复参数不匹配
        self.coordination_manager = CoordinationManager(
            num_satellites=num_satellites,
            obs_dim=15,
            hidden_dim=hidden_dim,
            num_heads=8,
            num_layers=3,
            dropout=0.1
        )
        logger.info("✓ 图注意力协同管理器初始化完成")
        
        # 4. 轨道-域映射
        self.agent_domain_mapping = {}  # {agent_id: domain_id}
        
        # 5. 性能统计
        self.episode_stats = {
            'task_completion_rate': [],
            'average_delay': [],
            'energy_efficiency': [],
            'coordination_success_rate': []
        }
        
        logger.info("OAAL框架初始化完成！")
    
    def update_agent_positions(self, satellite_positions: Dict[int, Tuple[float, float]]):
        """更新智能体位置和策略域映射"""
        for agent_id, (lat, lon) in satellite_positions.items():
            # 获取对应的策略域 - 现在传递纬度信息以支持LEO卫星坐标区域
            domain = self.domain_manager.get_domain_for_position(lon, lat)
            if domain is not None:
                old_domain_id = self.agent_domain_mapping.get(agent_id)
                new_domain_id = domain.domain_id
                
                # 处理域转换
                if old_domain_id != new_domain_id:
                    self._handle_domain_transition(agent_id, old_domain_id, new_domain_id)
                
                self.agent_domain_mapping[agent_id] = new_domain_id
    
    def _handle_domain_transition(self, agent_id: int, old_domain_id: Optional[int], 
                                new_domain_id: int):
        """处理智能体的域转换 - 修复数据格式不匹配"""
        agent = self.agents[agent_id]
        
        # 离开旧域
        if old_domain_id is not None:
            old_domain = self.domain_manager.domains[old_domain_id]
            old_domain.remove_visiting_agent(agent_id)
            
            # 贡献经验到旧域 - 修复格式匹配问题
            if len(agent.performance_history) > 0:
                avg_performance = np.mean(list(agent.performance_history))
                
                # 获取智能体策略参数 - 转换为正确格式
                policy_params = {}
                for name, param in agent.actor.named_parameters():
                    policy_params[name] = param.data.clone()
                
                experience = {
                    'performance': avg_performance,
                    'policy_params': policy_params
                }
                
                try:
                    old_domain.update_policy([experience])
                    logger.debug(f"智能体{agent_id}向域{old_domain_id}贡献经验 (性能: {avg_performance:.3f})")
                except Exception as e:
                    logger.warning(f"智能体{agent_id}向域{old_domain_id}贡献经验失败: {e}")
        
        # 进入新域
        new_domain = self.domain_manager.domains[new_domain_id]
        new_domain.add_visiting_agent(agent_id)
        logger.debug(f"智能体{agent_id}进入域{new_domain_id}")
    
    def select_actions(self, observations: Dict[int, np.ndarray],
                      task_queues: Dict[int, List[Dict]],
                      valid_actions: Dict[int, List[List[int]]]) -> Dict[int, List[int]]:
        """为所有智能体选择动作"""
        actions = {}
        action_infos = {}
        
        for agent_id, obs in observations.items():
            agent = self.agents[agent_id]
            task_queue = task_queues.get(agent_id, [])
            valid_acts = valid_actions.get(agent_id, [])
            
            if task_queue:
                # 使用混合学习智能体选择动作
                agent_actions, info = agent.select_actions(obs, task_queue, valid_acts)
                actions[agent_id] = agent_actions
                action_infos[agent_id] = info
            else:
                actions[agent_id] = []
        
        return actions
    
    def coordinate_agents(self, satellite_states: Dict[int, Dict],
                         communication_links: List[Dict]) -> Dict[int, Dict]:
        """执行智能体间协同"""
        # 转换链路信息格式
        link_dict = {}
        for link in communication_links:
            src = link['source_id']
            tgt = link['target_id']
            link_info = LinkInfo(
                source_id=src,
                target_id=tgt,
                distance_km=link.get('distance_km', 1000),
                data_rate_mbps=link.get('data_rate_mbps', 50),
                delay_ms=link.get('delay_ms', 10),
                energy_cost_j=link.get('energy_cost_j', 0.1),
                link_quality=link.get('link_quality', 0.8)
            )
            link_dict[(src, tgt)] = link_info
        
        # 执行协同
        coordination_decisions = self.coordination_manager.coordinate_satellites(
            satellite_states, link_dict
        )
        
        return coordination_decisions
    
    def update_agents(self, experiences: Dict[int, List[Dict]]):
        """更新所有智能体 - 修复数据格式和错误处理"""
        for agent_id, agent_experiences in experiences.items():
            if not agent_experiences:
                continue
                
            agent = self.agents[agent_id]
            
            # 获取当前域策略
            domain_id = self.agent_domain_mapping.get(agent_id)
            domain_policy = None
            if domain_id is not None:
                domain_policy = self.domain_manager.domains[domain_id]
            
            # 存储经验 - 添加数据验证
            valid_experiences = []
            for exp in agent_experiences:
                try:
                    # 验证必需字段
                    required_fields = ['observation', 'task_features', 'actions', 'reward', 
                                     'next_observation', 'done']
                    if all(field in exp for field in required_fields):
                        agent.store_transition(
                            observation=exp['observation'],
                            task_features=exp['task_features'],
                            actions=exp['actions'],
                            rewards=exp['reward'],  # 注意参数名是rewards
                            next_observation=exp['next_observation'],
                            done=exp['done'],
                            info=exp.get('info', {})
                        )
                        valid_experiences.append(exp)
                    else:
                        logger.warning(f"智能体{agent_id}经验缺失必需字段: {exp.keys()}")
                except Exception as e:
                    logger.warning(f"智能体{agent_id}存储经验失败: {e}")
            
            # 混合学习更新 - 添加错误处理
            if valid_experiences:
                try:
                    update_info = agent.update(domain_policy)
                    
                    # 记录训练信息
                    if update_info:
                        logger.debug(f"智能体{agent_id}更新完成: {update_info}")
                        
                except Exception as e:
                    logger.error(f"智能体{agent_id}更新失败: {e}")
                
                # 更新性能
                total_reward = sum(exp['reward'] for exp in valid_experiences)
                agent.update_performance(total_reward)
    
    def update_domains(self, current_time: float, global_stats: Dict):
        """更新所有策略域 - 改进性能统计和错误处理"""
        try:
            self.domain_manager.update_all_domains(current_time, global_stats)
        except Exception as e:
            logger.error(f"域更新失败: {e}")
            return
        
        # 域间知识传递（每10个时间步执行一次）
        if int(current_time) % 10 == 0:
            self._perform_knowledge_transfer()
    
    def _perform_knowledge_transfer(self):
        """执行域间知识传递"""
        transfer_count = 0
        for domain_id in range(self.num_domains):
            try:
                neighbors = self.domain_manager.get_neighbor_domains(domain_id)
                current_domain = self.domain_manager.domains[domain_id]
                
                for neighbor in neighbors:
                    # 基于性能差异决定传递方向 - 增加安全检查
                    if (hasattr(current_domain, 'performance_history') and 
                        hasattr(neighbor, 'performance_history') and
                        len(current_domain.performance_history) > 5 and 
                        len(neighbor.performance_history) > 5):
                        
                        domain_perf = np.mean(current_domain.performance_history[-10:])
                        neighbor_perf = np.mean(neighbor.performance_history[-10:])
                        
                        # 只从性能更好的域传递知识，且有明显差异
                        if neighbor_perf > domain_perf + 0.1:  # 至少0.1的性能差异
                            try:
                                self.domain_manager.transfer_knowledge_between_domains(
                                    neighbor.domain_id, domain_id, transfer_rate=0.05
                                )
                                transfer_count += 1
                                logger.debug(f"知识从域{neighbor.domain_id}传递到域{domain_id}")
                            except Exception as e:
                                logger.warning(f"域{neighbor.domain_id}到域{domain_id}知识传递失败: {e}")
            except Exception as e:
                logger.warning(f"域{domain_id}知识传递处理失败: {e}")
        
        if transfer_count > 0:
            logger.info(f"执行了{transfer_count}次域间知识传递")
    
    def get_episode_stats(self) -> Dict[str, float]:
        """获取回合统计信息 - 改进统计计算和错误处理"""
        stats = {}
        
        try:
            # 任务完成率
            if self.episode_stats['task_completion_rate']:
                completion_rates = self.episode_stats['task_completion_rate']
                stats['avg_completion_rate'] = np.mean(completion_rates)
                stats['std_completion_rate'] = np.std(completion_rates)
                stats['min_completion_rate'] = np.min(completion_rates)
                stats['max_completion_rate'] = np.max(completion_rates)
            
            # 平均延迟
            if self.episode_stats['average_delay']:
                delays = self.episode_stats['average_delay']
                stats['avg_delay'] = np.mean(delays)
                stats['std_delay'] = np.std(delays)
            
            # 能源效率
            if self.episode_stats['energy_efficiency']:
                efficiencies = self.episode_stats['energy_efficiency']
                stats['avg_energy_efficiency'] = np.mean(efficiencies)
                stats['std_energy_efficiency'] = np.std(efficiencies)
            
            # 协同成功率
            coord_stats = self.coordination_manager.coordination_stats
            if coord_stats['requests_sent'] > 0:
                stats['coordination_success_rate'] = \
                    coord_stats['requests_accepted'] / coord_stats['requests_sent']
                stats['total_requests_sent'] = coord_stats['requests_sent']
                stats['total_requests_accepted'] = coord_stats['requests_accepted']
                stats['total_tasks_offloaded'] = coord_stats['tasks_offloaded']
            
            # 智能体性能统计
            agent_performances = []
            for agent in self.agents.values():
                if len(agent.performance_history) > 0:
                    agent_performances.append(np.mean(list(agent.performance_history)))
            
            if agent_performances:
                stats['avg_agent_performance'] = np.mean(agent_performances)
                stats['std_agent_performance'] = np.std(agent_performances)
                stats['num_active_agents'] = len(agent_performances)
            
            # 域性能统计
            domain_performances = []
            active_domains = 0
            for domain in self.domain_manager.domains.values():
                if hasattr(domain, 'performance_history') and len(domain.performance_history) > 0:
                    domain_performances.append(np.mean(domain.performance_history[-10:]))
                    active_domains += 1
            
            if domain_performances:
                stats['avg_domain_performance'] = np.mean(domain_performances)
                stats['std_domain_performance'] = np.std(domain_performances)
                stats['num_active_domains'] = active_domains
            
            # 总体统计
            stats['total_episodes'] = len(self.episode_stats.get('task_completion_rate', []))
            
        except Exception as e:
            logger.error(f"统计信息计算失败: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def save_checkpoint(self, path: str):
        """保存检查点"""
        checkpoint = {
            'domain_policies': {
                d_id: domain.policy_network.state_dict() 
                for d_id, domain in self.domain_manager.domains.items()
            },
            'agent_policies': {
                a_id: agent.actor.state_dict()
                for a_id, agent in self.agents.items()
            },
            'coordination_network': self.coordination_manager.gat_network.state_dict(),
            'episode_stats': self.episode_stats
        }
        
        torch.save(checkpoint, path)
        logger.info(f"检查点已保存至: {path}")
    
    def load_checkpoint(self, path: str):
        """加载检查点"""
        if not Path(path).exists():
            logger.warning(f"检查点文件不存在: {path}")
            return
        
        checkpoint = torch.load(path, map_location=self.device)
        
        # 加载域策略
        for d_id, state_dict in checkpoint['domain_policies'].items():
            self.domain_manager.domains[int(d_id)].policy_network.load_state_dict(state_dict)
        
        # 加载智能体策略
        for a_id, state_dict in checkpoint['agent_policies'].items():
            self.agents[int(a_id)].actor.load_state_dict(state_dict)
        
        # 加载协同网络
        self.coordination_manager.gat_network.load_state_dict(
            checkpoint['coordination_network']
        )
        
        # 加载统计信息
        self.episode_stats = checkpoint['episode_stats']
        
        logger.info(f"检查点已加载: {path}")
    
    def reset_episode_stats(self):
        """重置回合统计"""
        for key in self.episode_stats:
            self.episode_stats[key].clear()
        
        self.coordination_manager.coordination_stats = {
            'requests_sent': 0,
            'requests_accepted': 0,
            'tasks_offloaded': 0,
            'total_reward': 0.0
        }
    
    def add_episode_metrics(self, completion_rate: float, avg_delay: float, 
                           energy_efficiency: float):
        """添加回合指标 - 便于外部调用"""
        self.episode_stats['task_completion_rate'].append(completion_rate)
        self.episode_stats['average_delay'].append(avg_delay)
        self.episode_stats['energy_efficiency'].append(energy_efficiency)
    
    def get_framework_status(self) -> Dict[str, any]:
        """获取框架状态信息 - 用于监控和调试"""
        status = {
            'num_satellites': self.num_satellites,
            'num_domains': self.num_domains,
            'device': str(self.device),
            'agent_domain_mapping': dict(self.agent_domain_mapping),
            'active_agents': len([a for a in self.agents.values() if len(a.memory) > 0]),
            'total_experiences': sum(len(a.memory) for a in self.agents.values()),
            'domains_with_visitors': len([d for d in self.domain_manager.domains.values() 
                                        if len(d.visiting_agents) > 0])
        }
        
        # 添加每个域的状态
        domain_status = {}
        for domain_id, domain in self.domain_manager.domains.items():
            domain_status[domain_id] = {
                'num_visitors': len(domain.visiting_agents),
                'visitors': list(domain.visiting_agents),
                'bounds': domain.bounds,
                'has_performance_history': hasattr(domain, 'performance_history') and len(domain.performance_history) > 0
            }
        status['domain_status'] = domain_status
        
        return status