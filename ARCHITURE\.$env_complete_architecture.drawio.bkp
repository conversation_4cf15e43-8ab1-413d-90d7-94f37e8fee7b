<mxfile host="app.diagrams.net" modified="2025-01-23T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="SPACE-OAAL环境架构图" id="env-architecture">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 程序入口层 -->
        <mxCell id="main-entry" value="SPACE-OAAL&#xa;环境系统入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="750" y="50" width="150" height="80" as="geometry" />
        </mxCell>
        
        <!-- 接口层 -->
        <mxCell id="interface-group" value="接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="180" width="800" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="env-interface" value="OAALEnvironmentInterface&#xa;环境接口&#xa;- reset() 重置环境&#xa;- step(actions) 执行动作&#xa;- _format_state() 格式化状态&#xa;- _apply_actions() 应用动作&#xa;- _calculate_rewards() 计算奖励&#xa;- render() 渲染环境" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=11;fontStyle=1" vertex="1" parent="interface-group">
          <mxGeometry x="290" y="40" width="220" height="90" as="geometry" />
        </mxCell>
        
        <!-- 适配器层 -->
        <mxCell id="adapter-group" value="适配器层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="330" width="1400" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter" value="SatelliteAdapter&#xa;卫星适配器&#xa;- sync_all_satellites() 同步卫星&#xa;- create_satellite_node() 创建节点&#xa;- update_satellite_node() 更新节点&#xa;- _update_communication_links() 更新链路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="50" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter" value="TaskAdapter&#xa;任务适配器&#xa;- generate_tasks_for_timeslot() 生成任务&#xa;- convert_generated_task() 转换任务&#xa;- get_enhanced_tasks() 获取增强任务&#xa;- _assign_tasks_to_satellites() 分配任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="300" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-manager" value="IntegrationManager&#xa;集成管理器&#xa;- step(timeslot) 执行时间步&#xa;- reset() 重置系统&#xa;- get_system_state() 获取状态&#xa;- export_results() 导出结果&#xa;- _update_simulation_stats() 更新统计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="550" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 实体层 -->
        <mxCell id="entity-group" value="实体层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="510" width="1400" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-node" value="SatelliteNode&#xa;卫星节点&#xa;- Position, EnergyState 位置和能量状态&#xa;- ResourceState, task_queue 资源和任务队列&#xa;- neighbors, ground_stations 邻居和地面站&#xa;- process_task() 处理任务&#xa;- offload_task() 卸载任务&#xa;- get_best_offload_target() 获取最佳卸载目标" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="entity-group">
          <mxGeometry x="50" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="task-entity" value="Task&#xa;任务实体&#xa;- task_id, state, priority 任务属性&#xa;- data_size_mb, deadline 数据和截止时间&#xa;- processing_records[] 处理记录&#xa;- transfer_records[] 传输记录&#xa;- process() 处理任务&#xa;- transfer() 传输任务&#xa;- calculate_priority() 计算优先级" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="entity-group">
          <mxGeometry x="300" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="task-manager" value="TaskManager&#xa;任务管理器&#xa;- tasks: Dict[str, Task] 任务字典&#xa;- add_task() 添加任务&#xa;- remove_task() 移除任务&#xa;- get_task() 获取任务&#xa;- update_priorities() 更新优先级&#xa;- cleanup_expired_tasks() 清理过期任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="entity-group">
          <mxGeometry x="550" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 物理层 -->
        <mxCell id="physics-group" value="物理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="690" width="1400" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-updater" value="OrbitalUpdater&#xa;轨道更新器&#xa;- satellite_data: DataFrame 卫星数据&#xa;- ground_stations: Dict 地面站&#xa;- get_satellites_at_time() 获取时刻卫星&#xa;- calculate_visibility_matrix() 计算可见性矩阵&#xa;- get_ground_coverage() 获取地面覆盖&#xa;- _load_satellite_data() 加载卫星数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" vertex="1" parent="physics-group">
          <mxGeometry x="50" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="communication-manager" value="CommunicationManager&#xa;通信管理器&#xa;- calculate_link_properties() 计算链路属性&#xa;- get_network_state() 获取网络状态&#xa;- get_neighbors() 获取邻居&#xa;- get_link_state() 获取链路状态&#xa;- calculate_signal_strength() 计算信号强度&#xa;- _load_config() 加载配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" vertex="1" parent="physics-group">
          <mxGeometry x="300" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="task-generator" value="TaskGenerator&#xa;任务生成器&#xa;- locations: List[Location] 位置列表&#xa;- generate_tasks_for_timeslot() 生成时隙任务&#xa;- calculate_lambda() 计算泊松参数&#xa;- sample_task_type() 采样任务类型&#xa;- load_locations_from_csv() 加载位置数据&#xa;- _generate_task_attributes() 生成任务属性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" vertex="1" parent="physics-group">
          <mxGeometry x="550" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 配置层 -->
        <mxCell id="config-group" value="配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="870" width="1400" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件&#xa;- system 系统配置&#xa;- communication 通信配置&#xa;- computation 计算配置&#xa;- queuing 队列配置&#xa;- reward 奖励配置&#xa;- fault 故障配置" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="config-group">
          <mxGeometry x="50" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-data" value="satellite_processed_data.csv&#xa;卫星数据文件&#xa;- satellite_id 卫星ID&#xa;- timestamp 时间戳&#xa;- latitude 纬度&#xa;- longitude 经度&#xa;- illuminated 光照状态" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" vertex="1" parent="config-group">
          <mxGeometry x="250" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="ground-stations-data" value="updated_global_ground_stations.csv&#xa;地面站数据文件&#xa;- ID 地面站ID&#xa;- Latitude 纬度&#xa;- Longitude 经度&#xa;- RegionType 区域类型&#xa;- PurposeType 用途类型" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" vertex="1" parent="config-group">
          <mxGeometry x="450" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="task-data" value="task_generation_results.json&#xa;任务数据文件&#xa;- regions.json 区域数据&#xa;- 任务生成结果" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" vertex="1" parent="config-group">
          <mxGeometry x="650" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        
        <!-- 主要调用关系连线 -->
        <!-- 主入口到接口层 -->
        <mxCell id="main-to-env" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="main-entry" target="env-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 接口层到适配器层 -->
        <mxCell id="env-to-integration" value="主要调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1" edge="1" parent="1" source="env-interface" target="integration-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器层内部连线 -->
        <mxCell id="integration-to-satellite" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="satellite-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-to-task" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="task-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器层到实体层 -->
        <mxCell id="satellite-adapter-to-node" value="创建/更新节点" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="satellite-node">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-task" value="创建任务实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="task-adapter" target="task-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器层到物理层 -->
        <mxCell id="satellite-adapter-to-orbital" value="数据获取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="orbital-updater">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-comm" value="网络状态" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="communication-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-generator" value="任务生成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-adapter" target="task-generator">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 实体层内部连线 -->
        <mxCell id="satellite-to-task-manager" value="任务管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="satellite-node" target="task-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-manager-to-task" value="管理任务实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="task-manager" target="task-entity">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 物理层到配置层 -->
        <mxCell id="orbital-to-config" value="配置参数" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#7030a0;dashed=1;fontSize=10" edge="1" parent="1" source="orbital-updater" target="config-yaml">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-to-satellite-data" value="卫星数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="orbital-updater" target="satellite-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-to-ground-data" value="地面站数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="orbital-updater" target="ground-stations-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="comm-to-config" value="配置参数" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#7030a0;dashed=1;fontSize=10" edge="1" parent="1" source="communication-manager" target="config-yaml">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="generator-to-ground-data" value="位置数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-generator" target="ground-stations-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="generator-to-task-data" value="任务数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-generator" target="task-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend-bg" value="图例 (Legend)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;strokeWidth=1;fontSize=14;fontStyle=1;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="1200" y="450" width="300" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-direct-call" value="直接调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=12;" edge="1" parent="1">
          <mxGeometry x="1220" y="480" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-main-flow" value="主要数据流" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#d62728;fontSize=12;" edge="1" parent="1">
          <mxGeometry x="1220" y="510" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-data-transfer" value="数据传递" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#666666;strokeDashArray=5 5;fontSize=12;" edge="1" parent="1">
          <mxGeometry x="1220" y="540" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- 标题 -->
        <mxCell id="title" value="SPACE-OAAL 环境架构图&#xa;Environment Architecture Diagram" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="10" width="400" height="30" as="geometry" />
        </mxCell>
        
        <!-- 版本信息 -->
        <mxCell id="version" value="Version: 1.0.0 | Date: 2025-07-26 | SPACE-OAAL Team" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="1000" width="400" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
