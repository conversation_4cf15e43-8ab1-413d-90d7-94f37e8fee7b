#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OAAL Simple Test
简化版测试程序，确保基本功能正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import time

def test_imports():
    """测试所有导入"""
    print("=== Testing Imports ===")
    
    try:
        from src.env.satellite_env import parallel_env
        print("  OK: Environment import successful")
    except Exception as e:
        print(f"  ERROR: Environment import failed - {e}")
        return False
    
    try:
        from src.agent.strategy_domain import DomainManager
        print("  OK: Strategy domain import successful")
    except Exception as e:
        print(f"  ERROR: Strategy domain import failed - {e}")
        return False
    
    try:
        from src.agent.transformer_scheduler import TransformerScheduler
        print("  OK: Transformer scheduler import successful")
    except Exception as e:
        print(f"  ERROR: Transformer scheduler import failed - {e}")
        return False
    
    try:
        from src.agent.hybrid_learning import HybridLearningAgent
        print("  OK: Hybrid learning import successful")
    except Exception as e:
        print(f"  ERROR: Hybrid learning import failed - {e}")
        return False
    
    try:
        from src.agent.graph_attention import CoordinationManager
        print("  OK: Graph attention import successful")
    except Exception as e:
        print(f"  ERROR: Graph attention import failed - {e}")
        return False
    
    try:
        from src.agent.oaal_framework import OAALFramework
        print("  OK: OAAL framework import successful")
    except Exception as e:
        print(f"  ERROR: OAAL framework import failed - {e}")
        return False
    
    return True


def test_environment():
    """测试环境基本功能"""
    print("\n=== Testing Environment ===")
    
    try:
        from src.env.satellite_env import parallel_env
        
        print("  Creating environment...")
        env = parallel_env()
        
        # 检查环境属性
        if hasattr(env, 'agents'):
            num_agents = len(env.agents)
            print(f"  Environment has {num_agents} agents: {list(env.agents)[:3]}...")
        else:
            print("  Warning: Environment has no 'agents' attribute")
            return False
        
        # 测试reset
        print("  Testing reset...")
        obs, infos = env.reset()
        print(f"  Reset successful: {len(obs)} observations, {len(infos)} infos")
        
        # 测试step
        print("  Testing step...")
        actions = {agent: 0 for agent in env.agents}  # 所有智能体选择动作0
        next_obs, rewards, dones, next_infos = env.step(actions)
        print(f"  Step successful: rewards range [{min(rewards.values()):.3f}, {max(rewards.values()):.3f}]")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"  ERROR: Environment test failed - {e}")
        return False


def test_components():
    """测试OAAL组件"""
    print("\n=== Testing OAAL Components ===")
    
    # 1. 测试策略域
    try:
        from src.agent.strategy_domain import DomainManager
        
        print("  Testing Domain Manager...")
        dm = DomainManager(num_domains=4)  # 小规模测试
        
        # 测试位置映射
        test_positions = [-180, -90, 0, 90]
        for lon in test_positions:
            domain = dm.get_domain_for_position(lon)
            if domain:
                print(f"    Longitude {lon} -> Domain {domain.domain_id}")
        
        print("  Domain Manager: OK")
        
    except Exception as e:
        print(f"  ERROR: Domain Manager test failed - {e}")
        return False
    
    # 2. 测试Transformer调度器
    try:
        from src.agent.transformer_scheduler import TransformerScheduler
        
        print("  Testing Transformer Scheduler...")
        scheduler = TransformerScheduler(d_model=32, num_encoder_layers=1, num_decoder_layers=1)
        
        # 测试动作生成
        obs = np.random.randn(15)
        task_queue = [
            {'type_id': 1, 'data_size_mb': 50, 'priority': 2},
            {'type_id': 2, 'data_size_mb': 30, 'priority': 3}
        ]
        valid_actions = [[0, 1, 2], [0, 1]]
        
        actions = scheduler.generate_action_sequence(obs, task_queue, valid_actions)
        print(f"    Generated actions: {actions}")
        print("  Transformer Scheduler: OK")
        
    except Exception as e:
        print(f"  ERROR: Transformer Scheduler test failed - {e}")
        return False
    
    # 3. 测试混合学习智能体
    try:
        from src.agent.hybrid_learning import HybridLearningAgent
        
        print("  Testing Hybrid Learning Agent...")
        agent = HybridLearningAgent(agent_id=0, hidden_dim=32)
        
        obs = np.random.randn(15)
        task_queue = [{'type_id': 1, 'data_size_mb': 50, 'priority': 2}]
        valid_actions = [[0, 1, 2]]
        
        actions, info = agent.select_actions(obs, task_queue, valid_actions)
        print(f"    Selected actions: {actions}")
        print("  Hybrid Learning Agent: OK")
        
    except Exception as e:
        print(f"  ERROR: Hybrid Learning Agent test failed - {e}")
        return False
    
    return True


def test_oaal_framework():
    """测试OAAL框架"""
    print("\n=== Testing OAAL Framework ===")
    
    try:
        from src.agent.oaal_framework import OAALFramework
        
        print("  Creating OAAL framework...")
        oaal = OAALFramework(
            num_satellites=4,  # 小规模测试
            num_domains=4,
            hidden_dim=32
        )
        print("  OAAL Framework created successfully")
        
        # 测试位置更新
        print("  Testing position updates...")
        positions = {0: (0, -180), 1: (0, -90), 2: (0, 0), 3: (0, 90)}
        oaal.update_agent_positions(positions)
        print("  Position updates: OK")
        
        # 测试动作选择
        print("  Testing action selection...")
        observations = {i: np.random.randn(15) for i in range(4)}
        task_queues = {i: [{'type_id': 1, 'data_size_mb': 50, 'priority': 2}] for i in range(4)}
        valid_actions = {i: [[0, 1, 2]] for i in range(4)}
        
        actions = oaal.select_actions(observations, task_queues, valid_actions)
        print(f"  Generated actions for {len(actions)} agents")
        print("  Action selection: OK")
        
        return True
        
    except Exception as e:
        print(f"  ERROR: OAAL Framework test failed - {e}")
        return False


def simple_integration_test():
    """简单集成测试"""
    print("\n=== Simple Integration Test ===")
    
    try:
        from src.env.satellite_env import parallel_env
        from src.agent.oaal_framework import OAALFramework
        
        # 创建环境和框架
        print("  Creating environment and framework...")
        env = parallel_env()
        num_agents = len(env.agents) if hasattr(env, 'agents') else 36
        
        oaal = OAALFramework(
            num_satellites=min(num_agents, 6),  # 限制规模
            num_domains=4,
            hidden_dim=32
        )
        
        # 运行几步
        print("  Running integration test...")
        obs, infos = env.reset()
        
        for step in range(3):
            print(f"    Step {step + 1}...")
            
            # 简化的动作选择
            actions = {agent: 0 for agent in env.agents}
            
            # 执行步骤
            next_obs, rewards, dones, next_infos = env.step(actions)
            
            # 计算指标
            avg_reward = np.mean(list(rewards.values()))
            print(f"      Average reward: {avg_reward:.3f}")
            
            obs = next_obs
            infos = next_infos
        
        env.close()
        print("  Integration test: OK")
        return True
        
    except Exception as e:
        print(f"  ERROR: Integration test failed - {e}")
        return False


def main():
    """主测试函数"""
    print("OAAL Framework Simple Test")
    print("=" * 40)
    
    # 测试步骤
    tests = [
        ("Import Test", test_imports),
        ("Environment Test", test_environment),
        ("Components Test", test_components),
        ("OAAL Framework Test", test_oaal_framework),
        ("Integration Test", simple_integration_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        start_time = time.time()
        
        try:
            success = test_func()
            test_time = time.time() - start_time
            
            if success:
                print(f"  PASSED ({test_time:.2f}s)")
                results.append(True)
            else:
                print(f"  FAILED ({test_time:.2f}s)")
                results.append(False)
                
        except Exception as e:
            test_time = time.time() - start_time
            print(f"  ERROR ({test_time:.2f}s): {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 40)
    print("Test Summary:")
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "PASS" if results[i] else "FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("All tests PASSED! OAAL framework is ready to use.")
        return 0
    else:
        print("Some tests FAILED. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)