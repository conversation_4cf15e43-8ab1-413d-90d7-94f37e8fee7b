"""
Transformer调度器
实现基于Transformer的生成式序列动作空间
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional
import math


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 100):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:, :x.size(1)]


class TransformerScheduler(nn.Module):
    """基于Transformer的任务调度器"""
    
    def __init__(self, 
                 obs_dim: int = 15,          # 观测维度
                 task_feature_dim: int = 8,   # 任务特征维度
                 d_model: int = 256,          # 模型维度
                 nhead: int = 8,              # 注意力头数
                 num_encoder_layers: int = 3,  # 编码器层数
                 num_decoder_layers: int = 3,  # 解码器层数
                 num_actions: int = 42,        # 动作数量(本地+36卫星+5云)
                 dropout: float = 0.1):
        super().__init__()
        
        self.d_model = d_model
        self.num_actions = num_actions
        
        # 输入嵌入层
        self.obs_embedding = nn.Linear(obs_dim, d_model)
        self.task_embedding = nn.Linear(task_feature_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.encoder = nn.TransformerEncoder(encoder_layer, num_encoder_layers)
        
        # Transformer解码器
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.decoder = nn.TransformerDecoder(decoder_layer, num_decoder_layers)
        
        # 输出投影层
        self.output_projection = nn.Linear(d_model, num_actions)
        
        # 特殊标记嵌入
        self.start_token = nn.Parameter(torch.randn(1, 1, d_model))
        # 移除未使用的task_token参数
        
    def encode_observation(self, observation: torch.Tensor) -> torch.Tensor:
        """编码观测"""
        # observation: [batch_size, obs_dim]
        obs_embed = self.obs_embedding(observation).unsqueeze(1)  # [batch, 1, d_model]
        return obs_embed
    
    def encode_tasks(self, task_features: torch.Tensor) -> torch.Tensor:
        """编码任务队列"""
        # task_features: [batch_size, num_tasks, task_feature_dim]
        if task_features.dim() == 2:
            task_features = task_features.unsqueeze(0)
            
        task_embeds = self.task_embedding(task_features)  # [batch, num_tasks, d_model]
        task_embeds = self.pos_encoding(task_embeds)
        return task_embeds
    
    def forward(self, observation: torch.Tensor, task_features: torch.Tensor,
                action_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播，生成任务调度序列
        
        Args:
            observation: [batch_size, obs_dim] 智能体观测
            task_features: [batch_size, num_tasks, task_feature_dim] 任务特征
            action_mask: [batch_size, num_tasks, num_actions] 动作掩码
            
        Returns:
            action_probs: [batch_size, num_tasks, num_actions] 每个任务的动作概率
        """
        batch_size = observation.size(0)
        num_tasks = task_features.size(1)
        
        # 编码观测和任务
        obs_embed = self.encode_observation(observation)  # [batch, 1, d_model]
        task_embeds = self.encode_tasks(task_features)    # [batch, num_tasks, d_model]
        
        # 组合编码器输入: [观测, 任务1, 任务2, ...]
        encoder_input = torch.cat([obs_embed, task_embeds], dim=1)  # [batch, 1+num_tasks, d_model]
        
        # 编码器前向传播
        memory = self.encoder(encoder_input)  # [batch, 1+num_tasks, d_model]
        
        # 准备解码器输入 - 使用因果掩码的自回归生成
        # 起始标记
        decoder_input = self.start_token.expand(batch_size, 1, -1)  # [batch, 1, d_model]
        
        all_logits = []
        
        # 自回归生成每个任务的动作
        for i in range(num_tasks):
            # 解码器前向传播
            tgt_mask = self._generate_square_subsequent_mask(decoder_input.size(1)).to(decoder_input.device)
            decoder_output = self.decoder(
                decoder_input, 
                memory,
                tgt_mask=tgt_mask
            )
            
            # 获取最后一个位置的输出
            last_output = decoder_output[:, -1, :]  # [batch, d_model]
            
            # 投影到动作空间
            logits = self.output_projection(last_output)  # [batch, num_actions]
            all_logits.append(logits)
            
            # 准备下一步的输入 - 使用任务嵌入作为输入
            if i < num_tasks - 1:
                next_input = task_embeds[:, i:i+1, :]  # [batch, 1, d_model]
                decoder_input = torch.cat([decoder_input, next_input], dim=1)
        
        # 堆叠所有logits
        all_logits = torch.stack(all_logits, dim=1)  # [batch, num_tasks, num_actions]
        
        # 应用动作掩码
        if action_mask is not None:
            masked_logits = all_logits.masked_fill(~action_mask, float('-inf'))
        else:
            masked_logits = all_logits
            
        # 计算概率
        action_probs = F.softmax(masked_logits, dim=-1)
        
        return action_probs
    
    def _generate_square_subsequent_mask(self, sz: int) -> torch.Tensor:
        """生成因果注意力掩码 - 使用布尔类型更规范"""
        # 返回布尔掩码，True表示需要被忽略的位置
        return torch.triu(torch.ones(sz, sz), diagonal=1).bool()
    
    # 移除generate_action_sequence和_extract_task_features方法
    # 这些方法属于Agent层的逻辑，不应该在nn.Module中实现
    # 请在HybridLearningAgent中使用这些功能