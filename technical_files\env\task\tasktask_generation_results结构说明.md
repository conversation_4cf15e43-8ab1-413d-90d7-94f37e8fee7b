基于您提供的task结构说明文档，我来简洁说明任务生成程序的数据结构：

## 任务生成程序数据结构说明

### 1. 输入数据结构
```python
# 地面站信息 (从CSV读取)
Location = {
    "location_id": int,           # 地面站ID (1-420)
    "coordinates": [lat, lon],    # 经纬度坐标
    "geography": str,             # "Land" 或 "Ocean"
    "scale": str,                 # "Small", "Medium", "Large"
    "functional_type": str        # "Normal", "Industrial", "DelaySensitive"
}
```

### 2. 生成的任务数据结构
```python
# 单个任务
Task = {
    "task_id": int,                    # 全局唯一任务ID
    "type_id": int,                    # 任务类型 (1,2,3)
    "data_size_mb": float,             # 数据大小 (MB)
    "complexity_cycles_per_bit": int,  # 计算复杂度 (100,200,300)
    "deadline_timestamp": float,       # 截止时间戳
    "priority": int,                   # 优先级 (1-5)
    "source_location_id": int          # 源地面站ID
}
```

{
  "simulation_metadata": {
    "total_locations": 420,
    "total_timeslots": 1000,
    "timeslot_duration_seconds": 10,
    "land_base_rate": 10.0,
    "total_tasks_generated": 2263029,
    "generation_timestamp": "2025-07-16T20:06:52.862968"
  },
  "simulation_results": [
    {
      "timeslot": 0,
      "timestamp": 0,
      "locations": [
        {
          "location_id": 1,
          "coordinates": [
            -65.0,
            -180.0
          ],
          "geography": "Land",
          "scale": "Medium",
          "functional_type": "Normal",
          "lambda_i": 6.0,
          "num_tasks": 6,
          "generated_tasks": [
            {
              "task_id": 1,
              "type_id": 3,
              "data_size_mb": 110.11150117432088,
              "complexity_cycles_per_bit": 300,
              "deadline_timestamp": 100,
              "priority": 3
            },
            {
              "task_id": 2,
              "type_id": 1,
              "data_size_mb": 19.699098521619945,
              "complexity_cycles_per_bit": 100,
              "deadline_timestamp": 10,
              "priority": 4
            },
            {
              "task_id": 3,
              "type_id": 3,
              "data_size_mb": 50.07787658410143,
              "complexity_cycles_per_bit": 300,
              "deadline_timestamp": 100,
              "priority": 4
            },
            {
              "task_id": 4,
              "type_id": 1,
              "data_size_mb": 13.042422429595376,
              "complexity_cycles_per_bit": 100,
              "deadline_timestamp": 10,
              "priority": 5
            },
            {
              "task_id": 5,
              "type_id": 2,
              "data_size_mb": 28.736874205941255,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 3
            },
            {
              "task_id": 6,
              "type_id": 2,
              "data_size_mb": 21.399969896408464,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 4
            }
          ]
        },
        {
          "location_id": 2,
          "coordinates": [
            -65.0,
            -168.0
          ],
          "geography": "Land",
          "scale": "Large",
          "functional_type": "Normal",
          "lambda_i": 10.0,
          "num_tasks": 9,
          "generated_tasks": [
            {
              "task_id": 7,
              "type_id": 2,
              "data_size_mb": 25.99021346475079,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 4
            },
            {
              "task_id": 8,
              "type_id": 2,
              "data_size_mb": 21.393512381599933,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 3
            },
            {
              "task_id": 9,
              "type_id": 2,
              "data_size_mb": 33.51497755908629,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 2
            },
            {
              "task_id": 10,
              "type_id": 3,
              "data_size_mb": 146.56320330745592,
              "complexity_cycles_per_bit": 300,
              "deadline_timestamp": 100,
              "priority": 2
            },
            {
              "task_id": 11,
              "type_id": 2,
              "data_size_mb": 20.478987566606428,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 2
            },
            {
              "task_id": 12,
              "type_id": 2,
              "data_size_mb": 33.20457481218804,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 4
            },
            {
              "task_id": 13,
              "type_id": 2,
              "data_size_mb": 21.031655633456552,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 1
            },
            {
              "task_id": 14,
              "type_id": 2,
              "data_size_mb": 39.875668530619464,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 2
            },
            {
              "task_id": 15,
              "type_id": 2,
              "data_size_mb": 26.238249886045665,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50,
              "priority": 4
            }
          ]
        },
