<mxfile host="app.diagrams.net" modified="2025-01-23T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="适配器模块架构图" id="adapters-architecture">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 程序入口层 -->
        <mxCell id="main-entry" value="IntegrationManager&#xa;集成管理器入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="750" y="50" width="150" height="80" as="geometry" />
        </mxCell>
        
        <!-- 外部依赖层 -->
        <mxCell id="dependency-group" value="外部依赖层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="180" width="1500" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="orbital-updater" value="orbital_updater&#xa;OrbitalUpdater&#xa;Satellite" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="50" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="task-generator" value="task_generator&#xa;TaskGenerator&#xa;Task" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="220" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="communication" value="communication&#xa;CommunicationManager" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="390" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-new" value="satellite&#xa;SatelliteNode" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="560" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="task-new" value="task&#xa;EnhancedTask" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="730" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=11" vertex="1" parent="dependency-group">
          <mxGeometry x="900" y="40" width="130" height="50" as="geometry" />
        </mxCell>
        
        <!-- 核心适配器层 -->
        <mxCell id="adapter-group" value="核心适配器层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="320" width="1400" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter" value="SatelliteAdapter&#xa;卫星适配器&#xa;- 轨道卫星与卫星节点转换&#xa;- 通信链路状态同步&#xa;- 卫星状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="50" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter" value="TaskAdapter&#xa;任务适配器&#xa;- 生成任务与增强任务转换&#xa;- 批量任务处理&#xa;- 时隙任务生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="300" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-manager" value="IntegrationManager&#xa;集成管理器&#xa;- 协调所有模块交互&#xa;- 仿真执行控制&#xa;- 系统状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" vertex="1" parent="adapter-group">
          <mxGeometry x="550" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 功能服务层 -->
        <mxCell id="function-group" value="功能服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="500" width="1500" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="sync-satellites" value="sync_all_satellites()&#xa;同步所有卫星状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="50" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="create-satellite-node" value="create_satellite_node()&#xa;创建卫星节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="230" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="convert-task" value="convert_generated_task()&#xa;转换生成任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="410" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="generate-tasks" value="generate_tasks_for_timeslot()&#xa;生成时隙任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="590" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="step-function" value="step()&#xa;执行时间步" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="790" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="assign-tasks" value="_assign_tasks_to_satellites()&#xa;任务分配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=10" vertex="1" parent="function-group">
          <mxGeometry x="970" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        
        <!-- 工具支持层 -->
        <mxCell id="utility-group" value="工具支持层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="650" width="1400" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="update-comm-links" value="_update_communication_links()&#xa;更新通信链路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="utility-group">
          <mxGeometry x="50" y="40" width="180" height="45" as="geometry" />
        </mxCell>
        
        <mxCell id="update-ground-links" value="_update_ground_station_links()&#xa;更新地面站链路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="utility-group">
          <mxGeometry x="250" y="40" width="180" height="45" as="geometry" />
        </mxCell>
        
        <mxCell id="find-best-satellite" value="_find_best_satellite_for_task()&#xa;寻找最佳卫星" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="utility-group">
          <mxGeometry x="450" y="40" width="180" height="45" as="geometry" />
        </mxCell>
        
        <mxCell id="update-stats" value="_update_simulation_stats()&#xa;更新仿真统计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="utility-group">
          <mxGeometry x="650" y="40" width="180" height="45" as="geometry" />
        </mxCell>
        
        <mxCell id="load-config" value="_load_config()&#xa;加载配置文件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="utility-group">
          <mxGeometry x="850" y="40" width="140" height="45" as="geometry" />
        </mxCell>
        
        <!-- 主要调用关系连线 -->
        <!-- 主入口到集成管理器 -->
        <mxCell id="main-to-integration" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="main-entry" target="integration-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 集成管理器到适配器 -->
        <mxCell id="integration-to-satellite-adapter" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="satellite-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-to-task-adapter" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="integration-manager" target="task-adapter">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器到外部模块 -->
        <mxCell id="satellite-adapter-to-orbital" value="数据获取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="orbital-updater">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="satellite-adapter-to-comm" value="链路状态" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="communication">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-generator" value="任务生成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="task-adapter" target="task-generator">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 适配器到新模块 -->
        <mxCell id="satellite-adapter-to-new" value="创建节点" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="satellite-adapter" target="satellite-new">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="task-adapter-to-new" value="创建任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="task-adapter" target="task-new">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 配置文件连接 -->
        <mxCell id="config-to-integration" value="配置参数" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#7030a0;dashed=1;fontSize=10" edge="1" parent="1" source="config-yaml" target="integration-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>