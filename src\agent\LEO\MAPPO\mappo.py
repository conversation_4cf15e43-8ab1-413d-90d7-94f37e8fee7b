#!/usr/bin/env python3
"""
Multi-Agent Proximal Policy Optimization (MAPPO) Implementation
For SPACE-OAAL Satellite Edge Computing Environment

修复版核心MAPPO算法实现，集成PPO的10大技巧：
- MAPPOAgent: 单智能体PPO实现
- MAPPOTrainer: 集中式训练协调器
- Actor/Critic网络架构（正交初始化、Tanh激活）
- 经验缓冲区和算法核心逻辑
- 状态/奖励归一化、学习率衰减等训练技巧

Author: SPACE-OAAL Team
Date: 2025-07-30 (修复版)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import math
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import random


class Normalization:
    """
    Trick 2: 状态归一化
    Trick 3: 奖励归一化
    """
    def __init__(self, shape, eps=1e-8):
        self.running_mean = np.zeros(shape)
        self.running_var = np.ones(shape)
        self.count = 0
        self.eps = eps
    
    def __call__(self, x, update=True):
        if update:
            self.count += 1
            delta = x - self.running_mean
            self.running_mean += delta / self.count
            delta2 = x - self.running_mean
            self.running_var += (delta * delta2 - self.running_var) / self.count
        
        normalized = (x - self.running_mean) / (np.sqrt(self.running_var) + self.eps)
        return normalized
    
    def reset(self):
        self.running_mean = np.zeros_like(self.running_mean)
        self.running_var = np.ones_like(self.running_var)
        self.count = 0


class RewardScaling:
    """
    Trick 4: 奖励缩放
    """
    def __init__(self, shape, gamma):
        self.shape = shape  # reward shape=1
        self.gamma = gamma  # discount factor
        self.running_var = np.ones(shape)
        self.count = 0
        self.eps = 1e-8
    
    def __call__(self, reward):
        self.count += 1
        if self.count == 1:
            self.running_var = reward ** 2
        else:
            self.running_var = self.gamma * self.running_var + (1 - self.gamma) * reward ** 2
        return reward / (np.sqrt(self.running_var) + self.eps)
    
    def reset(self):
        self.running_var = np.ones(self.shape)
        self.count = 0


class LearningRateScheduler:
    """
    Trick 6: 学习率衰减调度器
    """
    def __init__(self, optimizer, initial_lr, decay_steps, decay_rate=0.96, min_lr=1e-6):
        self.optimizer = optimizer
        self.initial_lr = initial_lr
        self.decay_steps = decay_steps
        self.decay_rate = decay_rate
        self.min_lr = min_lr
        self.step_count = 0
    
    def step(self):
        self.step_count += 1
        if self.step_count % self.decay_steps == 0:
            current_lr = max(
                self.initial_lr * (self.decay_rate ** (self.step_count // self.decay_steps)),
                self.min_lr
            )
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = current_lr
    
    def get_lr(self):
        return self.optimizer.param_groups[0]['lr']


class ActorNetwork(nn.Module):
    """
    Actor网络 - 策略网络 (集成PPO技巧版本)
    输入: 智能体观测
    输出: 动作概率分布
    
    集成技巧:
    - Trick 8: 正交初始化
    - Trick 10: Tanh激活函数
    """
    
    def __init__(self, obs_dim: int, action_dim: int, hidden_dim: int = 256, use_orthogonal_init: bool = True, use_tanh: bool = True):
        super(ActorNetwork, self).__init__()
        
        self.use_tanh = use_tanh
        activation = nn.Tanh if use_tanh else nn.ReLU
        
        # 构建网络层
        self.fc1 = nn.Linear(obs_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc_out = nn.Linear(hidden_dim, action_dim)
        
        self.activation = activation()
        
        # Trick 8: 正交初始化
        if use_orthogonal_init:
            self._orthogonal_init()
    
    def _orthogonal_init(self):
        """正交初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.orthogonal_(m.weight, gain=math.sqrt(2))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        
    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        """前向传播，输出动作概率分布"""
        x = self.activation(self.fc1(obs))
        x = self.activation(self.fc2(x))
        x = self.activation(self.fc3(x))
        x = self.fc_out(x)
        return F.softmax(x, dim=-1)
    
    def get_action_probs(self, obs: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取动作概率和熵"""
        action_probs = self.forward(obs)
        dist = torch.distributions.Categorical(action_probs)
        
        # 获取特定动作的概率
        selected_action_probs = action_probs.gather(1, actions.long().unsqueeze(-1)).squeeze(-1)
        
        # 计算熵
        entropy = dist.entropy()
        
        return selected_action_probs, entropy


class CriticNetwork(nn.Module):
    """
    Critic网络 - 价值网络 (集成PPO技巧版本)
    输入: 全局状态（集中式训练）
    输出: 状态价值
    
    集成技巧:
    - Trick 8: 正交初始化
    - Trick 10: Tanh激活函数
    """
    
    def __init__(self, state_dim: int, hidden_dim: int = 256, use_orthogonal_init: bool = True, use_tanh: bool = True):
        super(CriticNetwork, self).__init__()
        
        self.use_tanh = use_tanh
        activation = nn.Tanh if use_tanh else nn.ReLU
        
        # 构建网络层
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc_value = nn.Linear(hidden_dim, 1)
        
        self.activation = activation()
        
        # Trick 8: 正交初始化
        if use_orthogonal_init:
            self._orthogonal_init()
    
    def _orthogonal_init(self):
        """正交初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                if m == self.fc_value:
                    nn.init.orthogonal_(m.weight, gain=0.01)  # 价值层使用更小的gain
                else:
                    nn.init.orthogonal_(m.weight, gain=math.sqrt(2))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播，输出状态价值"""
        x = self.activation(self.fc1(state))
        x = self.activation(self.fc2(x))
        x = self.activation(self.fc3(x))
        value = self.fc_value(x)
        return value.squeeze(-1)


class ReplayBuffer:
    """
    经验缓冲区
    存储轨迹数据用于批量训练
    """
    
    def __init__(self, buffer_size: int = 10000):
        self.buffer_size = buffer_size
        self.buffer = []
        self.position = 0
        
    def push(self, transition: Dict[str, Any]):
        """添加经验数据"""
        if len(self.buffer) < self.buffer_size:
            self.buffer.append(transition)
        else:
            self.buffer[self.position] = transition
            self.position = (self.position + 1) % self.buffer_size
    
    def sample(self, batch_size: int) -> List[Dict[str, Any]]:
        """采样批量数据"""
        return random.sample(self.buffer, min(batch_size, len(self.buffer)))
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
        self.position = 0
    
    def size(self) -> int:
        """获取缓冲区大小"""
        return len(self.buffer)


class MAPPOAgent:
    """
    MAPPO智能体
    每个卫星对应一个智能体实例
    """
    
    def __init__(self, 
                 agent_id: str,
                 obs_dim: int, 
                 action_dim: int,
                 global_state_dim: int = None,  # 添加全局状态维度参数
                 lr_actor: float = 3e-4,
                 lr_critic: float = 1e-3,
                 hidden_dim: int = 512,  # 增加默认隐藏层维度，提升GPU利用率
                 device: str = 'cpu'):
        
        self.agent_id = agent_id
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        self.device = torch.device(device)
        
        # 网络初始化 (使用增强的网络架构)
        self.actor = ActorNetwork(obs_dim, action_dim, hidden_dim).to(self.device)
        # 修复：Critic网络使用global_state_dim进行集中式训练
        critic_input_dim = global_state_dim if global_state_dim is not None else obs_dim
        self.critic = CriticNetwork(critic_input_dim, hidden_dim).to(self.device)
        
        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr_actor)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr_critic)
        
        # 经验缓冲区
        self.buffer = ReplayBuffer()
        
    def select_action(self, obs: np.ndarray, valid_actions: List[int] = None) -> int:
        """
        选择动作（推理模式）
        
        Args:
            obs: 观测
            valid_actions: 有效动作列表
            
        Returns:
            int: 选择的动作
        """
        obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action_probs = self.actor(obs_tensor).squeeze(0)
            
            # 应用动作掩码
            if valid_actions is not None:
                mask = torch.zeros(self.action_dim)
                mask[valid_actions] = 1.0
                action_probs = action_probs * mask
                action_probs = action_probs / action_probs.sum()  # 重新归一化
            
            # 采样动作
            dist = torch.distributions.Categorical(action_probs)
            action = dist.sample()
            
        return action.item()
    
    def get_value(self, obs: np.ndarray) -> float:
        """获取状态价值"""
        obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            value = self.critic(obs_tensor)
            
        return value.item()


class MAPPOTrainer:
    """
    MAPPO训练器 - 集成PPO所有技巧的完整版本
    负责协调所有智能体的集中式训练
    
    修复：适配新环境接口，集成10大PPO技巧
    """
    
    def __init__(self,
                 num_agents: int,
                 obs_dim: int,
                 global_state_dim: int,
                 action_dim: int,
                 lr_actor: float = 3e-4,
                 lr_critic: float = 3e-4,  # 修复：使用与run_PPO一致的学习率
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_ratio: float = 0.2,
                 entropy_coef: float = 0.01,
                 value_coef: float = 0.5,
                 max_grad_norm: float = 0.5,
                 ppo_epochs: int = 10,  # 修复：使用与run_PPO一致的更新轮数
                 batch_size: int = 2048,  # 修复：使用更大的批量大小
                 mini_batch_size: int = 64,  # 新增：小批量大小
                 hidden_dim: int = 64,  # 修复：使用与run_PPO一致的隐藏层维度
                 # PPO技巧开关
                 use_state_norm: bool = True,
                 use_reward_norm: bool = False,
                 use_reward_scaling: bool = True,
                 use_lr_decay: bool = True,
                 use_orthogonal_init: bool = True,
                 use_tanh: bool = True,
                 adam_eps: float = 1e-5,  # Trick 9: Adam epsilon
                 device: str = 'cpu'):
        
        self.num_agents = num_agents
        self.obs_dim = obs_dim
        self.global_state_dim = global_state_dim
        self.action_dim = action_dim
        self.device = torch.device(device)
        
        # 超参数
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.max_grad_norm = max_grad_norm
        self.ppo_epochs = ppo_epochs
        self.batch_size = batch_size
        self.mini_batch_size = mini_batch_size
        
        # PPO技巧配置
        self.use_state_norm = use_state_norm
        self.use_reward_norm = use_reward_norm
        self.use_reward_scaling = use_reward_scaling
        self.use_lr_decay = use_lr_decay
        
        # 创建智能体网络（共享参数的方案）- 集成PPO技巧
        self.actor = ActorNetwork(
            obs_dim, action_dim, hidden_dim, 
            use_orthogonal_init=use_orthogonal_init, 
            use_tanh=use_tanh
        ).to(self.device)
        self.critic = CriticNetwork(
            global_state_dim, hidden_dim,
            use_orthogonal_init=use_orthogonal_init, 
            use_tanh=use_tanh
        ).to(self.device)
        
        # Trick 9: 优化器（设置Adam epsilon）
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr_actor, eps=adam_eps)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr_critic, eps=adam_eps)
        
        # Trick 6: 学习率衰减调度器
        if use_lr_decay:
            self.actor_lr_scheduler = LearningRateScheduler(
                self.actor_optimizer, lr_actor, decay_steps=1000, decay_rate=0.96
            )
            self.critic_lr_scheduler = LearningRateScheduler(
                self.critic_optimizer, lr_critic, decay_steps=1000, decay_rate=0.96
            )
        else:
            self.actor_lr_scheduler = None
            self.critic_lr_scheduler = None
        
        # Trick 2,3,4: 归一化器
        if use_state_norm:
            self.state_norm = Normalization(shape=obs_dim)
        else:
            self.state_norm = None
            
        if use_reward_norm:
            self.reward_norm = Normalization(shape=1)
        else:
            self.reward_norm = None
            
        if use_reward_scaling:
            self.reward_scaling = RewardScaling(shape=1, gamma=gamma)
        else:
            self.reward_scaling = None
        
        # 经验存储
        self.episode_data = []
        
        # GPU内存优化
        self._setup_gpu_optimization()
    
    def _setup_gpu_optimization(self):
        """设置GPU内存和异步处理优化"""
        if self.device.type == 'cuda':
            # 启用cudnn基准测试优化
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # 创建GPU异步流
            self.stream = torch.cuda.Stream()
            
            # 启用混合精度训练
            self.scaler = torch.cuda.amp.GradScaler()
            
            # 预分配GPU内存
            self._preallocate_gpu_memory()
            
            print(f"GPU优化已启用: {torch.cuda.get_device_name()}")
        else:
            self.stream = None
            self.scaler = None
    
    def _preallocate_gpu_memory(self):
        """预分配GPU内存以避免运行时分配开销"""
        try:
            # 预分配常用张量大小的内存
            dummy_obs = torch.zeros(self.batch_size, self.obs_dim, device=self.device)
            dummy_state = torch.zeros(self.batch_size, self.global_state_dim, device=self.device)
            dummy_actions = torch.zeros(self.batch_size, dtype=torch.long, device=self.device)
            
            # 执行一次前向传播预热GPU
            with torch.no_grad():
                _ = self.actor(dummy_obs)
                _ = self.critic(dummy_state)
            
            # 清理预热张量
            del dummy_obs, dummy_state, dummy_actions
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"GPU内存预分配失败: {e}")
    
    def select_actions(self, observations: Dict[str, np.ndarray], 
                      valid_actions_mask: Dict[str, List[int]]) -> Dict[str, int]:
        """
        为所有智能体选择动作 - 修复：支持扩展动作空间和状态归一化
        
        Args:
            observations: {agent_id: observation}
            valid_actions_mask: {agent_id: valid_actions}
            
        Returns:
            Dict[str, int]: {agent_id: action}
        """
        actions = {}
        
        for agent_id, obs in observations.items():
            # Trick 2: 状态归一化
            if self.state_norm is not None:
                obs = self.state_norm(obs, update=False)  # 推理时不更新
            
            obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(self.device)
            valid_actions = valid_actions_mask.get(agent_id, list(range(self.action_dim)))
            
            with torch.no_grad():
                action_probs = self.actor(obs_tensor).squeeze(0)
                
                # 修复：改进的动作掩码机制，支持扩展动作空间 + 增强健壮性
                if len(valid_actions) > 0:
                    # 创建掩码张量
                    mask = torch.zeros(self.action_dim, device=self.device)
                    # 确保有效动作索引在动作空间范围内
                    valid_indices = [idx for idx in valid_actions if 0 <= idx < self.action_dim]
                    if valid_indices:
                        mask[valid_indices] = 1.0
                        
                        # 掩码后重新归一化
                        masked_probs = action_probs * mask
                        mask_sum = masked_probs.sum()
                        if mask_sum > 1e-8:
                            action_probs = masked_probs / mask_sum
                        else:
                            # 修复：增强健壮性 - 如果掩码后概率和为0，均匀分布在有效动作上
                            mask_sum = mask.sum()
                            if mask_sum > 0:
                                action_probs = mask / mask_sum
                            else:
                                # 极端情况：如果没有有效动作，默认动作0
                                action_probs = torch.zeros(self.action_dim, device=self.device)
                                action_probs[0] = 1.0
                    else:
                        # 如果没有有效动作索引，默认选择动作0（本地处理）
                        action_probs = torch.zeros(self.action_dim, device=self.device)
                        action_probs[0] = 1.0
                else:
                    # 如果valid_actions为空，默认选择动作0
                    action_probs = torch.zeros(self.action_dim, device=self.device)
                    action_probs[0] = 1.0
                
                # 采样动作 - 修复：增强健壮性检查
                try:
                    # 验证概率分布的有效性
                    if torch.isnan(action_probs).any() or torch.isinf(action_probs).any():
                        # 如果有NaN或Inf，重置为默认动作
                        action = torch.tensor(0, device=self.device)
                    elif action_probs.sum() < 1e-8:
                        # 如果概率和太小，使用默认动作
                        action = torch.tensor(0, device=self.device)
                    else:
                        dist = torch.distributions.Categorical(action_probs)
                        action = dist.sample()
                except (ValueError, RuntimeError) as e:
                    # 如果分布创建或采样失败，默认选择动作0
                    action = torch.tensor(0, device=self.device)
                
            actions[agent_id] = action.item()
        
        return actions
    
    def store_transition(self, 
                        observations: Dict[str, np.ndarray],
                        global_state: np.ndarray,
                        actions: Dict[str, int],
                        rewards: Dict[str, float],
                        next_observations: Dict[str, np.ndarray],
                        next_global_state: np.ndarray,
                        dones: Dict[str, bool],
                        valid_actions_mask: Dict[str, List[int]]):
        """存储转换数据 - 修复：集成状态和奖励归一化"""
        # Trick 2: 状态归一化（训练时更新）
        normalized_observations = {}
        normalized_next_observations = {}
        
        for agent_id, obs in observations.items():
            if self.state_norm is not None:
                normalized_observations[agent_id] = self.state_norm(obs, update=True)
            else:
                normalized_observations[agent_id] = obs.copy()
        
        for agent_id, next_obs in next_observations.items():
            if self.state_norm is not None:
                normalized_next_observations[agent_id] = self.state_norm(next_obs, update=True)
            else:
                normalized_next_observations[agent_id] = next_obs.copy()
        
        # Trick 3,4: 奖励归一化和缩放
        processed_rewards = {}
        for agent_id, reward in rewards.items():
            processed_reward = reward
            
            # 应用奖励归一化
            if self.reward_norm is not None:
                processed_reward = self.reward_norm(processed_reward, update=True)
            
            # 应用奖励缩放
            if self.reward_scaling is not None:
                processed_reward = self.reward_scaling(processed_reward)
            
            processed_rewards[agent_id] = processed_reward
        
        transition = {
            'observations': normalized_observations,
            'global_state': global_state.copy(),
            'actions': actions.copy(),
            'rewards': processed_rewards,
            'next_observations': normalized_next_observations,
            'next_global_state': next_global_state.copy(),
            'dones': dones.copy(),
            'valid_actions_mask': valid_actions_mask.copy()
        }
        
        self.episode_data.append(transition)
    
    def compute_gae(self, rewards: List[float], values: List[float], next_values: List[float], 
                   dones: List[bool]) -> Tuple[List[float], List[float]]:
        """计算GAE优势函数和目标价值 - 修复：正确处理done信号"""
        advantages = []
        returns = []
        
        gae = 0
        for step in reversed(range(len(rewards))):
            # 修复：正确处理done信号
            if step == len(rewards) - 1:
                # 最后一步：如果done，next_value为0；否则使用next_values
                next_value = 0.0 if dones[step] else next_values[step]
            else:
                # 中间步骤：如果当前步done，next_value为0；否则使用下一步的value
                next_value = 0.0 if dones[step] else values[step + 1]
            
            # 计算TD误差
            delta = rewards[step] + self.gamma * next_value - values[step]
            
            # 计算GAE：如果done，则不从未来步骤传播优势
            gae = delta + self.gamma * self.gae_lambda * (1 - dones[step]) * gae
            
            advantages.insert(0, gae)
            returns.insert(0, gae + values[step])
        
        return advantages, returns
    
    def update(self) -> Dict[str, float]:
        """执行PPO更新"""
        if len(self.episode_data) == 0:
            return {}
        
        # 修复：优化数据处理，减少CPU-GPU传输 - 使用NumPy数组收集数据
        all_observations = []
        all_global_states = []
        all_actions = []
        all_rewards = []
        all_old_action_probs = []
        all_advantages = []
        all_returns = []
        
        # 预先收集所有数据为NumPy数组，减少重复的tensor创建
        agent_data = {}
        for agent_id in self.episode_data[0]['observations'].keys():
            agent_data[agent_id] = {
                'obs': [],
                'global_states': [],
                'actions': [],
                'rewards': [],
                'dones': []
            }
        
        # 收集所有episode数据
        for transition in self.episode_data:
            for agent_id in agent_data.keys():
                agent_data[agent_id]['obs'].append(transition['observations'][agent_id])
                agent_data[agent_id]['global_states'].append(transition['global_state'])
                agent_data[agent_id]['actions'].append(transition['actions'][agent_id])
                agent_data[agent_id]['rewards'].append(transition['rewards'][agent_id])
                agent_data[agent_id]['dones'].append(transition['dones'][agent_id])
        
        # 按智能体处理数据
        for agent_id, data in agent_data.items():
            # 转换为NumPy数组（一次性操作）
            agent_obs_np = np.array(data['obs'])
            agent_global_states_np = np.array(data['global_states'])
            agent_actions_np = np.array(data['actions'])
            agent_rewards = data['rewards']
            agent_dones = data['dones']
            
            # 批量计算价值（减少GPU调用次数）
            with torch.no_grad():
                # 当前状态价值
                global_states_tensor = torch.FloatTensor(agent_global_states_np).to(self.device)
                agent_values = self.critic(global_states_tensor).cpu().numpy().tolist()
                
                # 下一状态价值（使用next_global_state）
                next_global_states = []
                for transition in self.episode_data:
                    next_global_states.append(transition['next_global_state'])
                next_global_states_np = np.array(next_global_states)
                next_global_states_tensor = torch.FloatTensor(next_global_states_np).to(self.device)
                agent_next_values = self.critic(next_global_states_tensor).cpu().numpy().tolist()
            
            # 计算GAE
            advantages, returns = self.compute_gae(agent_rewards, agent_values, agent_next_values, agent_dones)
            
            # 批量计算旧动作概率
            with torch.no_grad():
                obs_tensor = torch.FloatTensor(agent_obs_np).to(self.device)
                actions_tensor = torch.LongTensor(agent_actions_np).to(self.device)
                old_action_probs, _ = self.actor.get_action_probs(obs_tensor, actions_tensor)
                old_action_probs_np = old_action_probs.cpu().numpy()
            
            # 汇总数据
            all_observations.extend(agent_obs_np.tolist())
            all_global_states.extend(agent_global_states_np.tolist())  
            all_actions.extend(agent_actions_np.tolist())
            all_rewards.extend(agent_rewards)
            all_old_action_probs.extend(old_action_probs_np.tolist())
            all_advantages.extend(advantages)
            all_returns.extend(returns)
        
        # 修复：优化数据转换 - 预先转换为NumPy数组，减少重复转换
        all_observations_np = np.array(all_observations, dtype=np.float32)
        all_global_states_np = np.array(all_global_states, dtype=np.float32)  
        all_actions_np = np.array(all_actions, dtype=np.int64)
        all_old_action_probs_np = np.array(all_old_action_probs, dtype=np.float32)
        all_advantages_np = np.array(all_advantages, dtype=np.float32)
        all_returns_np = np.array(all_returns, dtype=np.float32)
        
        # 一次性转换为GPU张量（减少CPU-GPU传输次数）
        with torch.cuda.stream(self.stream) if self.stream else torch.no_grad():
            obs_tensor = torch.from_numpy(all_observations_np).to(self.device, non_blocking=True)
            global_states_tensor = torch.from_numpy(all_global_states_np).to(self.device, non_blocking=True)
            actions_tensor = torch.from_numpy(all_actions_np).to(self.device, non_blocking=True)
            old_action_probs_tensor = torch.from_numpy(all_old_action_probs_np).to(self.device, non_blocking=True)
            advantages_tensor = torch.from_numpy(all_advantages_np).to(self.device, non_blocking=True)
            returns_tensor = torch.from_numpy(all_returns_np).to(self.device, non_blocking=True)
        
        # 等待异步传输完成
        if self.stream:
            torch.cuda.current_stream().wait_stream(self.stream)
        
        # Trick 1: 优势归一化
        advantages_tensor = (advantages_tensor - advantages_tensor.mean()) / (advantages_tensor.std() + 1e-8)
        
        # PPO更新
        total_actor_loss = 0
        total_critic_loss = 0
        total_entropy = 0
        
        dataset_size = len(all_observations_np)
        
        for epoch in range(self.ppo_epochs):
            # 随机打乱数据
            indices = torch.randperm(dataset_size)
            
            # 修复：使用mini_batch_size进行小批量训练，与run_PPO保持一致
            for start_idx in range(0, dataset_size, self.mini_batch_size):
                end_idx = min(start_idx + self.mini_batch_size, dataset_size)
                batch_indices = indices[start_idx:end_idx]
                
                batch_obs = obs_tensor[batch_indices]
                batch_global_states = global_states_tensor[batch_indices]
                batch_actions = actions_tensor[batch_indices]
                batch_old_action_probs = old_action_probs_tensor[batch_indices]
                batch_advantages = advantages_tensor[batch_indices]
                batch_returns = returns_tensor[batch_indices]
                
                # Actor更新（混合精度优化）
                if self.scaler is not None:
                    # 混合精度训练
                    with torch.cuda.amp.autocast():
                        new_action_probs, entropy = self.actor.get_action_probs(batch_obs, batch_actions)
                        ratio = new_action_probs / (batch_old_action_probs + 1e-8)
                        
                        surr1 = ratio * batch_advantages
                        surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * batch_advantages
                        actor_loss = -torch.min(surr1, surr2).mean() - self.entropy_coef * entropy.mean()
                    
                    self.actor_optimizer.zero_grad()
                    self.scaler.scale(actor_loss).backward()
                    self.scaler.unscale_(self.actor_optimizer)
                    torch.nn.utils.clip_grad_norm_(self.actor.parameters(), self.max_grad_norm)
                    self.scaler.step(self.actor_optimizer)
                    self.scaler.update()
                else:
                    # 标准精度训练
                    new_action_probs, entropy = self.actor.get_action_probs(batch_obs, batch_actions)
                    ratio = new_action_probs / (batch_old_action_probs + 1e-8)
                    
                    surr1 = ratio * batch_advantages
                    surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * batch_advantages
                    actor_loss = -torch.min(surr1, surr2).mean() - self.entropy_coef * entropy.mean()
                    
                    self.actor_optimizer.zero_grad()
                    actor_loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.actor.parameters(), self.max_grad_norm)
                    self.actor_optimizer.step()
                
                # Critic更新（混合精度优化）
                if self.scaler is not None:
                    with torch.cuda.amp.autocast():
                        values = self.critic(batch_global_states)
                        critic_loss = F.mse_loss(values, batch_returns)
                    
                    self.critic_optimizer.zero_grad()
                    self.scaler.scale(critic_loss).backward()  
                    self.scaler.unscale_(self.critic_optimizer)
                    torch.nn.utils.clip_grad_norm_(self.critic.parameters(), self.max_grad_norm)
                    self.scaler.step(self.critic_optimizer)
                    self.scaler.update()
                else:
                    values = self.critic(batch_global_states)
                    critic_loss = F.mse_loss(values, batch_returns)
                    
                    self.critic_optimizer.zero_grad()
                    critic_loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.critic.parameters(), self.max_grad_norm)
                    self.critic_optimizer.step()
                
                total_actor_loss += actor_loss.item()
                total_critic_loss += critic_loss.item()
                total_entropy += entropy.mean().item()
        
        # Trick 6: 学习率衰减
        if self.actor_lr_scheduler is not None:
            self.actor_lr_scheduler.step()
        if self.critic_lr_scheduler is not None:
            self.critic_lr_scheduler.step()
        
        # 清空episode数据
        self.episode_data.clear()
        
        # 修复：根据mini_batch_size计算更新次数
        num_updates = (dataset_size // self.mini_batch_size) * self.ppo_epochs
        if num_updates == 0:
            num_updates = 1
            
        # 添加学习率信息到返回值
        update_info = {
            'actor_loss': total_actor_loss / num_updates,
            'critic_loss': total_critic_loss / num_updates,
            'entropy': total_entropy / num_updates
        }
        
        # 添加学习率信息（如果启用衰减）
        if self.actor_lr_scheduler is not None:
            update_info['actor_lr'] = self.actor_lr_scheduler.get_lr()
        if self.critic_lr_scheduler is not None:
            update_info['critic_lr'] = self.critic_lr_scheduler.get_lr()
            
        return update_info
    
    def save_models(self, filepath: str):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
        }, filepath)
    
    def load_models(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])