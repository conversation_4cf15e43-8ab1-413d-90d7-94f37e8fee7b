# SPACE-OAAL PettingZoo Environment

## 概述

基于PettingZoo框架重建的SPACE-OAAL卫星边缘计算多智能体强化学习环境，提供标准的PettingZoo API接口，支持并行和AEC两种执行模式。

## 主要特性

### ✅ 完全兼容PettingZoo
- 标准的ParallelEnv和AECEnv接口
- 支持所有主流多智能体强化学习算法
- 与已修复的核心模块完全集成

### ✅ 双执行模式
- **并行模式**: 所有智能体同时执行动作 (适用于MAPPO、Independent PPO等)
- **AEC模式**: 智能体轮流执行动作 (适用于序列决策算法)

### ✅ 完整仿真功能
- 36颗LEO卫星的轨道动力学仿真
- 420个地面站的通信覆盖
- 真实的任务生成和分配
- 云服务器协作处理

## 快速开始

### 基本使用

```python
from src.env.satellite_env import parallel_env, aec_env

# 创建并行环境
env = parallel_env()
observations, infos = env.reset()

# 执行动作
actions = {agent: 0 for agent in env.agents}  # 所有智能体选择本地处理
observations, rewards, dones, infos = env.step(actions)

env.close()
```

### 高级使用

```python
# 使用自定义配置
env = parallel_env(config_file="path/to/your/config.yaml")

# 获取环境信息
print(f"智能体数量: {env.num_agents}")
print(f"观测空间: {env.observation_space}")
print(f"动作空间: {env.action_space}")

# 使用有效动作掩码
observations, infos = env.reset()
for agent in env.agents:
    valid_actions = infos[agent]['valid_actions']
    action_mask = infos[agent]['valid_actions_mask']
    print(f"{agent} 可用动作: {valid_actions}")
```

## 环境接口

### 观测空间
- **维度**: 15维连续向量
- **范围**: [-1.0, 1.0]
- **内容**: 
  - 位置信息 (4维): 纬度、经度、高度、光照状态
  - 能量信息 (2维): 电池比例、低电量状态
  - 资源信息 (2维): CPU利用率、内存利用率
  - 任务信息 (4维): 队列长度、平均优先级、最小紧急度、完成率
  - 通信信息 (2维): 邻居数量、地面站数量
  - 状态信息 (1维): 健康状态

### 动作空间
- **类型**: 离散动作
- **大小**: 3
- **动作**:
  - 0: 本地处理 (总是可用)
  - 1: 任务卸载到其他卫星 (需要可见邻居和队列中有任务)
  - 2: 任务卸载到云服务器 (需要可见云中心和队列中有任务)

### 奖励函数
```python
reward = 1.0 * completed_tasks - 2.0 * failed_tasks + energy_bonus + load_balance_bonus
```

- **任务完成奖励**: **** 每完成一个任务
- **任务失败惩罚**: -2.0 每失败一个任务  
- **能量效率奖励**: +0.1 (电池>20%) 或 -0.5 (电池≤20%)
- **负载均衡奖励**: +0.1 (队列长度1-5) 或 -0.2 (队列长度>10)

## 环境架构

```
SatelliteParallelEnv/SatelliteAECEnv (PettingZoo接口)
    ↓
SatelliteEnvironmentCore (仿真逻辑核心)
    ↓
SatelliteAdapter + TaskAdapter (已修复的适配器)
    ↓
OrbitalUpdater + CommunicationManager + CloudServerManager
```

## 与强化学习算法集成

### 使用Ray RLLib

```python
import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.env.wrappers.pettingzoo_env import PettingZooEnv

# 环境包装
def env_creator(args):
    from src.env.satellite_env import parallel_env
    return PettingZooEnv(parallel_env())

# 注册环境
tune.register_env("satellite_env", env_creator)

# 配置算法
config = (
    PPOConfig()
    .environment("satellite_env")
    .multi_agent(
        policies={"shared_policy"},
        policy_mapping_fn=lambda agent_id, *args, **kwargs: "shared_policy",
    )
)

# 训练
algo = config.build()
```

### 使用CleanRL

```python
import gymnasium as gym
from pettingzoo.utils import parallel_to_aec

# 创建环境
from src.env.satellite_env import parallel_env
env = parallel_env()

# 转换为gymnasium兼容格式 (如果需要)
# env = parallel_to_aec(env)
```

## 配置选项

环境支持通过config.yaml文件进行配置：

```yaml
system:
  num_leo_satellites: 36
  total_timeslots: 100
  timeslot_duration_s: 10

computation:
  f_leo_hz: 1000e9
  leo_battery_capacity_j: 3600000
  
communication:
  max_retries: 3
  
# ... 更多配置选项
```

## 测试和验证

运行内置测试：

```bash
cd src/env
python satellite_env.py
```

输出示例：
```
=== SPACE-OAAL 基于PettingZoo的环境测试 ===

1. 测试并行环境...
环境创建成功: 36 个智能体
观测空间: Box(-1.0, 1.0, (15,), float32)
动作空间: Discrete(3)
Step 0: 36 observations, reward sum: 3.60
并行环境测试完成

2. 测试AEC环境...
AEC环境创建成功: 36 个智能体
AEC环境测试完成
```

## 注意事项

### 已修复的核心模块兼容性
- 新环境完全兼容已修复的task.py、satellite.py、adapters.py
- 正确传递仿真时间参数
- 使用依赖注入模式初始化所有模块

### 性能考虑
- 36个智能体的大规模多智能体环境
- 建议使用参数共享减少计算开销
- 支持有效动作掩码优化训练效率

### 扩展性
- 可通过修改动作空间支持更多动作类型
- 可通过修改奖励函数调整学习目标
- 可通过配置文件调整仿真参数

## 故障排除

1. **导入错误**: 确保所有依赖模块已正确安装
2. **配置错误**: 检查config.yaml文件格式和路径
3. **内存不足**: 考虑减少timeslots数量或卫星数量
4. **性能问题**: 使用logging模块调整日志级别

## 更新日志

- **v1.0**: 基于PettingZoo重构，完全替代原有env_interface
- 支持并行和AEC两种执行模式
- 与所有已修复核心模块完全兼容
- 标准的PettingZoo API接口