"""
TransformerScheduler测试文件
验证修改后的Transformer调度器功能和性能
"""

import torch
import torch.nn as nn
import numpy as np
import time
import unittest
from typing import List, Dict, Tuple
import sys
import os

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from transformer_scheduler import TransformerScheduler, PositionalEncoding


class TestTransformerScheduler(unittest.TestCase):
    """TransformerScheduler测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.scheduler = TransformerScheduler(
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,  # 减小模型用于测试
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        ).to(self.device)
        
    def test_initialization(self):
        """测试模型初始化"""
        # 检查模型参数
        self.assertEqual(self.scheduler.d_model, 128)
        self.assertEqual(self.scheduler.num_actions, 42)
        
        # 检查是否正确移除了未使用的task_token
        self.assertTrue(hasattr(self.scheduler, 'start_token'))
        self.assertFalse(hasattr(self.scheduler, 'task_token'))
        
        # 检查各个组件是否正确初始化
        self.assertIsInstance(self.scheduler.obs_embedding, nn.Linear)
        self.assertIsInstance(self.scheduler.task_embedding, nn.Linear)
        self.assertIsInstance(self.scheduler.pos_encoding, PositionalEncoding)
        self.assertIsInstance(self.scheduler.encoder, nn.TransformerEncoder)
        self.assertIsInstance(self.scheduler.decoder, nn.TransformerDecoder)
        self.assertIsInstance(self.scheduler.output_projection, nn.Linear)
        
    def test_positional_encoding(self):
        """测试位置编码"""
        pe = PositionalEncoding(d_model=128, max_len=50)
        x = torch.randn(2, 10, 128)  # [batch, seq, dim]
        output = pe(x)
        
        # 检查输出形状
        self.assertEqual(output.shape, x.shape)
        
        # 检查位置编码是否被正确添加
        self.assertFalse(torch.equal(output, x))
        
    def test_encode_observation(self):
        """测试观测编码"""
        batch_size = 3
        obs = torch.randn(batch_size, 15).to(self.device)
        
        encoded = self.scheduler.encode_observation(obs)
        
        # 检查输出形状: [batch, 1, d_model]
        expected_shape = (batch_size, 1, self.scheduler.d_model)
        self.assertEqual(encoded.shape, expected_shape)
        
    def test_encode_tasks(self):
        """测试任务编码"""
        batch_size = 2
        num_tasks = 5
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        
        encoded = self.scheduler.encode_tasks(task_features)
        
        # 检查输出形状: [batch, num_tasks, d_model]
        expected_shape = (batch_size, num_tasks, self.scheduler.d_model)
        self.assertEqual(encoded.shape, expected_shape)
        
        # 测试2D输入的自动扩展
        task_features_2d = torch.randn(num_tasks, 8).to(self.device)
        encoded_2d = self.scheduler.encode_tasks(task_features_2d)
        expected_shape_2d = (1, num_tasks, self.scheduler.d_model)
        self.assertEqual(encoded_2d.shape, expected_shape_2d)
        
    def test_causal_mask_generation(self):
        """测试因果掩码生成"""
        sz = 5
        mask = self.scheduler._generate_square_subsequent_mask(sz)
        
        # 检查掩码类型和形状
        self.assertEqual(mask.dtype, torch.bool)
        self.assertEqual(mask.shape, (sz, sz))
        
        # 检查掩码结构（上三角为True）
        expected_mask = torch.triu(torch.ones(sz, sz), diagonal=1).bool()
        self.assertTrue(torch.equal(mask, expected_mask))
        
        # 检查对角线和下三角应该为False
        for i in range(sz):
            for j in range(i+1):
                self.assertFalse(mask[i, j].item())
            for j in range(i+1, sz):
                self.assertTrue(mask[i, j].item())
                
    def test_forward_pass(self):
        """测试前向传播"""
        batch_size = 2
        num_tasks = 4
        
        # 准备输入
        observation = torch.randn(batch_size, 15).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        action_mask = torch.ones(batch_size, num_tasks, 42, dtype=torch.bool).to(self.device)
        
        # 前向传播
        output = self.scheduler.forward(observation, task_features, action_mask)
        
        # 检查输出形状
        expected_shape = (batch_size, num_tasks, self.scheduler.num_actions)
        self.assertEqual(output.shape, expected_shape)
        
        # 检查概率和为1
        prob_sums = output.sum(dim=-1)
        expected_sums = torch.ones(batch_size, num_tasks).to(self.device)
        self.assertTrue(torch.allclose(prob_sums, expected_sums, atol=1e-6))
        
        # 检查所有概率非负
        self.assertTrue((output >= 0).all())
        self.assertTrue((output <= 1).all())
        
    def test_forward_without_mask(self):
        """测试不使用动作掩码的前向传播"""
        batch_size = 1
        num_tasks = 3
        
        observation = torch.randn(batch_size, 15).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        
        # 不提供action_mask
        output = self.scheduler.forward(observation, task_features, action_mask=None)
        
        # 检查输出形状
        expected_shape = (batch_size, num_tasks, self.scheduler.num_actions)
        self.assertEqual(output.shape, expected_shape)
        
        # 检查概率和为1
        prob_sums = output.sum(dim=-1)
        expected_sums = torch.ones(batch_size, num_tasks).to(self.device)
        self.assertTrue(torch.allclose(prob_sums, expected_sums, atol=1e-6))
        
    def test_action_mask_application(self):
        """测试动作掩码的正确应用"""
        batch_size = 1
        num_tasks = 2
        
        observation = torch.randn(batch_size, 15).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        
        # 创建限制性的动作掩码（只允许某些动作）
        action_mask = torch.zeros(batch_size, num_tasks, 42, dtype=torch.bool).to(self.device)
        action_mask[0, 0, [0, 1, 2]] = True  # 第一个任务只允许动作0,1,2
        action_mask[0, 1, [5, 10]] = True    # 第二个任务只允许动作5,10
        
        output = self.scheduler.forward(observation, task_features, action_mask)
        
        # 检查被掩码的动作概率为0
        self.assertTrue((output[0, 0, 3:] == 0).all())  # 第一个任务的动作3及以后应为0
        self.assertTrue((output[0, 1, [0,1,2,3,4,6,7,8,9]] == 0).all())  # 第二个任务除5,10外应为0
        
        # 检查允许的动作概率大于0
        self.assertTrue((output[0, 0, :3] > 0).all())
        self.assertTrue(output[0, 1, 5] > 0)
        self.assertTrue(output[0, 1, 10] > 0)
        
    def test_different_sequence_lengths(self):
        """测试不同序列长度的处理"""
        batch_size = 1
        
        observation = torch.randn(batch_size, 15).to(self.device)
        
        # 测试不同数量的任务
        for num_tasks in [1, 3, 5, 8]:
            task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
            
            output = self.scheduler.forward(observation, task_features)
            
            expected_shape = (batch_size, num_tasks, self.scheduler.num_actions)
            self.assertEqual(output.shape, expected_shape)
            
            # 检查概率和
            prob_sums = output.sum(dim=-1)
            expected_sums = torch.ones(batch_size, num_tasks).to(self.device)
            self.assertTrue(torch.allclose(prob_sums, expected_sums, atol=1e-6))
            
    def test_gradient_flow(self):
        """测试梯度流动"""
        batch_size = 2
        num_tasks = 3
        
        observation = torch.randn(batch_size, 15, requires_grad=True).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8, requires_grad=True).to(self.device)
        
        output = self.scheduler.forward(observation, task_features)
        
        # 计算一个简单的损失
        target = torch.randint(0, 42, (batch_size, num_tasks)).to(self.device)
        loss = nn.CrossEntropyLoss()(output.view(-1, 42), target.view(-1))
        
        # 反向传播
        loss.backward()
        
        # 检查梯度是否存在
        self.assertIsNotNone(observation.grad)
        self.assertIsNotNone(task_features.grad)
        
        # 检查模型参数是否有梯度
        for param in self.scheduler.parameters():
            if param.requires_grad:
                self.assertIsNotNone(param.grad)


class TestTransformerSchedulerPerformance(unittest.TestCase):
    """TransformerScheduler性能测试类"""
    
    def setUp(self):
        """性能测试初始化"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.scheduler = TransformerScheduler(
            obs_dim=15,
            task_feature_dim=8,
            d_model=256,
            nhead=8,
            num_encoder_layers=3,
            num_decoder_layers=3,
            num_actions=42
        ).to(self.device)
        
    def test_inference_speed(self):
        """测试推理速度"""
        batch_size = 8
        num_tasks = 10
        num_iterations = 50
        
        observation = torch.randn(batch_size, 15).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        
        # 预热
        with torch.no_grad():
            for _ in range(5):
                _ = self.scheduler.forward(observation, task_features)
        
        # 计时测试
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = self.scheduler.forward(observation, task_features)
                
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_iterations
        throughput = batch_size / avg_time
        
        print(f"Average inference time: {avg_time:.4f}s")
        print(f"Throughput: {throughput:.2f} samples/s")
        
        # 基本性能检查（应该能在合理时间内完成）
        self.assertLess(avg_time, 1.0)  # 每次推理应少于1秒
        
    def test_memory_usage(self):
        """测试内存使用"""
        if not torch.cuda.is_available():
            self.skipTest("CUDA not available")
            
        batch_size = 4
        num_tasks = 8
        
        observation = torch.randn(batch_size, 15).to(self.device)
        task_features = torch.randn(batch_size, num_tasks, 8).to(self.device)
        
        # 记录初始GPU内存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 前向传播
        output = self.scheduler.forward(observation, task_features)
        peak_memory = torch.cuda.memory_allocated()
        
        # 计算内存使用
        memory_used = (peak_memory - initial_memory) / 1024 / 1024  # MB
        print(f"Memory used: {memory_used:.2f} MB")
        
        # 基本内存检查（应该在合理范围内）
        self.assertLess(memory_used, 500)  # 应少于500MB
        
    def test_scalability(self):
        """测试可扩展性"""
        task_sizes = [5, 10, 15, 20]
        times = []
        
        for num_tasks in task_sizes:
            observation = torch.randn(1, 15).to(self.device)
            task_features = torch.randn(1, num_tasks, 8).to(self.device)
            
            # 计时
            start_time = time.time()
            with torch.no_grad():
                for _ in range(10):
                    _ = self.scheduler.forward(observation, task_features)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            times.append(avg_time)
            print(f"Tasks: {num_tasks}, Time: {avg_time:.4f}s")
        
        # 检查时间复杂度（应该是线性或接近线性增长）
        for i in range(1, len(times)):
            growth_ratio = times[i] / times[i-1]
            task_ratio = task_sizes[i] / task_sizes[i-1]
            # 时间增长应该不超过任务数量增长的平方
            self.assertLess(growth_ratio, task_ratio ** 2)


def run_comprehensive_test():
    """运行综合测试"""
    print("=== TransformerScheduler综合测试 ===\n")
    
    # 功能测试
    print("1. 运行功能测试...")
    suite1 = unittest.TestLoader().loadTestsFromTestCase(TestTransformerScheduler)
    runner1 = unittest.TextTestRunner(verbosity=2)
    result1 = runner1.run(suite1)
    
    print(f"\n功能测试结果: {result1.testsRun}个测试, {len(result1.failures)}个失败, {len(result1.errors)}个错误\n")
    
    # 性能测试
    print("2. 运行性能测试...")
    suite2 = unittest.TestLoader().loadTestsFromTestCase(TestTransformerSchedulerPerformance)
    runner2 = unittest.TextTestRunner(verbosity=2)
    result2 = runner2.run(suite2)
    
    print(f"\n性能测试结果: {result2.testsRun}个测试, {len(result2.failures)}个失败, {len(result2.errors)}个错误\n")
    
    # 总结
    total_tests = result1.testsRun + result2.testsRun
    total_failures = len(result1.failures) + len(result2.failures)
    total_errors = len(result1.errors) + len(result2.errors)
    
    print("=== 测试总结 ===")
    print(f"总测试数: {total_tests}")
    print(f"成功: {total_tests - total_failures - total_errors}")
    print(f"失败: {total_failures}")
    print(f"错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ 所有测试通过！")
    else:
        print("❌ 存在测试失败或错误")
    
    return total_failures == 0 and total_errors == 0


if __name__ == "__main__":
    run_comprehensive_test()