# 卫星节点程序架构图

## 1. 程序架构概述

卫星节点模块是SPACE-OAAL系统的核心组件，作为智能体在仿真世界中的物理载体。该模块采用面向对象的设计模式，以 `SatelliteNode` 为核心类，管理卫星的完整状态信息、行为执行和接口提供。架构特点包括：

- **完整状态管理**：位置、能量、资源、任务、通信等多维度状态维护
- **行为执行机制**：任务处理、能量管理、资源分配等核心行为
- **多模块集成**：与轨道更新、通信、任务管理模块的无缝集成
- **数据驱动设计**：基于YAML配置文件和状态历史记录
- **观测与调度接口**：为强化学习算法提供标准化的观测和调度接口

## 2. Draw.io架构图绘制指导

### 2.1 图形元素和颜色方案

#### 主要模块形状建议：
- **核心控制器类**：深蓝色矩形 (#1f4e79, 宽度220px, 高度90px)
- **数据结构类**：浅绿色圆角矩形 (#70ad47, 宽度160px, 高度70px, 圆角半径10px)
- **枚举类**：紫色菱形 (#7030a0, 宽度120px, 高度60px)
- **状态管理函数**：橙色圆角矩形 (#f4b942, 宽度180px, 高度55px, 圆角半径8px)
- **行为执行函数**：深橙色圆角矩形 (#d9730d, 宽度170px, 高度55px, 圆角半径8px)
- **接口函数**：蓝色圆角矩形 (#5b9bd5, 宽度160px, 高度50px, 圆角半径8px)
- **工具函数**：灰色圆角矩形 (#7f7f7f, 宽度150px, 高度45px, 圆角半径6px)
- **外部依赖**：黄色六边形 (#ffc000, 宽度140px, 高度60px)
- **配置文件**：浅黄色平行四边形 (#fff2cc, 宽度130px, 高度45px)
- **主函数入口**：红色六边形 (#c5504b, 宽度100px, 高度60px)

#### 连接线类型：
- **实线箭头**：直接函数调用 (线宽2px, 黑色 #000000)
- **虚线箭头**：数据传递/依赖 (虚线, 线宽1.5px, 深灰色 #404040)
- **双向箭头**：相互调用关系 (线宽2px, 蓝色 #0066cc)
- **粗线**：主要数据流 (线宽3px, 绿色 #00aa00)
- **点线**：状态更新 (点线, 线宽1.5px, 紫色 #7030a0)

### 2.2 布局建议（从上到下）

```
第1层：程序入口层 (Y坐标: 50px)
├── main() 函数 [红色六边形]

第2层：外部依赖层 (Y坐标: 150px)
├── orbital_updater模块 [黄色六边形]
├── communication模块 [黄色六边形]
├── task模块 [黄色六边形]
└── config.yaml [浅黄色平行四边形]

第3层：枚举和数据结构层 (Y坐标: 250px)
├── SatelliteStatus枚举 [紫色菱形]
├── ResourceType枚举 [紫色菱形]
├── Position数据类 [浅绿色圆角矩形]
├── EnergyState数据类 [浅绿色圆角矩形]
├── ResourceState数据类 [浅绿色圆角矩形]
├── CommunicationState数据类 [浅绿色圆角矩形]
├── TaskQueue数据类 [浅绿色圆角矩形]
└── PerformanceMetrics数据类 [浅绿色圆角矩形]

第4层：核心控制层 (Y坐标: 400px)
├── SatelliteNode类 [深蓝色矩形]

第5层：状态管理层 (Y坐标: 520px)
├── get_position_info() [橙色圆角矩形]
├── get_energy_info() [橙色圆角矩形]
├── get_resource_info() [橙色圆角矩形]
├── get_task_queue_info() [橙色圆角矩形]
├── get_communication_info() [橙色圆角矩形]
├── get_performance_metrics() [橙色圆角矩形]
└── get_system_status() [橙色圆角矩形]

第6层：行为执行层 (Y坐标: 620px)
├── update_position() [深橙色圆角矩形]
├── update_energy() [深橙色圆角矩形]
├── process_tasks() [深橙色圆角矩形]
├── execute_task_offload() [深橙色圆角矩形]
├── receive_task() [深橙色圆角矩形]
└── step() [深橙色圆角矩形]

第7层：接口层 (Y坐标: 720px)
├── sync_with_orbital_state() [蓝色圆角矩形]
├── sync_with_communication_state() [蓝色圆角矩形]
├── sync_with_task_state() [蓝色圆角矩形]
├── export_observation_data() [蓝色圆角矩形]
└── export_for_scheduling() [蓝色圆角矩形]

第8层：工具函数层 (Y坐标: 820px)
├── _sort_task_queue() [灰色圆角矩形]
├── _complete_task() [灰色圆角矩形]
├── _fail_task() [灰色圆角矩形]
├── _execute_transmission() [灰色圆角矩形]
├── _update_resource_utilization() [灰色圆角矩形]
└── _record_state_history() [灰色圆角矩形]

第9层：状态检查层 (Y坐标: 920px)
├── is_healthy() [灰色圆角矩形]
├── can_accept_tasks() [灰色圆角矩形]
├── can_process_tasks() [灰色圆角矩形]
├── has_available_resources() [灰色圆角矩形]
└── get_best_offload_target() [灰色圆角矩形]
```

### 2.3 分组和层次组织

#### 主要分组容器：
1. **外部依赖组** (虚线边框, 浅灰色背景 #f5f5f5)
   - 包含orbital_updater、communication、task模块和配置文件
   
2. **数据模型组** (实线边框, 浅绿色背景 #e6ffe6)
   - 包含枚举和数据类
   
3. **核心控制组** (实线边框, 浅蓝色背景 #e6f2ff)
   - 包含SatelliteNode类
   
4. **状态管理组** (实线边框, 浅橙色背景 #fff2e6)
   - 包含状态查询和管理函数
   
5. **行为执行组** (实线边框, 深橙色背景 #ffd7b5)
   - 包含行为执行函数
   
6. **接口服务组** (实线边框, 浅蓝色背景 #dae8fc)
   - 包含同步和导出接口
   
7. **工具支持组** (虚线边框, 浅灰色背景 #f8f8f8)
   - 包含工具函数和状态检查函数

## 3. 程序模块详细描述

### 3.1 核心控制模块 - SatelliteNode类
- **程序模块名称**：卫星节点核心类
- **文件位置**：`src/env/satellite.py` (第102-600行)
- **主要功能和职责**：
  - 作为智能体在仿真世界中的物理载体
  - 维护卫星的完整状态信息
  - 执行来自环境或智能体的动作指令
  - 提供状态查询和观测数据
  - 管理任务队列和资源分配

- **关键属性和方法**：
  ```python
  class SatelliteNode:
      # 核心属性
      satellite_id: str                         # 卫星ID
      status: SatelliteStatus                   # 卫星状态
      position: Position                        # 位置状态
      energy_state: EnergyState                 # 能量状态
      resource_state: ResourceState             # 资源状态
      communication_state: CommunicationState   # 通信状态
      task_queue: TaskQueue                     # 任务队列
      performance_metrics: PerformanceMetrics   # 性能指标
      
      # 核心方法
      step(time_step, time_delta)               # 执行时间步
      process_tasks(time_step, time_delta)      # 处理任务
      update_energy(energy_delta, time_delta)   # 更新能量
      execute_task_offload(task_id, target_id)  # 执行任务卸载
      export_observation_data()                 # 导出观测数据
  ```

- **对外提供的接口**：
  - `sync_with_orbital_state(OrbitalSatellite)` - 轨道状态同步
  - `sync_with_communication_state(NetworkState, int)` - 通信状态同步
  - `sync_with_task_state(List[Task])` - 任务状态同步
  - `export_observation_data() -> Dict` - 导出观测数据
  - `export_for_scheduling() -> Dict` - 导出调度数据

### 3.2 数据结构模块

#### 3.2.1 SatelliteStatus枚举
- **程序模块名称**：卫星状态枚举
- **文件位置**：`src/env/satellite.py` (第30-35行)
- **枚举值**：
  - ACTIVE: 活跃状态
  - INACTIVE: 非活跃状态
  - FAILED: 失败状态
  - MAINTENANCE: 维护状态

#### 3.2.2 Position数据类
- **程序模块名称**：位置状态
- **文件位置**：`src/env/satellite.py` (第45-52行)
- **关键属性**：
  ```python
  @dataclass
  class Position:
      latitude: float = 0.0                     # 纬度
      longitude: float = 0.0                    # 经度
      altitude: float = 0.0                     # 高度
      velocity_lat: float = 0.0                 # 纬度速度
      velocity_lon: float = 0.0                 # 经度速度
      timestamp: float = 0.0                    # 时间戳
  ```

#### 3.2.3 EnergyState数据类
- **程序模块名称**：能量状态
- **文件位置**：`src/env/satellite.py` (第55-75行)
- **关键属性和方法**：
  ```python
  @dataclass
  class EnergyState:
      current_battery_j: float = 0.0            # 当前电量
      battery_capacity_j: float = 0.0           # 电池容量
      solar_power_w: float = 0.0                # 太阳能功率
      power_consumption_w: float = 0.0          # 功耗
      charging_rate_w: float = 0.0              # 充电率
      is_illuminated: bool = False              # 是否有光照
      energy_threshold_j: float = 0.0           # 能量阈值
      
      @property
      def battery_ratio(self) -> float          # 电池电量比例
      @property
      def is_low_energy(self) -> bool           # 是否低能量
  ```

#### 3.2.4 ResourceState数据类
- **程序模块名称**：资源状态
- **文件位置**：`src/env/satellite.py` (第78-105行)
- **关键属性和方法**：
  ```python
  @dataclass
  class ResourceState:
      cpu_frequency_hz: float = 0.0             # CPU频率
      cpu_utilization: float = 0.0              # CPU利用率
      memory_total_mb: float = 0.0              # 总内存
      memory_used_mb: float = 0.0               # 已用内存
      storage_total_mb: float = 0.0             # 总存储
      storage_used_mb: float = 0.0              # 已用存储
      bandwidth_total_mbps: float = 0.0         # 总带宽
      bandwidth_used_mbps: float = 0.0          # 已用带宽
      
      @property
      def cpu_available_hz(self) -> float       # 可用CPU
      @property
      def memory_available_mb(self) -> float    # 可用内存
      @property
      def storage_available_mb(self) -> float   # 可用存储
      @property
      def bandwidth_available_mbps(self) -> float # 可用带宽
  ```

#### 3.2.5 CommunicationState数据类
- **程序模块名称**：通信状态
- **文件位置**：`src/env/satellite.py` (第108-125行)
- **关键属性和方法**：
  ```python
  @dataclass
  class CommunicationState:
      neighbors: List[str] = field(default_factory=list)           # 邻居列表
      active_links: Dict[str, LinkState] = field(default_factory=dict) # 活跃链路
      ground_stations: List[str] = field(default_factory=list)     # 地面站列表
      network_quality: float = 0.0                                 # 网络质量
      link_count: int = 0                                          # 链路数量
      
      @property
      def neighbor_count(self) -> int                              # 邻居数量
      @property
      def active_link_count(self) -> int                           # 活跃链路数量
  ```

#### 3.2.6 TaskQueue数据类
- **程序模块名称**：任务队列状态
- **文件位置**：`src/env/satellite.py` (第128-145行)
- **关键属性和方法**：
  ```python
  @dataclass
  class TaskQueue:
      queued_tasks: List[str] = field(default_factory=list)        # 排队任务
      processing_tasks: List[str] = field(default_factory=list)    # 处理中任务
      completed_tasks: List[str] = field(default_factory=list)     # 完成任务
      failed_tasks: List[str] = field(default_factory=list)        # 失败任务
      task_priorities: Dict[str, float] = field(default_factory=dict) # 任务优先级
      
      @property
      def queue_length(self) -> int                                # 队列长度
      @property
      def total_tasks(self) -> int                                 # 总任务数
  ```

#### 3.2.7 PerformanceMetrics数据类
- **程序模块名称**：性能指标
- **文件位置**：`src/env/satellite.py` (第148-175行)
- **关键属性和方法**：
  ```python
  @dataclass
  class PerformanceMetrics:
      total_tasks_processed: int = 0                               # 处理任务总数
      total_tasks_completed: int = 0                               # 完成任务总数
      total_tasks_failed: int = 0                                  # 失败任务总数
      total_energy_consumed_j: float = 0.0                         # 总能耗
      total_processing_time_s: float = 0.0                         # 总处理时间
      average_response_time_s: float = 0.0                         # 平均响应时间
      cpu_utilization_history: List[float] = field(default_factory=list) # CPU利用率历史
      energy_level_history: List[float] = field(default_factory=list)    # 能量水平历史
      queue_length_history: List[int] = field(default_factory=list)      # 队列长度历史
      
      @property
      def completion_rate(self) -> float                           # 完成率
      @property
      def failure_rate(self) -> float                              # 失败率
  ```

## 4. 程序执行流程说明

### 4.1 程序启动和初始化流程
```
1. SatelliteNode.__init__(satellite_id, config) [初始化入口]
   ↓ [实线箭头]
2. _load_default_config() 加载配置
   ├── 尝试从多个路径加载config.yaml [虚线箭头]
   └── 如果失败，使用_get_fallback_config()
   ↓ [实线箭头]
3. 初始化状态对象
   ├── status = SatelliteStatus.ACTIVE
   ├── position = Position()
   ├── energy_state = EnergyState()
   ├── resource_state = ResourceState()
   ├── communication_state = CommunicationState()
   ├── task_queue = TaskQueue()
   └── performance_metrics = PerformanceMetrics()
   ↓ [实线箭头]
4. 初始化任务管理
   ├── task_manager = TaskManager()
   ├── current_processing_task = None
   └── processing_start_time = 0.0
   ↓ [实线箭头]
5. _initialize_from_config() 从配置初始化参数
   ├── 初始化资源状态 (CPU、内存、存储、带宽)
   ├── 初始化能量状态 (电池容量、太阳能功率)
   └── 初始化位置 (高度)
```

### 4.2 时间步执行流程
```
step(time_step, time_delta) [核心执行循环]
    ↓ [实线箭头]
    1. 更新当前时间
    ↓ [实线箭头]
    2. process_tasks(time_step, time_delta) 处理任务
       ├── 检查是否可以处理任务 can_process_tasks()
       ├── 如果没有当前任务，从队列取出一个
       │   └── task.start_processing() 开始处理
       ├── 处理当前任务
       │   ├── 计算可用计算资源
       │   ├── 计算能耗
       │   ├── task.update_processing_progress() 更新进度
       │   ├── 更新资源利用率
       │   ├── update_energy() 更新能量
       │   └── 检查任务是否完成或超时
       │       ├── _complete_task() 完成任务
       │       └── _fail_task() 失败任务
       └── 更新性能指标历史
    ↓ [实线箭头]
    3. _update_resource_utilization() 更新资源利用率
       ├── 更新内存利用率
       ├── 更新存储利用率
       └── 更新带宽利用率
    ↓ [实线箭头]
    4. _record_state_history() 记录状态历史
```

### 4.3 任务处理流程
```
process_tasks(time_step, time_delta) [任务处理]
    ↓ [实线箭头]
    1. 检查是否可以处理任务
    ↓ [实线箭头]
    2. 获取任务
       ├── 如果没有当前任务，从队列取出一个
       └── 开始处理任务
    ↓ [实线箭头]
    3. 计算资源和能耗
       ├── 计算可用CPU和处理周期
       └── 计算能耗
    ↓ [实线箭头]
    4. 更新任务进度和资源状态
       ├── 更新任务进度
       ├── 更新CPU利用率
       └── 更新能量状态
    ↓ [实线箭头]
    5. 检查任务状态
       ├── 如果完成，调用_complete_task()
       └── 如果超时，调用_fail_task()
```

### 4.4 任务卸载流程
```
execute_task_offload(task_id, target_id) [任务卸载]
    ↓ [实线箭头]
    1. 检查任务和目标有效性
       ├── 检查任务是否在队列中
       ├── 检查目标是否可达
       └── 获取任务和链路状态
    ↓ [实线箭头]
    2. 计算传输参数
       ├── 计算传输时间
       └── 计算传输能耗
    ↓ [实线箭头]
    3. 执行传输 _execute_transmission()
       ├── 检查能量是否足够
       ├── 消耗能量
       └── 更新带宽使用
    ↓ [实线箭头]
    4. 更新任务状态
       ├── 从本地队列移除任务
       ├── 记录传输
       └── 更新性能指标
```

### 4.5 状态同步流程
```
sync_with_orbital_state(orbital_satellite) [轨道同步]
    ↓ [实线箭头]
    1. 更新位置信息
       ├── 更新经纬度
       ├── 更新时间戳
       └── 更新速度
    ↓ [实线箭头]
    2. 更新光照状态
       └── 设置is_illuminated

sync_with_communication_state(network_state, time_step) [通信同步]
    ↓ [实线箭头]
    1. 更新邻居列表
    ↓ [实线箭头]
    2. 更新活跃链路
    ↓ [实线箭头]
    3. 更新地面站连接
    ↓ [实线箭头]
    4. 更新网络质量

sync_with_task_state(new_tasks) [任务同步]
    ↓ [实线箭头]
    1. 添加新任务到队列
    ↓ [实线箭头]
    2. 按优先级排序队列
```

## 5. 接口调用分析

### 5.1 详细接口调用关系表

| 调用方 | 被调用方 | 接口函数名称 | 参数类型 | 返回值类型 | 调用频率 | 数据传递内容 |
|--------|----------|-------------|----------|------------|----------|-------------|
| 外部环境 | SatelliteNode | `sync_with_orbital_state()` | OrbitalSatellite | None | 每时间步1次 | 轨道位置数据 |
| 外部环境 | SatelliteNode | `sync_with_communication_state()` | NetworkState, int | None | 每时间步1次 | 网络状态数据 |
| 外部环境 | SatelliteNode | `sync_with_task_state()` | List[Task] | None | 按需调用 | 新任务列表 |
| 外部环境 | SatelliteNode | `step()` | float, float | None | 每时间步1次 | 时间步和时间间隔 |
| 外部环境 | SatelliteNode | `export_observation_data()` | None | Dict[str, Any] | 高频调用 | 完整观测数据 |
| 外部环境 | SatelliteNode | `export_for_scheduling()` | None | Dict[str, Any] | 中频调用 | 调度数据 |
| SatelliteNode | SatelliteNode | `process_tasks()` | float, float | None | 每时间步1次 | 时间步和时间间隔 |
| SatelliteNode | SatelliteNode | `update_energy()` | float, float | None | 高频调用 | 能量变化和时间间隔 |
| SatelliteNode | SatelliteNode | `_sort_task_queue()` | None | None | 中频调用 | 无 |
| SatelliteNode | Task | `start_processing()` | str, float | bool | 每任务1次 | 卫星ID和开始时间 |
| SatelliteNode | Task | `update_processing_progress()` | int, float, float | None | 高频调用 | 处理周期、能耗、时间 |
| SatelliteNode | Task | `calculate_dynamic_priority()` | float | float | 高频调用 | 当前时间 |
| SatelliteNode | Task | `transfer_to_satellite()` | str, str, float, float, bool | None | 中频调用 | 源卫星、目标卫星、时间、能耗、成功标志 |

### 5.2 数据传递格式和内容

#### 主要数据结构：
```python
# 观测数据格式
observation_data: Dict[str, Any] = {
    'satellite_id': 'SAT_001',
    'position': {
        'latitude': 45.0,
        'longitude': -74.0,
        'altitude': 1200.0,
        'velocity_lat': 0.01,
        'velocity_lon': 0.02,
        'timestamp': 1627984800.0
    },
    'energy': {
        'current_battery_j': 3500000.0,
        'battery_capacity_j': 3600000.0,
        'battery_ratio': 0.972,
        'solar_power_w': 500.0,
        'power_consumption_w': 120.0,
        'is_illuminated': True,
        'is_low_energy': False
    },
    'resources': {
        'cpu': {
            'frequency_hz': 10000000000.0,
            'utilization': 0.35,
            'available_hz': **********.0
        },
        'memory': {...},
        'storage': {...},
        'bandwidth': {...}
    },
    'tasks': {
        'queued': 5,
        'processing': 1,
        'completed': 10,
        'failed': 2,
        'total': 18,
        'queue_length': 5,
        'current_processing_task': '1001'
    },
    'communication': {
        'neighbors': ['SAT_002', 'SAT_003'],
        'neighbor_count': 2,
        'active_links': 2,
        'ground_stations': ['GS_001'],
        'network_quality': 0.85
    },
    'performance': {
        'total_tasks_processed': 12,
        'total_tasks_completed': 10,
        'completion_rate': 0.833,
        'total_energy_consumed_j': 25000.0,
        'average_response_time_s': 3.5
    },
    'status': {
        'status': 'active',
        'is_healthy': True,
        'can_accept_tasks': True,
        'can_process_tasks': True
    }
}

# 调度数据格式
scheduling_data: Dict[str, Any] = {
    'satellite_id': 'SAT_001',
    'can_accept_tasks': True,
    'can_process_tasks': True,
    'available_cpu_hz': **********.0,
    'available_memory_mb': 7500.0,
    'queue_length': 5,
    'energy_level': 0.972,
    'neighbor_count': 2,
    'network_quality': 0.85,
    'current_processing_task': '1001',
    'task_priorities': {'1002': 5, '1003': 3, '1004': 4, '1005': 2, '1006': 1}
}
```

### 5.3 调用频率和时机分析

#### 高频调用函数 (每时间步或每任务处理周期调用)：
- `process_tasks()`: 每时间步调用1次
- `update_energy()`: 每次能量变化时调用
- `_update_resource_utilization()`: 每时间步调用1次
- `update_processing_progress()`: 每任务处理周期调用

#### 中频调用函数 (按需调用)：
- `execute_task_offload()`: 任务卸载时调用
- `receive_task()`: 接收任务时调用
- `_sort_task_queue()`: 队列变化时调用
- `_complete_task()`: 任务完成时调用
- `_fail_task()`: 任务失败时调用

#### 低频调用函数 (初始化或特定事件调用)：
- `_load_default_config()`: 初始化时调用1次
- `_initialize_from_config()`: 初始化时调用1次
- `reset()`: 重置状态时调用

### 5.4 关键性能瓶颈分析

1. **任务处理瓶颈**：`process_tasks()` 是主要计算密集型操作
2. **资源管理瓶颈**：资源利用率计算和更新需要频繁操作
3. **任务队列排序瓶颈**：`_sort_task_queue()` 在任务数量大时可能成为瓶颈
4. **状态历史记录瓶颈**：`_record_state_history()` 在长时间运行时可能占用大量内存

## 6. Draw.io具体绘制步骤

### 步骤1：创建画布和设置
1. 打开Draw.io，选择空白画布
2. 设置画布大小：A3横向 (420mm × 297mm)
3. 启用网格对齐，网格间距10px

### 步骤2：绘制主要模块
1. 在顶部绘制main()函数 (红色六边形)
2. 第二层绘制外部依赖 (黄色六边形和平行四边形)
3. 第三层绘制枚举和数据结构 (紫色菱形和浅绿色圆角矩形)
4. 第四层绘制SatelliteNode类 (深蓝色矩形)

### 步骤3：添加功能层
1. 第五层添加7个状态管理函数 (橙色圆角矩形)
2. 第六层添加6个行为执行函数 (深橙色圆角矩形)
3. 第七层添加5个接口函数 (蓝色圆角矩形)
4. 第八层添加6个工具函数 (灰色圆角矩形)
5. 第九层添加5个状态检查函数 (灰色圆角矩形)

### 步骤4：绘制连接线
1. 从main()到SatelliteNode绘制实线箭头
2. 从外部依赖到SatelliteNode绘制虚线箭头
3. 在函数间绘制调用关系箭头
4. 用粗绿线标示主数据流
5. 用点线标示状态更新

### 步骤5：添加分组和标注
1. 创建7个分组容器，设置不同背景色
2. 添加图例说明不同线条和颜色含义
3. 为每个模块添加简要功能说明
4. 标注重要的参数和返回值类型
5. 添加状态流转图和调用频率说明

这个详细的架构图将清晰展示卫星节点模块的完整结构、状态管理机制、行为执行流程和接口调用关系，便于理解卫星节点作为智能体物理载体的设计思路和实现细节。