{"1": {"basic_info": {"task_id": "1", "source_timeslot": 0, "source_location_id": 1, "data_size_mb": 37.959754525911094, "priority": 2, "deadline": 50.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "1824": {"basic_info": {"task_id": "1824", "source_timeslot": 1, "source_location_id": 1, "data_size_mb": 126.40955865788793, "priority": 5, "deadline": 110.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "3650": {"basic_info": {"task_id": "3650", "source_timeslot": 2, "source_location_id": 1, "data_size_mb": 48.989554757212986, "priority": 1, "deadline": 70.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "5427": {"basic_info": {"task_id": "5427", "source_timeslot": 3, "source_location_id": 1, "data_size_mb": 34.04767726461931, "priority": 4, "deadline": 80.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "7205": {"basic_info": {"task_id": "7205", "source_timeslot": 4, "source_location_id": 1, "data_size_mb": 48.88873808545181, "priority": 5, "deadline": 90.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "8992": {"basic_info": {"task_id": "8992", "source_timeslot": 5, "source_location_id": 1, "data_size_mb": 108.90032577906283, "priority": 5, "deadline": 150.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "10774": {"basic_info": {"task_id": "10774", "source_timeslot": 6, "source_location_id": 1, "data_size_mb": 26.785838745349587, "priority": 1, "deadline": 110.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "12470": {"basic_info": {"task_id": "12470", "source_timeslot": 7, "source_location_id": 1, "data_size_mb": 26.271215251240626, "priority": 4, "deadline": 120.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "14271": {"basic_info": {"task_id": "14271", "source_timeslot": 8, "source_location_id": 1, "data_size_mb": 128.86959514119548, "priority": 2, "deadline": 180.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "16073": {"basic_info": {"task_id": "16073", "source_timeslot": 9, "source_location_id": 1, "data_size_mb": 115.89255431996476, "priority": 4, "deadline": 190.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "17917": {"basic_info": {"task_id": "17917", "source_timeslot": 10, "source_location_id": 2, "data_size_mb": 14.497627468874644, "priority": 2, "deadline": 110.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "19760": {"basic_info": {"task_id": "19760", "source_timeslot": 11, "source_location_id": 2, "data_size_mb": 49.13271530741143, "priority": 3, "deadline": 160.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "21452": {"basic_info": {"task_id": "21452", "source_timeslot": 12, "source_location_id": 2, "data_size_mb": 121.1320886632359, "priority": 4, "deadline": 220.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "23193": {"basic_info": {"task_id": "23193", "source_timeslot": 13, "source_location_id": 1, "data_size_mb": 18.88618932356533, "priority": 2, "deadline": 140.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "24960": {"basic_info": {"task_id": "24960", "source_timeslot": 14, "source_location_id": 1, "data_size_mb": 10.081603670917602, "priority": 2, "deadline": 150.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "26745": {"basic_info": {"task_id": "26745", "source_timeslot": 15, "source_location_id": 2, "data_size_mb": 34.10962216555535, "priority": 4, "deadline": 200.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "28502": {"basic_info": {"task_id": "28502", "source_timeslot": 16, "source_location_id": 1, "data_size_mb": 14.011070667289134, "priority": 3, "deadline": 170.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "30253": {"basic_info": {"task_id": "30253", "source_timeslot": 17, "source_location_id": 1, "data_size_mb": 18.05522890736355, "priority": 5, "deadline": 180.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "32084": {"basic_info": {"task_id": "32084", "source_timeslot": 18, "source_location_id": 1, "data_size_mb": 39.527069874201274, "priority": 2, "deadline": 230.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "33881": {"basic_info": {"task_id": "33881", "source_timeslot": 19, "source_location_id": 2, "data_size_mb": 38.72943552927736, "priority": 3, "deadline": 240.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "35624": {"basic_info": {"task_id": "35624", "source_timeslot": 20, "source_location_id": 1, "data_size_mb": 86.04949852036741, "priority": 3, "deadline": 300.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "37295": {"basic_info": {"task_id": "37295", "source_timeslot": 21, "source_location_id": 1, "data_size_mb": 110.9785728021003, "priority": 2, "deadline": 310.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "39065": {"basic_info": {"task_id": "39065", "source_timeslot": 22, "source_location_id": 1, "data_size_mb": 76.91332720286289, "priority": 3, "deadline": 320.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "40832": {"basic_info": {"task_id": "40832", "source_timeslot": 23, "source_location_id": 2, "data_size_mb": 33.6672748168591, "priority": 5, "deadline": 280.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "42606": {"basic_info": {"task_id": "42606", "source_timeslot": 24, "source_location_id": 1, "data_size_mb": 16.469295098146127, "priority": 2, "deadline": 250.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "44426": {"basic_info": {"task_id": "44426", "source_timeslot": 25, "source_location_id": 1, "data_size_mb": 25.77409816733806, "priority": 2, "deadline": 300.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "46270": {"basic_info": {"task_id": "46270", "source_timeslot": 26, "source_location_id": 1, "data_size_mb": 34.101582292664276, "priority": 5, "deadline": 310.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "48027": {"basic_info": {"task_id": "48027", "source_timeslot": 27, "source_location_id": 1, "data_size_mb": 15.336182646477313, "priority": 5, "deadline": 280.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "49832": {"basic_info": {"task_id": "49832", "source_timeslot": 28, "source_location_id": 1, "data_size_mb": 48.26484836566628, "priority": 4, "deadline": 330.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "51618": {"basic_info": {"task_id": "51618", "source_timeslot": 29, "source_location_id": 1, "data_size_mb": 35.16766362585711, "priority": 1, "deadline": 340.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "53394": {"basic_info": {"task_id": "53394", "source_timeslot": 30, "source_location_id": 1, "data_size_mb": 61.976003614272344, "priority": 5, "deadline": 400.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "55230": {"basic_info": {"task_id": "55230", "source_timeslot": 31, "source_location_id": 1, "data_size_mb": 60.52072829855396, "priority": 1, "deadline": 410.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "56958": {"basic_info": {"task_id": "56958", "source_timeslot": 32, "source_location_id": 3, "data_size_mb": 49.310630139907744, "priority": 4, "deadline": 370.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "58796": {"basic_info": {"task_id": "58796", "source_timeslot": 33, "source_location_id": 1, "data_size_mb": 10.60903742924383, "priority": 3, "deadline": 340.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "60636": {"basic_info": {"task_id": "60636", "source_timeslot": 34, "source_location_id": 2, "data_size_mb": 44.79173610937387, "priority": 3, "deadline": 390.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "62447": {"basic_info": {"task_id": "62447", "source_timeslot": 35, "source_location_id": 3, "data_size_mb": 23.715917962788577, "priority": 1, "deadline": 400.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "64254": {"basic_info": {"task_id": "64254", "source_timeslot": 36, "source_location_id": 1, "data_size_mb": 79.08413354353415, "priority": 3, "deadline": 460.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "65964": {"basic_info": {"task_id": "65964", "source_timeslot": 37, "source_location_id": 1, "data_size_mb": 15.831654178694832, "priority": 1, "deadline": 380.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "67760": {"basic_info": {"task_id": "67760", "source_timeslot": 38, "source_location_id": 1, "data_size_mb": 13.49765194996534, "priority": 2, "deadline": 390.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "69521": {"basic_info": {"task_id": "69521", "source_timeslot": 39, "source_location_id": 5, "data_size_mb": 33.203567989581664, "priority": 1, "deadline": 440.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "71376": {"basic_info": {"task_id": "71376", "source_timeslot": 40, "source_location_id": 1, "data_size_mb": 29.574468412295417, "priority": 5, "deadline": 450.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "73177": {"basic_info": {"task_id": "73177", "source_timeslot": 41, "source_location_id": 3, "data_size_mb": 80.46668271479078, "priority": 2, "deadline": 510.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "75012": {"basic_info": {"task_id": "75012", "source_timeslot": 42, "source_location_id": 2, "data_size_mb": 38.30815582128255, "priority": 3, "deadline": 470.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "76819": {"basic_info": {"task_id": "76819", "source_timeslot": 43, "source_location_id": 1, "data_size_mb": 30.547431440747783, "priority": 3, "deadline": 480.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "78543": {"basic_info": {"task_id": "78543", "source_timeslot": 44, "source_location_id": 1, "data_size_mb": 12.293761921659119, "priority": 3, "deadline": 450.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "80270": {"basic_info": {"task_id": "80270", "source_timeslot": 45, "source_location_id": 2, "data_size_mb": 67.01452701864827, "priority": 5, "deadline": 550.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "82095": {"basic_info": {"task_id": "82095", "source_timeslot": 46, "source_location_id": 1, "data_size_mb": 16.696967876990538, "priority": 1, "deadline": 470.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "83856": {"basic_info": {"task_id": "83856", "source_timeslot": 47, "source_location_id": 3, "data_size_mb": 17.804237119167226, "priority": 2, "deadline": 480.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "85616": {"basic_info": {"task_id": "85616", "source_timeslot": 48, "source_location_id": 2, "data_size_mb": 54.50218425239961, "priority": 3, "deadline": 580.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}, "87445": {"basic_info": {"task_id": "87445", "source_timeslot": 49, "source_location_id": 1, "data_size_mb": 15.512173795224253, "priority": 4, "deadline": 500.0, "completion_status": "active", "end_to_end_latency_s": 0.0, "total_energy_cost_j": 0.0}, "stages": []}}