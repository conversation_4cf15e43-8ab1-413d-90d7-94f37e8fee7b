#!/usr/bin/env python3
"""
SPACE-OAAL 适配器模块 - 精简版

该模块提供轻量级适配器，实现模块间的数据格式转换和接口统一：
- SatelliteAdapter: orbital_updater.Satellite与satellite.SatelliteNode的转换
- TaskAdapter: 任务数据格式转换和分配逻辑

精简版本，专注于适配功能，不承担仿真管理职责。
"""

# 修复：移除未使用的time导入，添加logging支持
from typing import Dict, List, Optional, Any
import numpy as np
import logging

# 导入核心模块
try:
    from .orbital_updater import Satellite as OrbitalSatellite, OrbitalUpdater
    from .communication import CommunicationManager
    from .satellite import SatelliteNode, SatelliteStatus
    from .task import Task, TaskState, TaskLoader
except ImportError:
    from orbital_updater import Satellite as OrbitalSatellite, OrbitalUpdater
    from communication import CommunicationManager
    from satellite import SatelliteNode, SatelliteStatus
    from task import Task, TaskState, TaskLoader


class SatelliteAdapter:
    """
    卫星适配器：处理orbital_updater与satellite模块的数据转换
    修复：使用依赖注入模式，接受必需的config参数
    """
    
    def __init__(self, orbital_updater: OrbitalUpdater, comm_manager: CommunicationManager, config: Dict):
        """
        初始化卫星适配器 - 修复：使用依赖注入
        
        Args:
            orbital_updater: 轨道更新器实例
            comm_manager: 通信管理器实例
            config: 统一配置字典
        """
        # 验证参数
        if orbital_updater is None:
            raise ValueError("orbital_updater参数不能为None")
        if comm_manager is None:
            raise ValueError("comm_manager参数不能为None")
        if config is None:
            raise ValueError("config参数不能为None")
        
        self.satellite_nodes: Dict[str, SatelliteNode] = {}
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
        self.config = config
        
        # 可见性矩阵缓存优化
        self._visibility_cache = {}
        self._neighbors_cache = {}
        self._cached_time_step = -1
        
    def create_satellite_nodes(self, time_step: int = 0) -> Dict[str, SatelliteNode]:
        """创建所有卫星节点 - 修复：传递必需的config参数"""
        try:
            orbital_satellites = self.orbital_updater.get_satellites_at_time(time_step)
            
            for satellite_id, orbital_satellite in orbital_satellites.items():
                # 修复：创建SatelliteNode时传递必需的config参数
                satellite_node = SatelliteNode(satellite_id=satellite_id, config=self.config)
                
                # 设置外部模块引用
                satellite_node.set_external_modules(
                    orbital_updater=self.orbital_updater,
                    comm_manager=self.comm_manager
                )
                
                # 同步初始状态
                satellite_node.sync_with_orbital_state(orbital_satellite)
                
                self.satellite_nodes[satellite_id] = satellite_node
            
            return self.satellite_nodes
            
        except Exception as e:
            import logging
            logging.error(f"Failed to create satellite nodes: {e}")
            raise
    
    def sync_orbital_states(self, time_step: int):
        """同步轨道状态 - 修复：添加异常处理"""
        try:
            orbital_satellites = self.orbital_updater.get_satellites_at_time(time_step)
            
            for satellite_id, orbital_satellite in orbital_satellites.items():
                if satellite_id in self.satellite_nodes:
                    self.satellite_nodes[satellite_id].sync_with_orbital_state(orbital_satellite)
                    
        except Exception as e:
            logging.error(f"Failed to sync orbital states at time_step {time_step}: {e}")
            raise
    
    def sync_communication_states(self, time_step: int):
        """同步通信状态 - 增强缓存优化，添加异常处理"""
        try:
            # 检查缓存，避免重复计算
            if time_step == self._cached_time_step and self._visibility_cache and self._neighbors_cache:
                self._apply_cached_communication_states()
                return
            
            # 缓存未命中，重新计算
            satellites = self.orbital_updater.get_satellites_at_time(time_step)
            ground_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(satellites, time_step)
            satellite_list = list(satellites.keys())
            ground_station_list = list(self.orbital_updater.ground_stations.keys())
        
            # 预构建邻居关系字典（使用高效的星间链路计算）
            neighbors_dict = {}
            for satellite_id in self.satellite_nodes.keys():
                try:
                    neighbors_dict[satellite_id] = self.comm_manager.get_neighbors(satellite_id, time_step)
                except Exception as e:
                    logging.warning(f"Failed to get neighbors for {satellite_id}: {e}")
                    neighbors_dict[satellite_id] = []
        
            # 缓存计算结果
            self._neighbors_cache = neighbors_dict.copy()
            self._cached_time_step = time_step
            
            # 计算并缓存可见地面站信息
            visibility_states = {}
            for satellite_id, satellite_node in self.satellite_nodes.items():
                # 获取预计算的邻居列表
                neighbors = neighbors_dict.get(satellite_id, [])
                
                # 获取可见地面站
                visible_ground_stations = []
                
                if satellite_id in satellite_list:
                    sat_idx = satellite_list.index(satellite_id)
                    
                    for gs_idx, gs_id in enumerate(ground_station_list):
                        if ground_matrix[sat_idx, gs_idx]:
                            visible_ground_stations.append(gs_id)
                
                # 构建网络状态
                network_state = {
                    'neighbors': neighbors,
                    'ground_stations': visible_ground_stations
                }
                
                # 缓存网络状态
                visibility_states[satellite_id] = network_state
                
                # 应用到卫星节点
                satellite_node.sync_with_communication_state(network_state, time_step)
            
            # 更新缓存
            self._visibility_cache = visibility_states
            
        except Exception as e:
            logging.error(f"Failed to sync communication states at time_step {time_step}: {e}")
            raise
    
    def _apply_cached_communication_states(self):
        """应用缓存的通信状态"""
        for satellite_id, satellite_node in self.satellite_nodes.items():
            if satellite_id in self._visibility_cache:
                network_state = self._visibility_cache[satellite_id]
                satellite_node.sync_with_communication_state(network_state, self._cached_time_step)
    
    def get_satellite_nodes(self) -> Dict[str, SatelliteNode]:
        """获取所有卫星节点"""
        return self.satellite_nodes


class TaskAdapter:
    """
    任务适配器：处理任务的加载和分配逻辑
    修复：使用依赖注入模式，接受必需的config参数
    """
    
    def __init__(self, config: Dict):
        """
        初始化任务适配器 - 修复：使用依赖注入
        
        Args:
            config: 统一配置字典
        """
        if config is None:
            raise ValueError("config参数不能为None")
        
        self.config = config
        self.task_loader = TaskLoader()
        # 可见性查找缓存
        self._visibility_lookup = {}
        self._lookup_cache_time = -1
    
    def load_tasks_for_timeslot(self, timeslot: int) -> List[Task]:
        """加载指定时隙的任务 - 修复：传递config参数"""
        return self.task_loader.get_tasks_for_timeslot(timeslot, self.config)
    
    def assign_tasks_to_satellites(self, tasks: List[Task], 
                                 satellites: Dict[str, SatelliteNode], 
                                 current_time: float) -> Dict[str, int]:
        """
        将地面用户任务分配给可见卫星（优化版本）
        修复：接受current_time参数，保证与Task模块的时间一致性
        
        Args:
            tasks: 任务列表
            satellites: 卫星节点字典
            current_time: 当前仿真时间
        
        Returns:
            Dict[str, int]: 每个卫星分配到的任务数量
        """
        assignment_count = {}
        successful_assignments = 0
        failed_assignments = 0
        failure_reasons = {
            'no_visible_satellites': 0,
            'no_healthy_satellites': 0,
            'queue_full': 0,
            'other': 0
        }
        
        # 修复：使用logging模块替代print语句
        if len(tasks) > 0:
            logging.info(f"TaskAdapter assigning {len(tasks)} tasks to {len(satellites)} satellites")
        
        # 检查缓存是否有效，避免重复构建查找表
        if current_time != self._lookup_cache_time:
            # 缓存失效，重新构建查找表
            self._visibility_lookup = self._build_visibility_lookup(satellites)
            self._lookup_cache_time = current_time
        
        visibility_lookup = self._visibility_lookup
        
        # 修复：移除healthy_satellites预筛选，不再需要回退逻辑
        
        for task in tasks:
            source_location_id = getattr(task, 'source_location_id', None)
            
            if source_location_id is not None:
                # 优化：使用预构建的查找表
                visible_satellites = visibility_lookup.get(str(source_location_id), [])
                
                if visible_satellites:
                    # 优化：负载均衡选择卫星（选择队列最短的）
                    target_satellite = self._select_best_satellite(visible_satellites)
                    success = target_satellite.receive_task(task)
                    
                    if success:
                        satellite_id = target_satellite.satellite_id
                        assignment_count[satellite_id] = assignment_count.get(satellite_id, 0) + 1
                        successful_assignments += 1
                    else:
                        failure_reasons['queue_full'] += 1
                        # 修复：传递current_time参数
                        task.update_state(TaskState.FAILED, None, current_time)
                        failed_assignments += 1
                else:
                    # 修复：移除不符合物理现实的回退逻辑
                    # 没有可见卫星，任务分配失败
                    failure_reasons['no_visible_satellites'] += 1
                    task.update_state(TaskState.FAILED, None, current_time)
                    failed_assignments += 1
            else:
                failure_reasons['other'] += 1
                # 修复：传递current_time参数
                task.update_state(TaskState.FAILED, None, current_time)
                failed_assignments += 1
        
        # 修复：使用logging模块替代print语句
        if len(tasks) > 0:
            success_rate = (successful_assignments / len(tasks)) * 100 if len(tasks) > 0 else 0
            logging.info(f"Task assignment - {successful_assignments}/{len(tasks)} tasks assigned ({success_rate:.1f}% success)")
            
            if failed_assignments > 0:
                top_reasons = sorted(failure_reasons.items(), key=lambda x: x[1], reverse=True)[:2]
                reason_str = ", ".join([f"{reason}: {count}" for reason, count in top_reasons if count > 0])
                logging.warning(f"Main failure reasons: {reason_str}")
            
            if assignment_count:
                # 显示分配最多的前3个卫星
                sorted_assignments = sorted(assignment_count.items(), key=lambda x: x[1], reverse=True)
                top_3 = dict(sorted_assignments[:3])
                total_satellites = len(assignment_count)
                logging.debug(f"Top assignments to {total_satellites} satellites: {top_3}")
        
        return assignment_count
    
    def _build_visibility_lookup(self, satellites: Dict[str, SatelliteNode]) -> Dict[str, List[SatelliteNode]]:
        """
        构建可见性查找表，优化任务分配复杂度
        修复：直接使用已缓存的可见性数据，避免重复计算
        """
        visibility_lookup = {}
        
        # 优化：直接使用已缓存的可见性数据  
        if hasattr(self, '_visibility_cache') and self._visibility_cache:
            for satellite_id, satellite in satellites.items():
                if (satellite_id in self._visibility_cache and
                    satellite.is_healthy() and satellite.can_accept_tasks()):
                    
                    network_state = self._visibility_cache[satellite_id]
                    visible_ground_stations = network_state.get('ground_stations', [])
                    
                    for location_id in visible_ground_stations:
                        if location_id not in visibility_lookup:
                            visibility_lookup[location_id] = []
                        visibility_lookup[location_id].append(satellite)
        else:
            # 回退到原始方法（如果缓存不可用）
            for satellite in satellites.values():
                if (hasattr(satellite, 'communication_state') and 
                    hasattr(satellite.communication_state, 'visible_ground_stations') and
                    satellite.is_healthy() and satellite.can_accept_tasks()):
                    
                    for location_id in satellite.communication_state.visible_ground_stations:
                        if location_id not in visibility_lookup:
                            visibility_lookup[location_id] = []
                        visibility_lookup[location_id].append(satellite)
        
        return visibility_lookup
    
    def _select_best_satellite(self, candidates: List[SatelliteNode]) -> SatelliteNode:
        """选择最佳卫星（负载均衡）"""
        if not candidates:
            return None
        
        # 选择队列长度最短且能量充足的卫星
        best_satellite = min(candidates, key=lambda sat: (
            len(sat.task_queue),  # 主要：队列长度
            -sat.energy_state.battery_ratio  # 次要：能量水平（负号表示优先选择能量高的）
        ))
        
        return best_satellite
    


def main():
    """简单测试 - 修复：适配新的依赖注入接口"""
    print("=== SPACE-OAAL 适配器模块测试 ===")
    
    # 创建测试配置
    test_config = {
        'system': {
            'leo_altitude_m': 1200000,
            'total_timeslots': 1000,
            'timeslot_duration_s': 10
        },
        'computation': {
            'f_leo_hz': 10e9,
            'leo_battery_capacity_j': 3600000,
            'leo_solar_power_w': 500,
            'zeta_leo': 1.0e-28
        },
        'communication': {
            'max_retries': 3
        }
    }
    
    try:
        # 创建依赖对象
        orbital_updater = OrbitalUpdater()
        comm_manager = CommunicationManager()
        comm_manager.set_orbital_updater(orbital_updater)
        
        # 测试卫星适配器 - 使用依赖注入
        sat_adapter = SatelliteAdapter(orbital_updater, comm_manager, test_config)
        satellites = sat_adapter.create_satellite_nodes(0)
        print(f"创建了 {len(satellites)} 颗卫星")
        
        # 测试任务适配器 - 传入config参数
        task_adapter = TaskAdapter(test_config)
        tasks = task_adapter.load_tasks_for_timeslot(0)
        print(f"加载了 {len(tasks)} 个任务")
        
        # 测试任务分配 - 传入current_time参数
        if satellites and tasks:
            current_time = 0.0
            assignment = task_adapter.assign_tasks_to_satellites(tasks, satellites, current_time)
            print(f"任务分配结果: {assignment}")
        
        print("=== 测试完成 ===")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()