# 轨道更新模块程序架构图

## 1. 程序架构概述

轨道更新模块是一个卫星轨道计算和管理系统，采用面向对象的设计模式。系统以 `OrbitalUpdater` 为核心控制器，管理卫星和地面站的位置信息、可见性计算和轨道更新。架构特点包括：

- **数据驱动设计**：通过CSV文件和YAML配置文件驱动系统运行
- **时序处理机制**：支持按时间步进行卫星状态更新和轨道计算
- **多层次可见性计算**：包含卫星间可见性和卫星-地面站可见性
- **模块化组件**：独立的卫星类、地面站类和轨道更新器类

## 2. Draw.io架构图绘制指导

### 2.1 图形元素和颜色方案

#### 主要模块形状建议：
- **主控制器类**：深蓝色矩形 (#1f4e79, 宽度200px, 高度80px)
- **数据实体类**：浅绿色圆角矩形 (#70ad47, 宽度150px, 高度60px, 圆角半径10px)
- **核心功能函数**：橙色圆角矩形 (#d9730d, 宽度180px, 高度50px, 圆角半径8px)
- **工具函数**：灰色圆角矩形 (#7f7f7f, 宽度140px, 高度40px, 圆角半径6px)
- **数据文件**：黄色平行四边形 (#ffc000, 宽度120px, 高度50px)
- **配置参数**：紫色椭圆 (#7030a0, 宽度100px, 高度40px)
- **主函数入口**：红色六边形 (#c5504b, 宽度100px, 高度60px)

#### 连接线类型：
- **实线箭头**：直接函数调用 (线宽2px, 黑色 #000000)
- **虚线箭头**：数据传递 (虚线, 线宽1.5px, 深灰色 #404040)
- **双向箭头**：相互调用关系 (线宽2px, 蓝色 #0066cc)
- **粗线**：主要数据流 (线宽3px, 绿色 #00aa00)

### 2.2 布局建议（从上到下）

```
第1层：程序入口层 (Y坐标: 50px)
├── main() 函数 [红色六边形]

第2层：数据源层 (Y坐标: 150px)
├── satellite_processed_data.csv [黄色平行四边形]
├── config.yaml [黄色平行四边形]
└── updated_global_ground_stations.csv [黄色平行四边形]

第3层：主控制器层 (Y坐标: 280px)
├── OrbitalUpdater类 [深蓝色矩形]

第4层：数据实体层 (Y坐标: 400px)
├── Satellite类 [浅绿色圆角矩形]
└── GroundStation类 [浅绿色圆角矩形]

第5层：核心功能层 (Y坐标: 520px)
├── get_satellites_at_time() [橙色圆角矩形]
├── update_satellite_positions() [橙色圆角矩形]
├── build_inter_satellite_visibility_matrix() [橙色圆角矩形]
├── build_satellite_ground_visibility_matrix() [橙色圆角矩形]
└── get_all_ground_coverage() [橙色圆角矩形]

第6层：计算服务层 (Y坐标: 640px)
├── calculate_satellite_visibility() [橙色圆角矩形]
├── calculate_satellite_ground_visibility() [橙色圆角矩形]
├── calculate_velocity() [橙色圆角矩形]
└── get_ground_coverage() [橙色圆角矩形]

第7层：工具函数层 (Y坐标: 760px)
├── _calculate_distance() [灰色圆角矩形]
├── _load_config() [灰色圆角矩形]
├── _load_satellite_data() [灰色圆角矩形]
└── _create_ground_stations() [灰色圆角矩形]

第8层：配置参数层 (Y坐标: 860px)
├── earth_radius [紫色椭圆]
├── satellite_altitude [紫色椭圆]
├── visibility_threshold [紫色椭圆]
└── timeslot_duration [紫色椭圆]
```

### 2.3 分组和层次组织

#### 主要分组容器：
1. **数据输入组** (虚线边框, 浅灰色背景 #f5f5f5)
   - 包含所有CSV和YAML文件
   
2. **核心控制组** (实线边框, 浅蓝色背景 #e6f2ff)
   - 包含OrbitalUpdater类和主要控制逻辑
   
3. **数据模型组** (实线边框, 浅绿色背景 #e6ffe6)
   - 包含Satellite和GroundStation类
   
4. **计算服务组** (实线边框, 浅橙色背景 #fff2e6)
   - 包含所有计算和处理函数
   
5. **系统配置组** (虚线边框, 浅紫色背景 #f0e6ff)
   - 包含所有配置参数

## 3. 程序模块详细描述

### 3.1 主控制器模块 - OrbitalUpdater类
- **程序模块名称**：轨道更新器
- **文件位置**：`src/env/orbital_updater.py` (第29-400行)
- **主要功能和职责**：
  - 卫星轨道数据管理和更新
  - 卫星间和卫星-地面站可见性计算
  - 地面覆盖范围分析
  - 系统配置和参数管理

- **关键类和函数列表**：
  ```python
  class OrbitalUpdater:
      __init__(data_file, config_file)                    # 初始化
      get_satellites_at_time(time_step) -> Dict           # 获取时间步卫星状态
      update_satellite_positions(satellites, time_step)   # 更新卫星位置
      calculate_velocity(satellite_id, time_step)         # 计算卫星速度
      build_inter_satellite_visibility_matrix()          # 卫星间可见性矩阵
      build_satellite_ground_visibility_matrix()         # 卫星-地面站可见性
      get_all_ground_coverage(satellites)                # 地面覆盖分析
  ```

- **对外提供的接口**：
  - `get_satellites_at_time(int) -> Dict[str, Satellite]`
  - `build_inter_satellite_visibility_matrix(Dict) -> np.ndarray`
  - `build_satellite_ground_visibility_matrix(Dict) -> np.ndarray`
  - `get_all_ground_coverage(Dict) -> Dict[str, Dict]`

- **依赖的其他模块**：
  - pandas (数据处理)
  - numpy (矩阵计算)
  - yaml (配置文件解析)
  - datetime (时间处理)
  - math (数学计算)

### 3.2 卫星实体模块 - Satellite类
- **程序模块名称**：卫星数据实体
- **文件位置**：`src/env/orbital_updater.py` (第9-22行)
- **主要功能和职责**：
  - 存储单个卫星的状态信息
  - 提供卫星属性访问接口

- **关键类和函数列表**：
  ```python
  class Satellite:
      __init__(satellite_id, longitude, latitude, illuminated, timestamp, total_timeslots)
      __repr__()  # 字符串表示
  ```

- **对外提供的接口**：
  - 属性访问：satellite_id, longitude, latitude, illuminated, timestamp, velocity
  - 字符串表示方法

### 3.3 地面站实体模块 - GroundStation类
- **程序模块名称**：地面站数据实体
- **文件位置**：`src/env/orbital_updater.py` (第25-37行)
- **主要功能和职责**：
  - 存储地面站的基本信息
  - 提供地面站属性访问接口

- **关键类和函数列表**：
  ```python
  class GroundStation:
      __init__(station_id, longitude, latitude, name, type)
      __repr__()  # 字符串表示
  ```

- **对外提供的接口**：
  - 属性访问：station_id, longitude, latitude, name, type
  - 字符串表示方法

## 4. 程序执行流程说明

### 4.1 程序启动和初始化流程
```
1. main() 函数启动 [程序入口点]
   ↓ [实线箭头]
2. 创建 OrbitalUpdater() 实例
   ↓ [实线箭头]
3. __init__() 初始化过程：
   ├── _load_config() 加载YAML配置 [虚线箭头指向config.yaml]
   ├── _load_satellite_data() 加载卫星数据 [虚线箭头指向CSV文件]
   ├── _create_ground_stations() 创建地面站 [虚线箭头指向地面站CSV]
   └── 初始化系统参数 [虚线箭头指向配置参数]
   ↓ [实线箭头]
4. 获取总时隙数 get_total_timeslots()
   ↓ [实线箭头]
5. 进入主循环处理
```

### 4.2 主要业务逻辑执行顺序
```
for time_step in range(total_timeslots):  [循环控制]
    ↓ [粗线绿色箭头 - 主数据流]
    1. get_satellites_at_time(time_step)
       ├── 查询DataFrame数据 [虚线箭头]
       └── 创建Satellite对象 [实线箭头]
    ↓ [实线箭头]
    2. update_satellite_positions(satellites, time_step)
       └── calculate_velocity() [实线箭头]
    ↓ [实线箭头]
    3. build_inter_satellite_visibility_matrix(satellites)
       └── calculate_satellite_visibility() [实线箭头]
           └── _calculate_distance() [实线箭头]
    ↓ [实线箭头]
    4. build_satellite_ground_visibility_matrix(satellites)
       └── calculate_satellite_ground_visibility() [实线箭头]
           └── _calculate_distance() [实线箭头]
    ↓ [实线箭头]
    5. get_all_ground_coverage(satellites)
       └── get_ground_coverage() [实线箭头]
    ↓ [实线箭头]
    6. 输出统计信息 [终端输出]
```

### 4.3 模块间调用时序
```
时序图：
main() 
  │
  ├─→ OrbitalUpdater.__init__()
  │     ├─→ _load_config()
  │     ├─→ _load_satellite_data()  
  │     └─→ _create_ground_stations()
  │
  └─→ for循环 (1000次时间步)
        ├─→ get_satellites_at_time()
        ├─→ update_satellite_positions()
        │     └─→ calculate_velocity()
        ├─→ build_inter_satellite_visibility_matrix()
        │     └─→ calculate_satellite_visibility()
        │           └─→ _calculate_distance()
        ├─→ build_satellite_ground_visibility_matrix()
        │     └─→ calculate_satellite_ground_visibility()
        │           └─→ _calculate_distance()
        └─→ get_all_ground_coverage()
              └─→ get_ground_coverage()
```

### 4.4 数据处理和传递流程
```
数据流向图：
CSV文件 ──虚线箭头──→ pandas.DataFrame ──实线箭头──→ Satellite对象
YAML文件 ──虚线箭头──→ dict配置 ──实线箭头──→ 系统参数
Satellite对象 ──粗线箭头──→ 可见性矩阵 ──实线箭头──→ 统计结果
GroundStation对象 ──双向箭头──→ 可见性计算 ──实线箭头──→ 覆盖信息
```

## 5. 接口调用分析

### 5.1 详细接口调用关系表

| 调用方 | 被调用方 | 接口函数名称 | 参数类型 | 返回值类型 | 调用频率 | 数据传递内容 |
|--------|----------|-------------|----------|------------|----------|-------------|
| main() | OrbitalUpdater | `__init__()` | str, str | None | 1次 | 文件路径 |
| main() | OrbitalUpdater | `get_satellites_at_time()` | int | Dict[str, Satellite] | 1000次/循环 | 时间步→卫星状态字典 |
| main() | OrbitalUpdater | `build_inter_satellite_visibility_matrix()` | Dict[str, Satellite] | np.ndarray | 1000次/循环 | 卫星字典→可见性矩阵 |
| main() | OrbitalUpdater | `build_satellite_ground_visibility_matrix()` | Dict[str, Satellite] | np.ndarray | 1000次/循环 | 卫星字典→地面可见性矩阵 |
| main() | OrbitalUpdater | `get_all_ground_coverage()` | Dict[str, Satellite] | Dict[str, Dict] | 1000次/循环 | 卫星字典→覆盖信息 |
| OrbitalUpdater | OrbitalUpdater | `calculate_satellite_visibility()` | Satellite, Satellite | bool | 高频(N²次) | 两卫星对象→可见性布尔值 |
| OrbitalUpdater | OrbitalUpdater | `calculate_satellite_ground_visibility()` | Satellite, GroundStation | bool | 高频(N×M次) | 卫星地面站→可见性布尔值 |
| OrbitalUpdater | OrbitalUpdater | `_calculate_distance()` | float×4, float×2 | float | 极高频 | 坐标和高度→距离值 |
| OrbitalUpdater | OrbitalUpdater | `calculate_velocity()` | str, int | Tuple[float, float] | 1000次/循环 | 卫星ID时间步→速度向量 |

### 5.2 数据传递格式和内容

#### 主要数据结构：
```python
# 卫星状态字典
satellites: Dict[str, Satellite] = {
    "satellite_1": Satellite(id="satellite_1", lon=120.5, lat=30.2, ...),
    "satellite_2": Satellite(id="satellite_2", lon=121.0, lat=31.0, ...),
    ...
}

# 可见性矩阵
visibility_matrix: np.ndarray = [
    [False, True,  False, ...],  # satellite_1 与其他卫星的可见性
    [True,  False, True,  ...],  # satellite_2 与其他卫星的可见性
    ...
]

# 覆盖信息字典
coverage_info: Dict[str, Dict[str, float]] = {
    "satellite_1": {
        "center_longitude": 120.5,
        "center_latitude": 30.2,
        "coverage_radius_km": 2500.0,
        "coverage_area_km2": 19634954.0,
        "illuminated": True
    },
    ...
}

# 配置参数字典
config: Dict = {
    "system": {
        "earth_radius_m": 6371000,
        "leo_altitude_m": 550000,
        "visibility_threshold_m": 5000000,
        "timeslot_duration_s": 1,
        "total_timeslots": 1000,
        ...
    }
}
```

### 5.3 调用频率和时机分析

#### 高频调用函数 (每时间步调用)：
- `get_satellites_at_time()`: 1000次 (每时间步1次)
- `build_inter_satellite_visibility_matrix()`: 1000次
- `build_satellite_ground_visibility_matrix()`: 1000次
- `get_all_ground_coverage()`: 1000次

#### 极高频调用函数 (嵌套循环调用)：
- `calculate_satellite_visibility()`: ~1000 × N² 次 (N为卫星数量)
- `calculate_satellite_ground_visibility()`: ~1000 × N × M 次 (M为地面站数量)
- `_calculate_distance()`: 极高频，被上述函数频繁调用

#### 低频调用函数 (初始化时调用)：
- `_load_config()`: 1次
- `_load_satellite_data()`: 1次  
- `_create_ground_stations()`: 1次

### 5.4 关键性能瓶颈分析

1. **距离计算瓶颈**：`_calculate_distance()` 函数被极高频调用，是主要性能瓶颈
2. **矩阵构建瓶颈**：可见性矩阵构建涉及O(N²)复杂度的嵌套循环
3. **数据查询瓶颈**：每时间步都需要从DataFrame中查询卫星数据

## 6. Draw.io具体绘制步骤

### 步骤1：创建画布和设置
1. 打开Draw.io，选择空白画布
2. 设置画布大小：A3横向 (420mm × 297mm)
3. 启用网格对齐，网格间距10px

### 步骤2：绘制主要模块
1. 在顶部绘制main()函数 (红色六边形)
2. 第二层绘制三个数据文件 (黄色平行四边形)
3. 第三层绘制OrbitalUpdater类 (深蓝色矩形)
4. 第四层绘制Satellite和GroundStation类 (浅绿色圆角矩形)

### 步骤3：添加功能层
1. 第五层添加5个核心功能函数 (橙色圆角矩形)
2. 第六层添加4个计算服务函数 (橙色圆角矩形)
3. 第七层添加4个工具函数 (灰色圆角矩形)
4. 第八层添加配置参数 (紫色椭圆)

### 步骤4：绘制连接线
1. 从main()到OrbitalUpdater绘制实线箭头
2. 从数据文件到相应加载函数绘制虚线箭头
3. 在函数间绘制调用关系箭头
4. 用粗绿线标示主数据流

### 步骤5：添加分组和标注
1. 创建5个分组容器，设置背景色
2. 添加图例说明不同线条和颜色含义
3. 为每个模块添加简要功能说明
4. 标注重要的参数和返回值类型

这个详细的架构图将清晰展示轨道更新模块的完整结构、执行流程和接口关系，便于理解和维护代码。