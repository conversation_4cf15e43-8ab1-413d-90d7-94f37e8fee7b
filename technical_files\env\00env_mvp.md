# SPACE-OAAL 基础环境 MVP 上下文工程说明文档

## 项目概述

SPACE-OAAL 基础环境是一个LEO卫星星座边缘计算仿真平台，支持多智能体强化学习算法，提供完整的卫星边缘计算仿真环境。当前版本已实现完整的仿真功能，包括轨道动力学、通信链路、任务管理、卫星实体和环境控制。

---

## 1. 项目结构与模块划分

### 1.1 核心文件结构

```
src/env/
├── __init__.py                          # 包初始化文件
├── config.yaml                          # 全局配置参数
├── satellite_env.py                     # 主环境类，支持多智能体RL
├── env_interface.py                     # RL环境接口和动作空间管理
├── adapters.py                          # 模块间适配器，数据转换和接口统一
├── satellite.py                         # 卫星实体类，完整的状态管理和任务处理
├── communication.py                     # 通信链路管理，基于物理模型的通信计算
├── orbital_updater.py                   # 轨道更新与可见性计算
├── task.py                              # 任务实体定义，完整生命周期管理
├── task_generator.py                    # 任务生成器，基于地理时空分布
├── policy_domain.py                     # 策略域配置（简单配置文件）
├── satellite_processed_data1.csv        # 36颗LEO卫星轨道预计算数据
├── updated_global_ground_stations.csv   # 420个地面用户终端坐标
├── cloud_station.csv                    # 云中心数据
├── regions.json                         # 区域配置数据
└── task_data/
    └── task_generation_results.json     # 任务生成结果数据
```

### 1.2 模块功能详述

#### **数据文件**
- **`satellite_processed_data1.csv`**: 36颗LEO卫星轨道数据
  - 格式: `satellite_id,timestamp,latitude,longitude,illuminated`
  - 作用: 提供1000个时隙的卫星位置和光照状态数据
  
- **`updated_global_ground_stations.csv`**: 420个地面用户终端坐标
  - 格式: `ID,Latitude,Longitude,RegionType,Size,PurposeType`
  - 作用: 定义全球分布的地面用户终端位置和属性

- **`cloud_station.csv`**: 云中心配置
  - 作用: 定义云计算中心的地理位置

#### **配置层**
- **`config.yaml`**: 系统全局配置
  - 系统架构参数：卫星数量、轨道参数、仿真时长
  - 通信模型参数：频率、功率、带宽、信道参数
  - 计算模型参数：CPU频率、能效系数、电池容量
  - 排队模型参数：优先级权重、队列管理
  - 强化学习参数：DPPO算法超参数
  - 奖励函数参数：各种奖励权重和惩罚参数

#### **物理层**
- **`orbital_updater.py`**: 轨道动力学和几何计算
  - 核心类: `OrbitalUpdater`, `Satellite`, `GroundStation`
  - 核心功能: 
    - 轨道数据加载和时间步更新
    - 星间、星地、星云可见性矩阵计算
    - 3D距离计算和地面覆盖分析
    - 支持420个地面用户和多个云中心

- **`communication.py`**: 基于物理模型的通信链路管理
  - 核心类: `CommunicationManager`
  - 核心功能: 
    - 信道增益计算（基于路径损耗公式）
    - 信号强度和信噪比计算
    - 香农容量和数据传输速率计算
    - 传输延迟和能耗计算
    - 支持不同类型链路（星间、星地、星云）

#### **实体层**
- **`satellite.py`**: 完整的卫星实体实现
  - 核心类: `SatelliteNode`
  - 状态管理: `Position`, `EnergyState`, `ResourceState`, `CommunicationState`, `PerformanceMetrics`
  - 核心功能:
    - 完整的能量管理（太阳能充电、功耗计算）
    - 任务队列管理和优先级调度
    - 任务处理进度跟踪
    - 任务卸载和传输成本计算
    - 与外部模块的状态同步
  
- **`task.py`**: 任务实体完整生命周期管理
  - 核心类: `Task`, `TaskLoader`, `TaskManager`
  - 核心功能:
    - 动态优先级计算（基于紧迫性和成本）
    - 任务状态转换管理
    - 处理历史和传输记录
    - 重试机制和性能统计
    - 资源需求估算

- **`task_generator.py`**: 智能任务生成器
  - 核心类: `TaskGenerator`, `Location`, `Task`
  - 核心功能: 基于地理位置和功能类型的泊松分布任务生成

#### **适配层**
- **`adapters.py`**: 模块间适配器，实现数据转换和接口统一
  - 核心类: `SatelliteAdapter`, `TaskAdapter`
  - 核心功能:
    - `SatelliteAdapter`: orbital_updater与satellite模块的数据转换
    - `TaskAdapter`: 任务加载和分配逻辑
    - 状态同步和外部模块引用管理

#### **环境层**
- **`satellite_env.py`**: 多智能体强化学习环境
  - 核心类: `SatelliteEnvironment`, `IndependentWrapper`, `CentralizedWrapper`
  - 核心功能:
    - 完整的多智能体RL接口
    - 支持独立学习和集中式训练
    - 观测空间和动作空间管理
    - 奖励函数和协作指标计算


---

## 2. 文件间依赖关系

### 2.1 架构层次依赖图

```
satellite_env.py (主环境控制器)
├── adapters.py (适配器层)
│   ├── orbital_updater.py
│   ├── communication.py
│   ├── satellite.py
│   └── task.py
├── env_interface.py (RL接口层)
│   ├── adapters.py
│   ├── satellite.py
│   └── task.py
└── config.yaml
```

### 2.2 数据层依赖图

```
orbital_updater.py
├── satellite_processed_data1.csv
├── updated_global_ground_stations.csv
└── cloud_station.csv

task_generator.py
└── updated_global_ground_stations.csv

task.py
└── task_data/task_generation_results.json

communication.py
└── orbital_updater.py (运行时依赖)
```

### 2.3 模块间运行时依赖

1. **核心数据流**:
   - `orbital_updater.py` 加载轨道和地面站数据
   - `communication.py` 依赖 `orbital_updater.py` 计算链路状态
   - `satellite.py` 接收来自 `orbital_updater.py` 和 `communication.py` 的状态同步
   - `task.py` 独立加载任务数据，通过 `TaskAdapter` 分发

2. **适配器协调**:
   - `SatelliteAdapter` 管理 `orbital_updater.py` → `satellite.py` 的数据转换
   - `TaskAdapter` 管理任务加载和分配逻辑
   - 两个适配器协调卫星状态同步和任务分配

3. **环境层集成**:
   - `satellite_env.py` 通过适配器统一管理所有模块
   - `env_interface.py` 提供RL算法的标准接口
   - 支持独立学习和集中式训练两种模式

---

## 3. 程序运行流程

### 3.1 环境初始化流程

```python
# satellite_env.py 主环境初始化
def __init__(self):
    # 1. 初始化适配器
    self.satellite_adapter = SatelliteAdapter()
    self.task_adapter = TaskAdapter()
    
    # 2. 获取环境参数
    config = self.satellite_adapter.orbital_updater.config
    self.num_satellites = config['system']['num_leo_satellites']
    self.total_timeslots = config['system']['total_timeslots']
    
    # 3. 设置RL接口（如果启用）
    if enable_rl:
        self.rl_interface = OAALEnvironmentInterface()
        self.rl_interface.satellite_adapter = self.satellite_adapter
        self.rl_interface.task_adapter = self.task_adapter
```

```python
# adapters.py 适配器初始化
class SatelliteAdapter:
    def __init__(self):
        # 初始化外部模块
        self.orbital_updater = OrbitalUpdater()
        self.comm_manager = CommunicationManager()
        self.comm_manager.set_orbital_updater(self.orbital_updater)
        
        # 创建卫星节点字典
        self.satellite_nodes = {}
```

### 3.2 主仿真循环流程

```python
def step(self, actions=None):
    # 1. 应用RL动作（如果有）
    if actions and self.enable_rl:
        self.rl_interface._apply_actions(actions)
    
    # 2. 同步轨道状态
    self.satellite_adapter.sync_orbital_states(self.current_step)
    
    # 3. 同步通信状态
    self.satellite_adapter.sync_communication_states(self.current_step)
    
    # 4. 加载新任务
    new_tasks = self.task_adapter.load_tasks_for_timeslot(self.current_step)
    
    # 5. 任务分配
    satellites = self.satellite_adapter.get_satellite_nodes()
    task_assignments = self.task_adapter.assign_tasks_to_satellites(new_tasks, satellites)
    
    # 6. 执行卫星时间步
    for satellite in satellites.values():
        satellite.step(self.current_step, self.timeslot_duration)
    
    # 7. 收集结果
    return self._collect_step_results(new_tasks, task_assignments, actions)
```

### 3.3 状态同步流程

```python
# SatelliteAdapter 中的状态同步
def sync_orbital_states(self, time_step):
    """轨道状态同步"""
    orbital_satellites = self.orbital_updater.get_satellites_at_time(time_step)
    for satellite_id, orbital_satellite in orbital_satellites.items():
        if satellite_id in self.satellite_nodes:
            self.satellite_nodes[satellite_id].sync_with_orbital_state(orbital_satellite)

def sync_communication_states(self, time_step):
    """通信状态同步"""
    for satellite_id, satellite_node in self.satellite_nodes.items():
        # 获取邻居和可见地面站
        neighbors = self.comm_manager.get_neighbors(satellite_id, time_step)
        visible_ground_stations = self._get_visible_ground_stations(satellite_id, time_step)
        
        # 构建网络状态
        network_state = {
            'neighbors': neighbors,
            'ground_stations': visible_ground_stations
        }
        satellite_node.sync_with_communication_state(network_state, time_step)
```

### 3.4 任务处理流程

```python
# 卫星节点中的任务处理
def step(self, time_step, time_delta):
    """卫星节点时间步处理"""
    self.current_timeslot = time_step
    self.current_time = time_step * time_delta
    
    # 更新各个状态
    self.update_energy(time_delta)
    self.update_processing(time_delta)
    
    # 尝试调度新任务
    if not self.current_processing_task:
        self.schedule_next_task()

def schedule_next_task(self):
    """优先级调度算法"""
    if not self.task_queue:
        return False
    
    # 选择优先级最高且未超时的任务
    eligible_tasks = [task for task in self.task_queue 
                     if task.deadline_timestamp > self.current_time]
    
    if eligible_tasks:
        selected_task = max(eligible_tasks, key=lambda t: t.priority)
        self.task_queue.remove(selected_task)
        self.current_processing_task = selected_task
        selected_task.start_processing(self.satellite_id, self.current_time)
        return True
    return False
```

---

## 4. 核心模块接口设计

### 4.1 轨道更新器接口 (orbital_updater.py)

```python
class OrbitalUpdater:
    def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]:
        """
        获取指定时间步的所有卫星状态
        输入: 时间步索引 (0-999)
        输出: {satellite_id: Satellite对象}
        """
    
    def build_inter_satellite_visibility_matrix(self, satellites) -> np.ndarray:
        """
        构建卫星间可见性矩阵
        输入: 卫星字典
        输出: 可见性矩阵 (36x36 bool矩阵)
        """
    
    def build_satellite_ground_visibility_matrix(self, satellites) -> np.ndarray:
        """
        构建卫星-地面站可见性矩阵
        输入: 卫星字典  
        输出: 卫星-地面站可见性矩阵 (36x420 bool矩阵)
        """
    
    def build_satellite_cloud_visibility_matrix(self, satellites) -> np.ndarray:
        """
        构建卫星-云中心可见性矩阵
        输入: 卫星字典
        输出: 卫星-云中心可见性矩阵 (36xN_cloud bool矩阵)
        """
```

### 4.2 通信管理器接口 (communication.py)

```python
class CommunicationManager:
    def get_all_link_states(self, time_step: int) -> Dict[Tuple[str, str], Dict[str, float]]:
        """
        获取所有链路状态 - 主要接口方法
        输入: 时间步索引
        输出: {(source_id, target_id): {
            'distance_km': float,
            'data_rate_mbps': float,
            'transmission_delay_ms': float,
            'transmission_energy_j': float,
            'signal_strength_dbm': float,
            'snr_db': float,
            'link_type': str  # 'inter_satellite'/'user_to_satellite'/'satellite_to_cloud'等
        }}
        """
    
    def calculate_data_rate(self, distance: float, power: float, bandwidth: float) -> float:
        """
        计算链路数据传输速率 - 基于香农公式
        输入: 距离(km), 发射功率(W), 带宽(Hz)
        输出: 数据传输速率(Mbps)
        """
    
    def get_neighbors(self, node_id: str, time_step: int) -> List[str]:
        """
        获取节点的所有邻居
        输入: 节点ID, 时间步
        输出: 邻居节点ID列表
        """
```

### 4.3 卫星实体接口 (satellite.py)

```python
class SatelliteNode:
    def sync_with_orbital_state(self, orbital_satellite):
        """
        与orbital_updater.py同步轨道状态
        输入: 来自orbital_updater的卫星对象
        """
    
    def sync_with_communication_state(self, network_state: Dict, time_step: int):
        """
        与communication.py同步网络状态
        输入: 网络状态信息, 当前时间步
        """
    
    def receive_task(self, task: Task) -> bool:
        """
        接收新任务
        输入: 任务对象
        输出: 是否成功接收
        """
    
    def step(self, time_step: int, time_delta: float):
        """
        执行一个时间步
        输入: 时间步索引, 时间间隔（秒）
        """
    
    def export_observation_data(self) -> Dict[str, Any]:
        """
        导出用于强化学习观测的完整状态数据
        输出: 完整的观测数据字典
        """
```

### 4.4 任务管理接口 (task.py)

```python
class Task:
    def calculate_dynamic_priority(self, current_time: float, estimated_processing_time: float) -> float:
        """
        计算动态优先级分数
        Score(Ti, t_now) = w_p * f_p(Pi) + w_d * f_d(Di, t_now) - w_c * f_c(Si, Ci)
        """
    
    def update_processing_progress(self, cycles_processed: int, energy_consumed: float, current_time: float):
        """
        更新处理进度
        """
    
    def is_completed(self) -> bool:
        """
        检查任务是否完成
        """

class TaskLoader:
    def get_tasks_for_timeslot(self, timeslot: int, config: Dict = None) -> List[Task]:
        """
        获取指定时隙的所有任务
        输入: 时隙索引, 配置参数
        输出: 任务列表
        """
```

### 4.5 适配器接口 (adapters.py)

```python
class SatelliteAdapter:
    def create_satellite_nodes(self, time_step: int = 0) -> Dict[str, SatelliteNode]:
        """
        创建所有卫星节点
        输入: 初始时间步
        输出: 卫星节点字典
        """
    
    def sync_orbital_states(self, time_step: int):
        """
        同步轨道状态
        """
    
    def sync_communication_states(self, time_step: int):
        """
        同步通信状态
        """

class TaskAdapter:
    def load_tasks_for_timeslot(self, timeslot: int) -> List[Task]:
        """
        加载指定时隙的任务
        """
    
    def assign_tasks_to_satellites(self, tasks: List[Task], satellites: Dict[str, SatelliteNode]) -> Dict[str, int]:
        """
        将地面用户任务分配给可见卫星
        输出: 每个卫星分配到的任务数量
        """
```

### 4.6 环境接口 (env_interface.py)

```python
class OAALEnvironmentInterface:
    def get_valid_actions(self, satellite_id: str, time_step: int) -> List[int]:
        """
        获取当前时刻卫星的有效动作
        输出: 有效动作列表 [0=本地处理, 1-36=卫星卸载, 37-41=云卸载]
        """
    
    def decode_action(self, action: int, satellite_id: str) -> Dict[str, Any]:
        """
        解码动作为具体操作
        输出: {"type": "local"/"satellite_offload"/"cloud_offload", "target": target_id}
        """
    
    def get_action_space_info(self) -> Dict[str, Any]:
        """
        获取动作空间信息
        输出: 动作空间描述和配置信息
        """
```

---

## 5. 关键数据结构说明

### 5.1 卫星轨道数据格式

```csv
# satellite_processed_data1.csv
satellite_id,timestamp,latitude,longitude,illuminated
Satellite111,2025-06-08 04:00:00,0.0,43.204,True
Satellite111,2025-06-08 04:00:10,0.363,43.465,True
Satellite112,2025-06-08 04:00:00,1.2,45.123,False
```

- **36颗卫星** (Satellite111-Satellite146) × **1000个时隙** = 36,000条记录
- **时隙间隔**: 10秒
- **光照状态**: 影响卫星太阳能充电和能量管理

### 5.2 地面站数据格式

```csv
# updated_global_ground_stations.csv
ID,Latitude,Longitude,RegionType,Size,PurposeType
1,-65,-180,Land,Medium,Normal
2,-65,-168,Land,Large,Industrial
3,45.2,102.3,Ocean,Small,DelaySensitive
```

- **420个地面用户终端**
- **RegionType**: Land/Ocean (影响任务生成率: Land=高, Ocean=低)
- **Size**: Small/Medium/Large (影响任务生成率: Large>Medium>Small)
- **PurposeType**: Normal/Industrial/DelaySensitive (影响任务类型分布)

### 5.3 任务生成结果数据格式

```json
# task_data/task_generation_results.json
{
  "simulation_metadata": {
    "total_locations": 420,
    "total_timeslots": 1000,
    "timeslot_duration_seconds": 10,
    "total_tasks_generated": 156789
  },
  "simulation_results": [
    {
      "timeslot": 0,
      "timestamp": 0.0,
      "total_tasks": 157,
      "locations": [
        {
          "location_id": 1,
          "coordinates": [-65, -180],
          "geography": "Land",
          "scale": "Medium",
          "functional_type": "Normal",
          "lambda_i": 6.0,
          "num_tasks": 3,
          "generated_tasks": [
            {
              "task_id": 1,
              "type_id": 2,
              "data_size_mb": 35.67,
              "complexity_cycles_per_bit": 200,
              "deadline_timestamp": 50.0,
              "priority": 3
            }
          ]
        }
      ]
    }
  ]
}
```

### 5.4 卫星状态数据结构

```python
# satellite.py 中的核心数据结构
@dataclass
class Position:
    latitude: float = 0.0
    longitude: float = 0.0
    altitude: float = 0.0
    timestamp: float = 0.0
    is_illuminated: bool = False

@dataclass
class EnergyState:
    current_battery_j: float = 0.0
    battery_capacity_j: float = 0.0
    solar_power_w: float = 0.0
    base_power_consumption_w: float = 50.0
    is_illuminated: bool = False

@dataclass
class CommunicationState:
    visible_neighbors: List[str] = None
    visible_ground_stations: List[str] = None
    active_links: Dict[str, Any] = None
```

### 5.5 任务状态数据结构

```python
# task.py 中的核心数据结构
class TaskState(Enum):
    GENERATED = "generated"
    QUEUED = "queued"
    PROCESSING = "processing"
    TRANSFERRING = "transferring"
    RETRYING = "retrying"
    RETURNING = "returning"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ProcessingRecord:
    satellite_id: str
    start_time: float
    end_time: float
    cpu_cycles_processed: int
    energy_consumed: float
    completion_ratio: float
    is_partial: bool
```

### 5.6 通信链路数据结构

```python
# communication.py 中的链路状态格式
link_state = {
    'distance_km': 1247.3,
    'data_rate_mbps': 85.4,
    'transmission_delay_ms': 12.7,
    'transmission_energy_j': 0.045,
    'signal_strength_dbm': -87.2,
    'snr_db': 18.3,
    'link_type': 'inter_satellite'  # 或 'user_to_satellite', 'satellite_to_cloud'
}
```

---

## 6. 当前实现状态总结

### 6.1 已完成功能模块

#### **核心物理层** ✅
1. **`orbital_updater.py`**: 完整实现
   - 卫星轨道数据加载 (36颗卫星，1000时隙)
   - 420个地面用户终端和云中心数据管理
   - 星间、星地、星云可见性矩阵计算
   - 3D距离计算和地面覆盖分析

2. **`communication.py`**: 完整实现
   - 基于物理模型的通信链路计算
   - 信道增益、信噪比、香农容量计算
   - 支持多种链路类型 (星间/星地/星云)
   - 传输延迟和能耗精确计算

#### **核心实体层** ✅
3. **`satellite.py`**: 完整实现
   - 完整的卫星节点实体 (`SatelliteNode`)
   - 能量管理 (太阳能充电、功耗计算)
   - 任务队列管理和优先级调度
   - 任务卸载和传输成本计算
   - 多种状态数据结构 (Position, Energy, Communication等)

4. **`task.py`**: 完整实现
   - 任务实体完整生命周期管理
   - 动态优先级计算算法
   - 任务状态转换和处理历史追踪
   - TaskLoader和TaskManager支持

#### **适配器层** ✅
5. **`adapters.py`**: 完整实现
   - SatelliteAdapter: 模块间数据转换
   - TaskAdapter: 任务加载和分配逻辑
   - 状态同步和外部模块引用管理

#### **环境层** ✅
6. **`satellite_env.py`**: 完整实现
   - 多智能体强化学习环境
   - 支持独立学习 (`IndependentWrapper`)
   - 支持集中式训练 (`CentralizedWrapper`)
   - 完整的观测空间和奖励函数

7. **`env_interface.py`**: 完整实现
   - RL环境接口和动作空间管理
   - 动态动作掩码 (本地+卫星卸载+云卸载)
   - 动作编码解码和容错处理

#### **配置和数据** ✅
8. **`config.yaml`**: 完整配置
   - 系统架构、通信模型、计算模型参数
   - 强化学习算法超参数
   - 奖励函数和惩罚参数

9. **数据文件**: 完整
   - 轨道数据、地面站数据、云中心数据
   - 任务生成结果数据

### 6.2 系统架构特点

#### **设计优势** ✨
1. **模块化架构**: 清晰的层次划分，松耦合高内聚
2. **适配器模式**: 通过adapters.py实现模块间解耦
3. **多算法支持**: 同时支持独立学习和集中式训练
4. **物理模型准确**: 基于真实物理公式的通信和轨道计算
5. **完整状态管理**: 卫星能量、任务处理、通信状态全面跟踪
6. **扩展性良好**: 接口设计考虑后续算法和功能扩展

#### **技术亮点** 🚀
1. **动态优先级调度**: 基于紧迫性和成本的智能任务调度
2. **精确能量建模**: 太阳能充电和功耗的时序建模
3. **多层次可见性**: 星间、星地、星云三种链路类型全覆盖
4. **RL接口标准化**: 符合Gym标准的多智能体环境接口
5. **状态同步机制**: 轨道、通信、任务状态的实时同步

### 6.3 当前系统完整性

该SPACE-OAAL环境已经是一个**功能完整的卫星边缘计算仿真平台**，而非MVP版本：

- ✅ **完整的物理仿真**: 轨道动力学 + 通信链路 + 能量管理
- ✅ **完整的实体建模**: 卫星节点 + 任务实体 + 生命周期管理  
- ✅ **完整的RL支持**: 多智能体环境 + 观测动作空间 + 奖励函数
- ✅ **完整的数据支持**: 真实轨道数据 + 地理分布 + 任务生成
- ✅ **完整的系统集成**: 适配器模式 + 状态同步 + 错误处理

### 6.4 使用指南

#### **基础仿真运行**
```python
from satellite_env import SatelliteEnvironment

# 创建环境
env = SatelliteEnvironment()
env.reset()

# 运行仿真
for step in range(100):
    result = env.step()
    env.render()

env.close()
```

#### **强化学习接口使用**
```python
from satellite_env import SatelliteEnvironment, IndependentWrapper

# 独立学习模式
env = IndependentWrapper(SatelliteEnvironment())
obs = env.reset()

# 随机策略测试
for step in range(100):
    actions = {agent: env.action_space.sample() for agent in obs.keys()}
    obs, rewards, done, info = env.step(actions)
    if done:
        break
```

### 6.5 开发建议

该环境已经具备了完整的仿真能力，建议后续开发重点：

1. **算法集成**: 集成MAPPO、DPPO等多智能体RL算法
2. **性能调优**: 优化大规模矩阵计算和状态更新性能
3. **可视化增强**: 添加轨道可视化和网络拓扑动画
4. **故障建模**: 添加卫星故障和网络中断仿真
5. **实验分析**: 开发实验结果分析和性能评估工具

该仿真平台已经为SPACE-OAAL项目提供了坚实的基础环境支撑。
