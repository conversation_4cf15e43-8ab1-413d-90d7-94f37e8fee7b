# SPACE-OAAL 环境架构图生成指导文档

## 1. 程序架构概述

SPACE-OAAL 环境是一个分层的、模块化的LEO卫星星座边缘计算仿真平台。整个系统采用**适配器模式**和**分层架构**设计，实现了新旧模块的无缝集成和标准化接口。

### 核心架构特点：
- **分层设计**：配置层 → 物理层 → 实体层 → 适配器层 → 接口层
- **模块解耦**：各模块通过标准化接口通信，降低耦合度
- **适配器集成**：通过适配器模式实现新旧模块的兼容
- **状态管理**：集中式状态管理与分布式实体状态相结合
- **数据驱动**：基于真实轨道数据和任务生成数据的仿真

## 2. Draw.io架构图绘制指导

### 2.1 整体布局建议

**画布尺寸**：建议使用 A3 横向布局 (1600x1200 像素)

**分层布局**（从上到下）：
```
┌─────────────────────────────────────────────────────────────┐
│                    接口层 (Interface Layer)                  │
├─────────────────────────────────────────────────────────────┤
│                   适配器层 (Adapter Layer)                   │
├─────────────────────────────────────────────────────────────┤
│                    实体层 (Entity Layer)                     │
├─────────────────────────────────────────────────────────────┤
│                    物理层 (Physics Layer)                    │
├─────────────────────────────────────────────────────────────┤
│                    配置层 (Configuration Layer)              │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 图形元素和颜色方案

**模块形状建议**：
- **主要程序模块**：圆角矩形 (rounded rectangle)，边框宽度 2px
- **类 (Class)**：矩形 (rectangle)，边框宽度 1px
- **函数/方法**：椭圆 (ellipse)，边框宽度 1px
- **数据文件**：文档形状 (document shape)
- **配置文件**：齿轮形状 (gear shape)
- **接口**：菱形 (diamond)

**颜色方案**：
- **接口层**：深蓝色 (#1f4e79) - 背景浅蓝色 (#d5e3f0)
- **适配器层**：紫色 (#7030a0) - 背景浅紫色 (#e6d9f0)
- **实体层**：绿色 (#548235) - 背景浅绿色 (#e2efda)
- **物理层**：橙色 (#c65911) - 背景浅橙色 (#fce4d6)
- **配置层**：灰色 (#595959) - 背景浅灰色 (#f2f2f2)
- **数据文件**：黄色 (#bf9000) - 背景浅黄色 (#fff2cc)

**连接线类型**：
- **直接调用**：实线箭头，宽度 2px，颜色 #000000
- **数据传递**：虚线箭头，宽度 1px，颜色 #666666
- **继承/实现**：空心箭头，宽度 1px，颜色 #333333
- **依赖关系**：点线箭头，宽度 1px，颜色 #999999
- **主要数据流**：粗实线箭头，宽度 3px，颜色 #d62728

### 2.3 程序模块布局

#### 接口层 (顶部)
```
┌─────────────────────────────────────────────────────────────┐
│  [OAALEnvironmentInterface]                                 │
│  ├─ reset()                                                 │
│  ├─ step(actions)                                           │
│  ├─ _format_state()                                         │
│  ├─ _apply_actions()                                        │
│  ├─ _calculate_rewards()                                    │
│  └─ render()                                                │
└─────────────────────────────────────────────────────────────┘
```

#### 适配器层 (第二层)
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ SatelliteAdapter│  │   TaskAdapter   │  │IntegrationManager│
│ ├─ sync_all_    │  │ ├─ generate_    │  │ ├─ step()       │
│ │  satellites() │  │ │  tasks_for_   │  │ ├─ reset()      │
│ ├─ create_      │  │ │  timeslot()   │  │ ├─ get_system_  │
│ │  satellite_   │  │ ├─ convert_     │  │ │  state()      │
│ │  node()       │  │ │  generated_   │  │ └─ export_      │
│ └─ update_      │  │ │  task()       │  │   results()     │
│   communication│  │ └─ get_enhanced_│  └─────────────────┘
│   _links()      │  │   tasks()       │
└─────────────────┘  └─────────────────┘
```

#### 实体层 (第三层)
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  SatelliteNode  │  │      Task       │  │   TaskManager   │
│ ├─ Position     │  │ ├─ task_id      │  │ ├─ tasks: Dict  │
│ ├─ EnergyState  │  │ ├─ state        │  │ ├─ add_task()   │
│ ├─ ResourceState│  │ ├─ data_size_mb │  │ ├─ remove_task()│
│ ├─ task_queue   │  │ ├─ deadline     │  │ ├─ get_task()   │
│ ├─ neighbors    │  │ ├─ priority     │  │ └─ update_      │
│ ├─ process_task()│  │ ├─ process()    │  │   priorities()  │
│ ├─ offload_task()│  │ ├─ transfer()   │  └─────────────────┘
│ └─ update_      │  │ └─ calculate_   │
│   status()      │  │   priority()    │
└─────────────────┘  └─────────────────┘
```

#### 物理层 (第四层)
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ OrbitalUpdater  │  │CommunicationMgr │  │  TaskGenerator  │
│ ├─ satellite_   │  │ ├─ calculate_   │  │ ├─ locations    │
│ │  data: DF     │  │ │  link_        │  │ ├─ generate_    │
│ ├─ ground_      │  │ │  properties() │  │ │  tasks_for_   │
│ │  stations     │  │ ├─ get_network_ │  │ │  timeslot()   │
│ ├─ get_         │  │ │  state()      │  │ ├─ calculate_   │
│ │  satellites_  │  │ ├─ get_         │  │ │  lambda()     │
│ │  at_time()    │  │ │  neighbors()  │  │ └─ sample_task_ │
│ ├─ calculate_   │  │ └─ get_link_    │  │   type()        │
│ │  visibility() │  │   state()       │  └─────────────────┘
│ └─ get_ground_  │  └─────────────────┘
│   coverage()    │
└─────────────────┘
```

#### 配置层 (底部)
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   config.yaml   │  │satellite_data.csv│  │ground_stations. │
│ ├─ system       │  │ ├─ satellite_id │  │ csv             │
│ ├─ communication│  │ ├─ timestamp    │  │ ├─ ID           │
│ ├─ computation  │  │ ├─ latitude     │  │ ├─ Latitude     │
│ ├─ queuing      │  │ ├─ longitude    │  │ ├─ Longitude    │
│ ├─ reward       │  │ └─ illuminated  │  │ ├─ RegionType   │
│ └─ fault        │  └─────────────────┘  │ └─ PurposeType  │
└─────────────────┘                       └─────────────────┘
```

## 3. 程序模块详细描述

### 3.1 接口层模块

#### OAALEnvironmentInterface
- **文件位置**：`src/env/env_interface.py`
- **主要功能**：提供标准化的强化学习环境接口
- **核心职责**：
  - 环境状态格式化和标准化
  - 动作应用和执行
  - 奖励函数计算
  - 环境步进逻辑控制
- **关键类和函数**：
  - `OAALEnvironmentInterface` 类
  - `reset()` - 环境重置
  - `step(actions)` - 环境步进
  - `_format_state()` - 状态格式化
  - `_apply_actions()` - 动作应用
  - `_calculate_rewards()` - 奖励计算
- **对外接口**：
  - 标准 Gym 环境接口
  - 状态空间和动作空间定义
  - 奖励函数接口
- **依赖模块**：
  - `adapters.IntegrationManager`
  - `task.Task`, `task.TaskState`
  - `satellite.SatelliteNode`

### 3.2 适配器层模块

#### IntegrationManager (adapters.py)
- **文件位置**：`src/env/adapters.py`
- **主要功能**：统一管理和协调所有子适配器
- **核心职责**：
  - 协调卫星适配器和任务适配器
  - 执行仿真时间步
  - 管理系统状态
  - 导出仿真结果
- **关键类和函数**：
  - `IntegrationManager` 类
  - `step(timeslot)` - 执行时间步
  - `reset()` - 重置系统
  - `get_system_state()` - 获取系统状态
- **对外接口**：
  - 时间步执行接口
  - 系统状态查询接口
  - 结果导出接口

#### SatelliteAdapter (adapters.py)
- **文件位置**：`src/env/adapters.py`
- **主要功能**：处理轨道更新器与卫星节点之间的数据转换
- **核心职责**：
  - 同步轨道数据到卫星节点
  - 更新通信链路信息
  - 管理卫星状态转换
- **关键类和函数**：
  - `SatelliteAdapter` 类
  - `sync_all_satellites()` - 同步所有卫星
  - `create_satellite_node()` - 创建卫星节点
  - `update_satellite_node()` - 更新卫星节点
- **接口调用**：
  - 调用 `orbital_updater.get_satellites_at_time()`
  - 调用 `communication_manager.get_network_state()`
  - 创建和更新 `satellite.SatelliteNode` 实例

#### TaskAdapter (adapters.py)
- **文件位置**：`src/env/adapters.py`
- **主要功能**：处理任务生成器与增强任务之间的转换
- **核心职责**：
  - 生成时隙任务
  - 转换任务格式
  - 管理任务生命周期
- **关键类和函数**：
  - `TaskAdapter` 类
  - `generate_tasks_for_timeslot()` - 生成时隙任务
  - `convert_generated_task()` - 转换任务格式
  - `get_enhanced_tasks()` - 获取增强任务
- **接口调用**：
  - 调用 `task_generator.generate_tasks_for_timeslot()`
  - 创建 `task.Task` 实例

### 3.3 实体层模块

#### SatelliteNode (satellite.py)
- **文件位置**：`src/env/satellite.py`
- **主要功能**：卫星节点实体，管理卫星的完整状态和行为
- **核心职责**：
  - 维护卫星位置、能量、资源状态
  - 管理任务队列和处理
  - 处理通信链路和邻居关系
  - 执行任务卸载决策
- **关键类和函数**：
  - `SatelliteNode` 类
  - `Position`, `EnergyState`, `ResourceState` 数据类
  - `process_task()` - 处理任务
  - `offload_task()` - 卸载任务
  - `update_status()` - 更新状态
  - `get_best_offload_target()` - 获取最佳卸载目标
- **数据结构**：
  - `task_queue: List[str]` - 任务队列
  - `neighbors: Dict[str, LinkInfo]` - 邻居信息
  - `ground_stations: Dict[str, LinkInfo]` - 地面站信息
- **对外接口**：
  - 任务处理接口
  - 状态查询接口
  - 卸载决策接口

#### Task (task.py)
- **文件位置**：`src/env/task.py`
- **主要功能**：任务实体，管理任务的完整生命周期
- **核心职责**：
  - 维护任务状态和属性
  - 跟踪处理历史
  - 计算动态优先级
  - 管理重试机制
- **关键类和函数**：
  - `Task` 类
  - `TaskState` 枚举
  - `ProcessingRecord`, `TransferRecord` 数据类
  - `process()` - 处理任务
  - `transfer()` - 传输任务
  - `calculate_priority()` - 计算优先级
- **数据结构**：
  - `processing_records: List[ProcessingRecord]` - 处理记录
  - `transfer_records: List[TransferRecord]` - 传输记录
  - `retry_record: RetryRecord` - 重试记录
- **对外接口**：
  - 任务处理接口
  - 状态查询接口
  - 优先级计算接口

#### TaskManager (task.py)
- **文件位置**：`src/env/task.py`
- **主要功能**：任务管理器，统一管理所有任务
- **核心职责**：
  - 维护任务字典
  - 提供任务查询接口
  - 管理任务优先级
- **关键类和函数**：
  - `TaskManager` 类
  - `add_task()` - 添加任务
  - `remove_task()` - 移除任务
  - `get_task()` - 获取任务
  - `update_priorities()` - 更新优先级

### 3.4 物理层模块

#### OrbitalUpdater (orbital_updater.py)
- **文件位置**：`src/env/orbital_updater.py`
- **主要功能**：轨道动力学管理，提供卫星位置和可见性计算
- **核心职责**：
  - 加载和管理轨道数据
  - 计算卫星间可见性
  - 计算地面覆盖
  - 提供位置查询接口
- **关键类和函数**：
  - `OrbitalUpdater` 类
  - `Satellite`, `GroundStation` 数据类
  - `get_satellites_at_time()` - 获取时刻卫星状态
  - `calculate_visibility_matrix()` - 计算可见性矩阵
  - `get_ground_coverage()` - 获取地面覆盖
- **数据源**：
  - `satellite_processed_data.csv` - 卫星轨道数据
  - `updated_global_ground_stations.csv` - 地面站数据
- **对外接口**：
  - 卫星位置查询接口
  - 可见性计算接口
  - 地面覆盖查询接口

#### CommunicationManager (communication.py)
- **文件位置**：`src/env/communication.py`
- **主要功能**：通信链路计算，提供链路质量和网络状态
- **核心职责**：
  - 计算链路属性（速率、延迟、能耗）
  - 管理网络状态
  - 提供邻居查询
  - 计算信号强度和信噪比
- **关键类和函数**：
  - `CommunicationManager` 类
  - `LinkState`, `NetworkState` 数据类
  - `calculate_link_properties()` - 计算链路属性
  - `get_network_state()` - 获取网络状态
  - `get_neighbors()` - 获取邻居
- **物理模型**：
  - 自由空间路径损耗模型
  - 香农容量公式
  - 信号强度和信噪比计算
- **对外接口**：
  - 链路质量查询接口
  - 网络状态接口
  - 邻居查询接口

#### TaskGenerator (task_generator.py)
- **文件位置**：`src/env/task_generator.py`
- **主要功能**：基于地理分布的任务生成
- **核心职责**：
  - 基于霍克斯过程生成任务
  - 根据地理位置特征调整生成率
  - 生成不同类型的任务
  - 管理任务属性分布
- **关键类和函数**：
  - `TaskGenerator` 类
  - `Location`, `Task` 数据类
  - `generate_tasks_for_timeslot()` - 生成时隙任务
  - `calculate_lambda()` - 计算生成率
  - `sample_task_type()` - 采样任务类型
- **数据源**：
  - `updated_global_ground_stations.csv` - 地面站位置
- **对外接口**：
  - 任务生成接口
  - 位置查询接口

### 3.5 配置层模块

#### config.yaml
- **文件位置**：`src/env/config.yaml`
- **主要功能**：系统配置参数管理
- **配置项**：
  - `system` - 系统架构参数
  - `communication` - 通信模型参数
  - `computation` - 计算模型参数
  - `queuing` - 排队模型参数
  - `reinforcement_learning` - 强化学习参数
  - `reward` - 奖励函数参数
  - `fault` - 故障模型参数
  - `policy_domain` - 策略域参数
  - `gnn` - GNN预测模型参数

#### 数据文件
- **satellite_processed_data.csv**：36颗LEO卫星的轨道数据
- **updated_global_ground_stations.csv**：420个地面用户终端坐标
- **task_generation_results.json**：任务生成结果数据
- **regions.json**：策略域划分数据

## 4. 程序执行流程说明

### 4.1 程序启动和初始化流程

```
1. 环境初始化
   ├─ OAALEnvironmentInterface.__init__()
   │  ├─ 创建 IntegrationManager
   │  ├─ 加载配置文件 config.yaml
   │  └─ 初始化环境参数
   │
   ├─ IntegrationManager.__init__()
   │  ├─ 创建 SatelliteAdapter
   │  │  ├─ 初始化 OrbitalUpdater
   │  │  │  ├─ 加载 satellite_processed_data.csv
   │  │  │  └─ 加载 updated_global_ground_stations.csv
   │  │  └─ 初始化 CommunicationManager
   │  │     └─ 设置轨道更新器引用
   │  │
   │  └─ 创建 TaskAdapter
   │     └─ 初始化 TaskGenerator
   │        └─ 加载地面站位置数据
   │
   └─ 系统就绪，等待环境重置
```

### 4.2 主要业务逻辑执行顺序

```
环境步进循环 (每个时隙):
│
├─ 1. 环境接口层
│  ├─ OAALEnvironmentInterface.step(actions)
│  ├─ _apply_actions(actions) - 应用智能体动作
│  └─ 调用 IntegrationManager.step(timeslot)
│
├─ 2. 适配器协调层
│  ├─ IntegrationManager.step(timeslot)
│  ├─ SatelliteAdapter.sync_all_satellites(timeslot)
│  │  ├─ 调用 OrbitalUpdater.get_satellites_at_time()
│  │  ├─ 更新或创建 SatelliteNode 实例
│  │  └─ 调用 CommunicationManager.get_network_state()
│  │
│  ├─ TaskAdapter.generate_tasks_for_timeslot(timeslot)
│  │  ├─ 调用 TaskGenerator.generate_tasks_for_timeslot()
│  │  └─ 转换为增强任务格式
│  │
│  └─ 任务分配到卫星节点
│
├─ 3. 实体处理层
│  ├─ SatelliteNode.process_task() - 处理本地任务
│  ├─ SatelliteNode.offload_task() - 执行任务卸载
│  ├─ Task.process() - 更新任务状态
│  └─ Task.calculate_priority() - 重计算优先级
│
├─ 4. 状态收集和格式化
│  ├─ 收集所有卫星状态
│  ├─ 格式化为标准状态格式
│  ├─ 计算奖励函数
│  └─ 返回 (state, reward, done, info)
│
└─ 循环继续或结束
```

### 4.3 模块间调用时序

```
时序图 (单个时隙执行):

OAALEnvironmentInterface  IntegrationManager  SatelliteAdapter  OrbitalUpdater  CommunicationManager
        │                        │                   │               │                    │
        ├─ step(actions) ────────>│                   │               │                    │
        │                        ├─ step(timeslot) ──>│               │                    │
        │                        │                   ├─ sync_all ────>│                    │
        │                        │                   │               ├─ get_satellites ───┤
        │                        │                   │<─ satellites ──┤                    │
        │                        │                   ├─ get_network ─────────────────────>│
        │                        │                   │<─ network_state ────────────────────┤
        │                        │<─ updated_sats ────┤               │                    │
        │                        │                   │               │                    │
        │                        ├─ generate_tasks ──>TaskAdapter     │                    │
        │                        │<─ new_tasks ───────┤               │                    │
        │                        │                   │               │                    │
        │<─ state_result ─────────┤                   │               │                    │
        ├─ _format_state() ───────┤                   │               │                    │
        ├─ _calculate_rewards() ──┤                   │               │                    │
        │                        │                   │               │                    │
```

### 4.4 数据处理和传递流程

```
数据流向图:

配置文件 (config.yaml)
    │
    ├─> OrbitalUpdater ──> 卫星位置数据 ──> SatelliteAdapter ──> SatelliteNode
    │                                                              │
    ├─> CommunicationManager ──> 链路状态 ──> SatelliteAdapter ────┤
    │                                                              │
    ├─> TaskGenerator ──> 原始任务 ──> TaskAdapter ──> 增强任务 ────┤
    │                                                              │
    └─> OAALEnvironmentInterface <──── 格式化状态 <──── IntegrationManager
                │
                └─> 强化学习算法 (外部)
```

## 5. 接口调用分析

### 5.1 核心接口调用关系

#### 环境接口 → 适配器管理器
```python
# OAALEnvironmentInterface → IntegrationManager
integration_manager.step(timeslot)
integration_manager.reset()
integration_manager.get_system_state()
```

#### 适配器管理器 → 子适配器
```python
# IntegrationManager → SatelliteAdapter
satellite_adapter.sync_all_satellites(timeslot)
satellite_adapter.get_all_satellite_nodes()

# IntegrationManager → TaskAdapter
task_adapter.generate_tasks_for_timeslot(timeslot)
task_adapter.get_enhanced_tasks()
```

#### 卫星适配器 → 物理层
```python
# SatelliteAdapter → OrbitalUpdater
orbital_updater.get_satellites_at_time(timeslot)
# 返回: Dict[str, OrbitalSatellite]

# SatelliteAdapter → CommunicationManager
communication_manager.get_network_state(timeslot)
communication_manager.get_neighbors(satellite_id, timeslot)
communication_manager.get_link_state(sat1_id, sat2_id, timeslot)
# 返回: NetworkState, List[str], LinkState
```

#### 任务适配器 → 任务生成器
```python
# TaskAdapter → TaskGenerator
task_generator.generate_tasks_for_timeslot(timeslot)
# 返回: List[GeneratedTask]

# TaskAdapter → Task (增强任务)
Task(task_data, source_location_id, generation_timestamp, config)
# 创建增强任务实例
```

#### 卫星节点 → 任务管理
```python
# SatelliteNode → TaskManager
task_manager.add_task(task)
task_manager.get_task(task_id)
task_manager.remove_task(task_id)

# SatelliteNode → Task
task.process(cpu_cycles, energy_consumed)
task.transfer(from_satellite, to_satellite)
task.calculate_priority(current_time)
```

### 5.2 数据传递格式和内容

#### 轨道数据格式
```python
# OrbitalUpdater.get_satellites_at_time() 返回
{
    "satellite_id": OrbitalSatellite(
        satellite_id="sat_001",
        longitude=120.5,
        latitude=35.2,
        illuminated=True,
        timestamp=datetime_obj
    )
}
```

#### 网络状态格式
```python
# CommunicationManager.get_network_state() 返回
NetworkState(
    timestamp=100,
    inter_satellite_matrix=np.array([[0, 1, 0], [1, 0, 1], [0, 1, 0]]),
    satellite_ground_matrix=np.array([[1, 0, 1], [0, 1, 0]]),
    satellite_ids=["sat_001", "sat_002", "sat_003"],
    ground_station_ids=["gs_001", "gs_002"],
    link_states={
        ("sat_001", "sat_002"): LinkState(
            source_id="sat_001",
            target_id="sat_002",
            link_type=LinkType.INTER_SATELLITE,
            is_visible=True,
            distance_km=1500.0,
            data_rate_mbps=100.0,
            transmission_delay_ms=5.0,
            transmission_energy_j=0.1,
            signal_strength_dbm=-80.0,
            snr_db=15.0,
            timestamp=100
        )
    },
    network_metrics={"connectivity": 0.85, "avg_distance": 1200.0}
)
```

#### 任务数据格式
```python
# TaskGenerator.generate_tasks_for_timeslot() 返回
[
    GeneratedTask(
        task_id=1001,
        type_id=1,
        data_size_mb=10.5,
        complexity_cycles_per_bit=1000,
        deadline_timestamp=150.0,
        priority=2
    )
]

# 转换为增强任务
Task(
    task_id="task_1001",
    task_type=1,
    data_size_mb=10.5,
    complexity_cycles_per_bit=1000,
    deadline_timestamp=150.0,
    priority=2,
    state=TaskState.GENERATED,
    processing_records=[],
    transfer_records=[],
    retry_record=RetryRecord()
)
```

#### 卫星状态格式
```python
# SatelliteNode.get_status_summary() 返回
{
    "satellite_id": "sat_001",
    "position": {
        "latitude": 35.2,
        "longitude": 120.5,
        "altitude": 1200.0
    },
    "status": "active",
    "resources": {
        "CPU": {
            "frequency_hz": 10e9,
            "utilization": 0.65
        },
        "memory": {
            "total_mb": 1024,
            "used_mb": 512
        }
    },
    "energy": {
        "battery_ratio": 0.85,
        "is_illuminated": True,
        "power_consumption_w": 150.0
    },
    "task_queues": {
        "queued": 5,
        "processing": 2,
        "completed": 15,
        "failed": 1
    },
    "links": {
        "neighbors": 4,
        "active_links": 3
    },
    "performance": {
        "average_response_time": 2.5,
        "throughput_tasks_per_second": 0.8
    }
}
```

### 5.3 调用频率和时机

#### 高频调用 (每时隙)
- `IntegrationManager.step()` - 每时隙1次
- `SatelliteAdapter.sync_all_satellites()` - 每时隙1次
- `OrbitalUpdater.get_satellites_at_time()` - 每时隙1次
- `CommunicationManager.get_network_state()` - 每时隙1次
- `TaskAdapter.generate_tasks_for_timeslot()` - 每时隙1次

#### 中频调用 (按需)
- `SatelliteNode.process_task()` - 每个任务处理时
- `Task.calculate_priority()` - 优先级更新时
- `CommunicationManager.get_link_state()` - 卸载决策时

#### 低频调用 (初始化/配置)
- 配置文件加载 - 系统启动时
- 数据文件加载 - 模块初始化时
- `TaskManager.update_priorities()` - 周期性更新

## 6. Draw.io 绘制步骤指导

### 6.1 创建画布和分层

1. **新建画布**：选择空白画布，设置为 A3 横向 (1600x1200)
2. **创建分层背景**：
   - 使用矩形工具创建5个水平分层区域
   - 从上到下分别设置为对应的背景颜色
   - 添加分层标题文本

### 6.2 绘制模块和组件

1. **接口层**：
   - 绘制 `OAALEnvironmentInterface` 圆角矩形
   - 添加主要方法列表
   - 使用深蓝色边框和浅蓝色填充

2. **适配器层**：
   - 绘制三个适配器模块的圆角矩形
   - 水平排列，等间距
   - 使用紫色边框和浅紫色填充

3. **实体层**：
   - 绘制三个实体模块的圆角矩形
   - 水平排列，等间距
   - 使用绿色边框和浅绿色填充

4. **物理层**：
   - 绘制三个物理模块的圆角矩形
   - 水平排列，等间距
   - 使用橙色边框和浅橙色填充

5. **配置层**：
   - 绘制配置文件和数据文件
   - 使用文档形状和齿轮形状
   - 使用灰色和黄色配色

### 6.3 添加连接线和标注

1. **垂直调用关系**：
   - 接口层 → 适配器层：粗实线箭头
   - 适配器层 → 实体层：实线箭头
   - 适配器层 → 物理层：实线箭头
   - 物理层 → 配置层：虚线箭头

2. **水平协作关系**：
   - 适配器层内部：双向箭头
   - 实体层内部：实线箭头
   - 物理层内部：虚线箭头

3. **添加接口标注**：
   - 在连接线上添加接口方法名
   - 使用小字体标注参数和返回值类型

### 6.4 完善细节和美化

1. **添加图例**：在右下角添加颜色和线条类型说明
2. **添加标题**：在顶部添加"SPACE-OAAL 环境架构图"
3. **调整布局**：确保模块间距合理，避免线条交叉
4. **添加版本信息**：在底部添加版本号和日期

这个架构图描述提供了完整的 SPACE-OAAL 环境程序架构，包括详细的模块关系、接口调用、数据流向和执行时序。您可以根据这个指导在 Draw.io 中绘制出清晰、专业的架构图。
