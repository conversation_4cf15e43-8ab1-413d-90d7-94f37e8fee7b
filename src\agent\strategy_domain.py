"""
策略域管理组件
实现地理绑定的集体智慧机制
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

# 导入TransformerScheduler以实现教师-学生架构一致性
from transformer_scheduler import TransformerScheduler

logger = logging.getLogger(__name__)


@dataclass
class DomainFeatures:
    """策略域特征向量"""
    domain_id: int
    geographic_bounds: Tuple[float, ...]  # (lon_start, lon_end) 或 (lon_start, lon_end, lat_start, lat_end)
    
    # 静态地理特征
    population_density: float = 0.5
    economic_index: float = 0.5
    terrain_complexity: float = 0.5
    
    # 动态时间特征
    local_time: float = 0.0
    is_business_hours: bool = False
    
    # 任务统计特征
    task_arrival_rate: float = 0.0
    avg_task_priority: float = 0.5
    task_completion_rate: float = 0.0
    
    # 网络拓扑特征
    satellite_density: float = 0.0
    avg_link_quality: float = 0.5
    coverage_ratio: float = 0.0
    
    def to_vector(self) -> np.ndarray:
        """将特征转换为向量"""
        return np.array([
            self.population_density,
            self.economic_index,
            self.terrain_complexity,
            self.local_time / 24.0,  # 归一化到[0,1]
            float(self.is_business_hours),
            self.task_arrival_rate,
            self.avg_task_priority,
            self.task_completion_rate,
            self.satellite_density,
            self.avg_link_quality,
            self.coverage_ratio
        ])


class StrategyDomain:
    """单个策略域的实现"""
    
    def __init__(self, domain_id: int, bounds: Tuple[float, ...], 
                 obs_dim: int = 15, task_feature_dim: int = 8, 
                 domain_feature_dim: int = 11, d_model: int = 256,
                 nhead: int = 8, num_encoder_layers: int = 2, 
                 num_decoder_layers: int = 2, num_actions: int = 42):
        self.domain_id = domain_id
        self.bounds = bounds  # 经纬度范围
        self.features = DomainFeatures(domain_id, bounds)
        
        # 参数化配置
        self.obs_dim = obs_dim
        self.task_feature_dim = task_feature_dim
        self.domain_feature_dim = domain_feature_dim
        self.num_actions = num_actions
        
        # 域策略网络 - 使用TransformerScheduler与LEO智能体架构完全一致
        self.policy_network = self._build_policy_network(
            obs_dim, task_feature_dim, d_model, nhead, 
            num_encoder_layers, num_decoder_layers, num_actions
        )
        
        # 性能统计
        self.performance_history = []
        self.visiting_agents = set()  # 当前在域内的智能体
        
    def _build_policy_network(self, obs_dim: int, task_feature_dim: int, 
                             d_model: int, nhead: int, num_encoder_layers: int,
                             num_decoder_layers: int, num_actions: int) -> TransformerScheduler:
        """构建域策略网络 - 使用TransformerScheduler实现教师-学生架构一致性"""
        return TransformerScheduler(
            obs_dim=obs_dim + self.domain_feature_dim,  # 组合观测特征和域特征
            task_feature_dim=task_feature_dim,
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_encoder_layers,
            num_decoder_layers=num_decoder_layers,
            num_actions=num_actions
        )
    
    def update_features(self, current_time: float, task_stats: Dict, 
                       satellite_count: int, link_quality: float):
        """更新域特征"""
        # 更新时间特征
        hours = (current_time / 3600) % 24  # 转换为小时
        self.features.local_time = hours
        self.features.is_business_hours = 8 <= hours <= 18
        
        # 更新任务统计
        if task_stats:
            self.features.task_arrival_rate = task_stats.get('arrival_rate', 0.0)
            self.features.avg_task_priority = task_stats.get('avg_priority', 0.5)
            self.features.task_completion_rate = task_stats.get('completion_rate', 0.0)
        
        # 更新网络拓扑
        self.features.satellite_density = satellite_count / 10.0  # 归一化
        self.features.avg_link_quality = link_quality
        
    def get_action_probs(self, observation, task_features, action_mask: Optional = None):
        """获取动作概率分布 - 支持批处理张量和NumPy数组输入"""
        # 检查输入类型并转换为张量
        if isinstance(observation, np.ndarray):
            # 原有的NumPy接口 - 单个样本
            return self._get_action_probs_numpy(observation, task_features, action_mask)
        else:
            # 新的批处理张量接口 - 批处理
            return self._get_action_probs_batch(observation, task_features, action_mask)
    
    def _get_action_probs_numpy(self, observation: np.ndarray, task_features: np.ndarray,
                               action_mask: Optional[np.ndarray] = None) -> np.ndarray:
        """获取动作概率分布 - NumPy接口（保持向后兼容）"""
        # 组合域特征和观测
        domain_features = self.features.to_vector()
        combined_observation = np.concatenate([observation, domain_features])
        
        # 转换为张量
        obs_tensor = torch.FloatTensor(combined_observation).unsqueeze(0)
        task_tensor = torch.FloatTensor(task_features).unsqueeze(0) if task_features.ndim == 2 else torch.FloatTensor(task_features).unsqueeze(0).unsqueeze(0)
        
        # 转换动作掩码
        mask_tensor = None
        if action_mask is not None:
            mask_tensor = torch.BoolTensor(action_mask).unsqueeze(0) if action_mask.ndim == 2 else torch.BoolTensor(action_mask).unsqueeze(0).unsqueeze(0)
        
        # 前向传播
        with torch.no_grad():
            action_probs = self.policy_network(obs_tensor, task_tensor, mask_tensor)
            
        return action_probs.squeeze().numpy()
    
    def _get_action_probs_batch(self, observations: torch.Tensor, task_features: torch.Tensor,
                               action_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """获取动作概率分布 - 批处理张量接口（高性能）"""
        batch_size = observations.shape[0]
        
        # 获取域特征并扩展到批次大小
        domain_features = torch.FloatTensor(self.features.to_vector()).unsqueeze(0)  # [1, domain_dim]
        domain_features = domain_features.expand(batch_size, -1)  # [batch_size, domain_dim]
        
        # 组合域特征和观测
        combined_observations = torch.cat([observations, domain_features], dim=1)  # [batch_size, obs_dim + domain_dim]
        
        # 前向传播
        with torch.no_grad():
            action_probs = self.policy_network(combined_observations, task_features, action_mask)
            
        return action_probs
    
    def update_policy(self, agent_experiences: List[Dict], learning_rate: float = 0.01):
        """基于智能体经验更新域策略 - 修复策略融合逻辑"""
        if not agent_experiences:
            return
            
        # 计算性能加权
        performances = [exp['performance'] for exp in agent_experiences]
        weights = self._compute_performance_weights(performances)
        
        # 修复的策略融合：先计算加权平均，再一次性更新
        avg_params = self._compute_weighted_average_params(agent_experiences, weights)
        
        # 使用Polyak averaging一次性更新域策略
        self._update_domain_params(avg_params, learning_rate)
    
    def _compute_weighted_average_params(self, agent_experiences: List[Dict], 
                                       weights: np.ndarray) -> Dict[str, torch.Tensor]:
        """计算所有智能体策略参数的加权平均"""
        avg_params = {}
        
        # 初始化累加器
        for name, param in self.policy_network.named_parameters():
            avg_params[name] = torch.zeros_like(param.data)
        
        # 累加所有智能体的加权参数
        for i, exp in enumerate(agent_experiences):
            agent_state_dict = exp['policy_params']
            weight = weights[i]
            
            for name in avg_params.keys():
                if name in agent_state_dict:
                    avg_params[name] += weight * agent_state_dict[name]
                else:
                    logger.warning(f"参数 {name} 在智能体经验中未找到")
        
        return avg_params
    
    def _update_domain_params(self, avg_params: Dict[str, torch.Tensor], 
                            learning_rate: float):
        """使用加权平均参数更新域策略"""
        with torch.no_grad():
            for name, domain_param in self.policy_network.named_parameters():
                if name in avg_params:
                    # Polyak averaging: θ_new = (1-α)θ_old + α*θ_avg
                    domain_param.data.mul_(1 - learning_rate)
                    domain_param.data.add_(learning_rate * avg_params[name])
                else:
                    logger.warning(f"域参数 {name} 在平均参数中未找到")
    
    def _compute_performance_weights(self, performances: List[float]) -> np.ndarray:
        """计算基于性能的权重"""
        if not performances:
            return np.array([])
            
        # 使用softmax温度参数
        temperature = 2.0
        performances = np.array(performances)
        
        # 避免数值溢出
        performances = performances - np.max(performances)
        exp_perfs = np.exp(performances / temperature)
        weights = exp_perfs / np.sum(exp_perfs)
        
        return weights
    
    def add_visiting_agent(self, agent_id: int):
        """添加访问智能体"""
        self.visiting_agents.add(agent_id)
        
    def remove_visiting_agent(self, agent_id: int):
        """移除访问智能体"""
        self.visiting_agents.discard(agent_id)
        
    def is_position_in_domain(self, longitude: float, latitude: float = None) -> bool:
        """检查位置是否在域内"""
        if len(self.bounds) == 2:
            # 原始格式：只有经度范围
            lon_start, lon_end = self.bounds
            if lon_start <= lon_end:
                return lon_start <= longitude <= lon_end
            else:  # 跨越180度经线
                return longitude >= lon_start or longitude <= lon_end
        elif len(self.bounds) == 4:
            # 新格式：经纬度范围
            lon_start, lon_end, lat_start, lat_end = self.bounds
            
            # 检查经度范围
            lon_in_range = False
            if lon_start <= lon_end:
                lon_in_range = lon_start <= longitude <= lon_end
            else:  # 跨越180度经线
                lon_in_range = longitude >= lon_start or longitude <= lon_end
            
            # 检查纬度范围（如果提供了纬度）
            if latitude is not None:
                lat_in_range = lat_start <= latitude <= lat_end
                return lon_in_range and lat_in_range
            else:
                return lon_in_range
        else:
            return False


class DomainManager:
    """策略域管理器"""
    
    def __init__(self, num_domains: int = 24, obs_dim: int = 15, 
                 task_feature_dim: int = 8, domain_feature_dim: int = 11,
                 d_model: int = 256, nhead: int = 8, 
                 num_encoder_layers: int = 2, num_decoder_layers: int = 2,
                 num_actions: int = 42):
        self.num_domains = num_domains
        self.obs_dim = obs_dim
        self.task_feature_dim = task_feature_dim
        self.domain_feature_dim = domain_feature_dim
        
        # 初始化策略域
        self.domains = self._initialize_domains(
            num_domains, obs_dim, task_feature_dim, domain_feature_dim,
            d_model, nhead, num_encoder_layers, num_decoder_layers, num_actions
        )
        
        # 预计算邻居关系
        self.domain_neighbors = self._compute_domain_neighbors()
        
    def _initialize_domains(self, num_domains: int, obs_dim: int, task_feature_dim: int,
                          domain_feature_dim: int, d_model: int, nhead: int,
                          num_encoder_layers: int, num_decoder_layers: int,
                          num_actions: int) -> Dict[int, StrategyDomain]:
        """初始化所有策略域"""
        import json
        from pathlib import Path
        
        domains = {}
        
        # 加载regions.json中的LEO卫星坐标区域
        regions_file = Path(__file__).parent.parent / "env" / "regions.json"
        try:
            with open(regions_file, 'r') as f:
                regions_data = json.load(f)
            
            # 使用regions.json中定义的区域
            for region in regions_data:
                region_id = region['region_id'] - 1  # 转换为0-based索引
                if region_id < num_domains:
                    lon_start = region['longitude_range']['min']
                    lon_end = region['longitude_range']['max']
                    lat_start = region['latitude_range']['min']
                    lat_end = region['latitude_range']['max']
                    
                    domains[region_id] = StrategyDomain(
                        region_id, 
                        (lon_start, lon_end, lat_start, lat_end),
                        obs_dim, task_feature_dim, domain_feature_dim, d_model,
                        nhead, num_encoder_layers, num_decoder_layers, num_actions
                    )
                    logger.info(f"初始化策略域 {region_id}: 经度[{lon_start}, {lon_end}], 纬度[{lat_start}, {lat_end}]")
            
            logger.info(f"成功从regions.json加载了{len(domains)}个LEO卫星策略域")
            
        except FileNotFoundError:
            logger.warning("未找到regions.json文件，使用默认均分策略")
            # 回退到原来的均分策略
            longitude_range = 360.0 / num_domains
            for i in range(num_domains):
                lon_start = i * longitude_range - 180
                lon_end = (i + 1) * longitude_range - 180
                if lon_end > 180:
                    lon_end = lon_end - 360
                domains[i] = StrategyDomain(
                    i, (lon_start, lon_end), obs_dim, task_feature_dim, 
                    domain_feature_dim, d_model, nhead, num_encoder_layers, 
                    num_decoder_layers, num_actions
                )
                logger.info(f"初始化策略域 {i}: 经度范围 [{lon_start:.1f}, {lon_end:.1f}]")
            
        return domains
    
    def get_domain_for_position(self, longitude: float, latitude: float = None) -> Optional[StrategyDomain]:
        """根据经纬度获取对应的策略域"""
        # 将经度归一化到[-180, 180]
        while longitude > 180:
            longitude -= 360
        while longitude < -180:
            longitude += 360
            
        for domain in self.domains.values():
            if domain.is_position_in_domain(longitude, latitude):
                return domain
                
        return None
    
    def update_all_domains(self, current_time: float, global_stats: Dict):
        """更新所有域的特征"""
        for domain in self.domains.values():
            # 获取域内的任务统计
            domain_stats = global_stats.get(domain.domain_id, {})
            
            # 获取域内卫星数量
            satellite_count = len(domain.visiting_agents)
            
            # 更新域特征
            domain.update_features(
                current_time,
                domain_stats,
                satellite_count,
                link_quality=0.8  # 简化处理
            )
    
    def get_neighbor_domains(self, domain_id: int) -> List[StrategyDomain]:
        """获取相邻的策略域 - 使用预计算的地理邻居关系"""
        neighbor_ids = self.domain_neighbors.get(domain_id, [])
        return [self.domains[nid] for nid in neighbor_ids if nid in self.domains]
    
    def _compute_domain_neighbors(self) -> Dict[int, List[int]]:
        """基于地理位置计算所有域的邻居关系"""
        neighbors = {domain_id: [] for domain_id in self.domains.keys()}
        
        # 计算所有域对之间的邻接关系
        domain_ids = list(self.domains.keys())
        for i, domain_a_id in enumerate(domain_ids):
            for j, domain_b_id in enumerate(domain_ids[i+1:], i+1):
                domain_a = self.domains[domain_a_id]
                domain_b = self.domains[domain_b_id]
                
                if self._are_domains_adjacent(domain_a, domain_b):
                    neighbors[domain_a_id].append(domain_b_id)
                    neighbors[domain_b_id].append(domain_a_id)
        
        # 记录邻居关系
        for domain_id, neighbor_list in neighbors.items():
            logger.info(f"策略域 {domain_id} 的邻居: {neighbor_list}")
        
        return neighbors
    
    def _are_domains_adjacent(self, domain_a: StrategyDomain, domain_b: StrategyDomain) -> bool:
        """判断两个域是否地理相邻"""
        bounds_a = domain_a.bounds
        bounds_b = domain_b.bounds
        
        # 处理不同格式的边界
        if len(bounds_a) == 2 and len(bounds_b) == 2:
            # 只有经度范围的情况
            return self._are_longitude_ranges_adjacent(bounds_a, bounds_b)
        elif len(bounds_a) == 4 and len(bounds_b) == 4:
            # 有经纬度范围的情况
            return self._are_geographic_bounds_adjacent(bounds_a, bounds_b)
        else:
            # 格式不匹配，不认为相邻
            return False
    
    def _are_longitude_ranges_adjacent(self, bounds_a: Tuple[float, float], 
                                     bounds_b: Tuple[float, float]) -> bool:
        """判断两个经度范围是否相邻 - 修复跨180度逻辑"""
        lon_start_a, lon_end_a = bounds_a
        lon_start_b, lon_end_b = bounds_b
        
        # 将跨越180度的范围分解为不跨越的子范围
        def decompose_longitude_range(start, end):
            """将经度范围分解为不跨越180度的子范围"""
            if start > end:  # 跨越180度经线
                return [(start, 180.0), (-180.0, end)]
            else:
                return [(start, end)]
        
        ranges_a = decompose_longitude_range(lon_start_a, lon_end_a)
        ranges_b = decompose_longitude_range(lon_start_b, lon_end_b)
        
        # 检查任意两个子范围是否相邻
        tolerance = 1e-6
        for start_a, end_a in ranges_a:
            for start_b, end_b in ranges_b:
                # 检查右边界与左边界是否接触
                if (abs(end_a - start_b) <= tolerance or 
                    abs(end_b - start_a) <= tolerance):
                    return True
        
        return False
    
    def _are_geographic_bounds_adjacent(self, bounds_a: Tuple[float, float, float, float], 
                                      bounds_b: Tuple[float, float, float, float]) -> bool:
        """判断两个地理边界是否相邻 - 修复经度重叠和相邻判断"""
        lon_start_a, lon_end_a, lat_start_a, lat_end_a = bounds_a
        lon_start_b, lon_end_b, lat_start_b, lat_end_b = bounds_b
        
        tolerance = 1e-6
        
        # 1. 检查纬度是否相邻且经度有重叠
        lat_adjacent = (abs(lat_end_a - lat_start_b) <= tolerance or 
                       abs(lat_end_b - lat_start_a) <= tolerance)
        
        if lat_adjacent:
            # 纬度相邻时，检查经度是否有重叠（考虑跨180度情况）
            if self._longitude_ranges_overlap(
                (lon_start_a, lon_end_a), 
                (lon_start_b, lon_end_b)
            ):
                return True
        
        # 2. 检查经度是否相邻且纬度有重叠
        if self._are_longitude_ranges_adjacent(
            (lon_start_a, lon_end_a), 
            (lon_start_b, lon_end_b)
        ):
            # 经度相邻时，检查纬度是否有重叠
            lat_overlap = not (lat_end_a < lat_start_b - tolerance or 
                              lat_end_b < lat_start_a - tolerance)
            if lat_overlap:
                return True
        
        return False
    
    def _longitude_ranges_overlap(self, bounds_a: Tuple[float, float], 
                                bounds_b: Tuple[float, float]) -> bool:
        """判断两个经度范围是否有重叠 - 正确处理跨180度情况"""
        lon_start_a, lon_end_a = bounds_a
        lon_start_b, lon_end_b = bounds_b
        
        # 将跨越180度的范围分解为不跨越的子范围
        def decompose_longitude_range(start, end):
            if start > end:  # 跨越180度经线
                return [(start, 180.0), (-180.0, end)]
            else:
                return [(start, end)]
        
        ranges_a = decompose_longitude_range(lon_start_a, lon_end_a)
        ranges_b = decompose_longitude_range(lon_start_b, lon_end_b)
        
        # 检查任意两个子范围是否有重叠
        tolerance = 1e-6
        for start_a, end_a in ranges_a:
            for start_b, end_b in ranges_b:
                # 检查范围重叠：not (end_a < start_b or end_b < start_a)
                if not (end_a < start_b - tolerance or end_b < start_a - tolerance):
                    return True
        
        return False
    
    
    def transfer_knowledge_between_domains(self, source_id: int, target_id: int, 
                                         transfer_rate: float = 0.1):
        """域间知识传递"""
        if source_id not in self.domains or target_id not in self.domains:
            return
            
        source = self.domains[source_id]
        target = self.domains[target_id]
        
        # 简化的参数传递
        with torch.no_grad():
            for target_param, source_param in zip(
                target.policy_network.parameters(),
                source.policy_network.parameters()
            ):
                target_param.data = (1 - transfer_rate) * target_param.data + \
                                  transfer_rate * source_param.data