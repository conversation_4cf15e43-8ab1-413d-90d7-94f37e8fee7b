<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="Communication模块架构图" id="communication-architecture">
    <mxGraphModel dx="2603" dy="1633" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="main-function" value="main()&#xa;程序入口&#xa;测试和演示" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="565" y="-390" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dependency-group" value="外部依赖层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="72.5" y="-260" width="1125" height="120" as="geometry" />
        </mxCell>
        <mxCell id="config-yaml" value="config.yaml&#xa;通信配置文件&#xa;功率/带宽参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="80" y="50" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="orbital-updater" value="orbital_updater&#xa;轨道更新模块&#xa;卫星位置/可见性" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="280" y="45" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="numpy-lib" value="numpy&#xa;矩阵计算" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="480" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="yaml-lib" value="yaml&#xa;配置解析" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="620" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="math-lib" value="math&#xa;数学计算" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="760" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="typing-lib" value="typing&#xa;类型注解" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="dependency-group" vertex="1">
          <mxGeometry x="900" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="link-type-group" value="支持的5种链路类型" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1270" y="-130" width="220" height="670" as="geometry" />
        </mxCell>
        <mxCell id="inter-satellite" value="inter_satellite&#xa;卫星间链路&#xa;激光通信&#xa;50Gbps固定速率" style="rhombus;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10;fontStyle=1" parent="link-type-group" vertex="1">
          <mxGeometry x="45" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="user-to-satellite" value="user_to_satellite&#xa;用户→卫星上行&#xa;p_u_w: 5W&#xa;b_us_hz: 100MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="link-type-group" vertex="1">
          <mxGeometry x="45" y="170" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="satellite-to-user" value="satellite_to_user&#xa;卫星→用户下行&#xa;p_su_w: 10W&#xa;b_su_hz: 120MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="link-type-group" vertex="1">
          <mxGeometry x="45" y="290" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="satellite-to-cloud" value="satellite_to_cloud&#xa;卫星→云中心下行&#xa;p_sc_w: 15W&#xa;b_sc_hz: 150MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=10" parent="link-type-group" vertex="1">
          <mxGeometry x="45" y="410" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="cloud-to-satellite" value="cloud_to_satellite&#xa;云中心→卫星上行&#xa;p_c_w: 50W&#xa;b_cs_hz: 180MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=10" parent="link-type-group" vertex="1">
          <mxGeometry x="45" y="530" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="control-group" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="235" y="-60" width="800" height="170" as="geometry" />
        </mxCell>
        <mxCell id="communication-manager" value="CommunicationManager&#xa;通信管理器&#xa;- 基于orbital_updater的可见性矩阵&#xa;- 支持5种双向链路类型计算&#xa;- 物理模型准确的链路状态计算&#xa;- 统一的链路查询接口" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" parent="control-group" vertex="1">
          <mxGeometry x="280" y="40" width="240" height="120" as="geometry" />
        </mxCell>
        <mxCell id="interface-group" value="核心接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="90" y="190" width="1030" height="100" as="geometry" />
        </mxCell>
        <mxCell id="get-all-link-states" value="get_all_link_states()&#xa;获取所有链路状态&#xa;[主要接口]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10;fontStyle=1" parent="interface-group" vertex="1">
          <mxGeometry x="100" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="get-link-state" value="get_link_state()&#xa;获取特定链路状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="interface-group" vertex="1">
          <mxGeometry x="350" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="get-neighbors" value="get_neighbors()&#xa;获取节点邻居" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="interface-group" vertex="1">
          <mxGeometry x="577" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="set-orbital-updater" value="set_orbital_updater()&#xa;设置轨道更新器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="interface-group" vertex="1">
          <mxGeometry x="800" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="compute-group" value="核心物理计算层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="40" y="450" width="1190" height="100" as="geometry" />
        </mxCell>
        <mxCell id="calc-signal-strength" value="calculate_signal_strength_and_snr()&#xa;信号强度和SNR计算&#xa;[自由空间路径损耗+SNR]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10;fontStyle=1" parent="compute-group" vertex="1">
          <mxGeometry x="120" y="40" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="calc-data-rate" value="calculate_data_rate()&#xa;数据速率计算&#xa;[香农公式]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10;fontStyle=1" parent="compute-group" vertex="1">
          <mxGeometry x="400" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="calc-transmission-delay" value="calculate_transmission_delay()&#xa;传输延迟计算&#xa;[传播+传输+处理延迟]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="compute-group" vertex="1">
          <mxGeometry x="690" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="calc-transmission-energy" value="calculate_transmission_energy()&#xa;传输能耗计算&#xa;[功率×时间模型]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="compute-group" vertex="1">
          <mxGeometry x="930" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="main-to-manager" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="main-function" target="communication-manager" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="orbital-to-manager" value="数据依赖" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="orbital-updater" target="communication-manager" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="-80" />
              <mxPoint x="620" y="-80" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="manager-to-main-interface" value="主要接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="communication-manager" target="get-all-link-states" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="290.0769230769233" y="160" as="targetPoint" />
            <Array as="points">
              <mxPoint x="635" y="145" />
              <mxPoint x="290" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-to-get-link" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="get-link-state" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="main-to-neighbors" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="get-neighbors" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="330" />
              <mxPoint x="685" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-to-signal" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="calc-signal-strength" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="420" />
              <mxPoint x="290" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-to-data-rate" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="calc-data-rate" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="310" />
              <mxPoint x="520" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-to-delay" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="calc-transmission-delay" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="360" />
              <mxPoint x="830" y="360" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-to-energy" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-all-link-states" target="calc-transmission-energy" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="400" />
              <mxPoint x="1050" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-group" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-200" y="100" width="200" height="200" as="geometry" />
        </mxCell>
        <mxCell id="legend-solid" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="40" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-dashed" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="65" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-thick" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="90" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-blue" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="115" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-green" value="绿色/紫色：链路类型" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="140" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-orange" value="橙色：核心接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="165" width="140" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
