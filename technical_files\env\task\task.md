# Task.py 上下文工程说明文档

## 文档概述

本文档详细说明 `task.py` 模块在 SPACE-OAAL 仿真环境中的上下文定位、设计理念、实现细节和集成方案，为开发者提供完整的工程实现指导。

---

## 1. 模块定位与上下文关系

### 1.1 在系统架构中的位置

```
SPACE-OAAL 仿真环境架构
├── satellite_env.py (主环境协调器)
├── orbital_updater.py (轨道动力学)
├── communication.py (通信链路)
├── atellite_processed_data1.md (任务生成器)
├── task.py ⭐ (任务实体核心)
└── satellite.py (卫星节点实体)
```

**核心定位**：
- **数据实体层**：定义任务的完整生命周期和状态管理
- **业务逻辑核心**：承载任务调度、优先级计算、资源管理的核心算法
- **接口桥梁**：连接任务生成器和卫星节点的关键纽带

### 1.2 依赖关系分析

**上游依赖**：
- `satellite_processed_data1` → Task实例文件
- `satellite.py` → 管理Task队列和执行

**下游服务**：
- 为 `satellite.py` 提供任务状态管理接口
- 为 `satellite_env.py` 提供任务统计和监控数据
- 为强化学习算法提供状态特征和奖励计算基础

**数据流向**：
```
satellite_processed_data1 → Task创建 → satellite队列 → 状态转换 → 完成统计
     ↓              ↓           ↓           ↓           ↓
   地面需求      任务实例    资源分配    执行监控    性能评估
```
**补充**：'数据结构'


---
# Task.py 理论分析文档

## 1. 模块核心职责定义

### 1.1 基本职责
- **任务生命周期追踪**：记录任务从生成到完成的完整路径
- **处理历史记录**：追踪任务在哪些卫星上处理过，处理了多少
- **重发机制管理**：处理没有卫星接收时的重发逻辑
- **动态优先级计算**：基于时间紧迫性调整任务优先级
- **性能统计汇总**：统计总CPU时间、总能耗、总时延

### 1.2 简化假设
- **资源模型**：任务仅占用CPU计算资源，不考虑内存占用
- **处理模式**：任务可以在多个卫星间传递和部分处理
- **完成条件**：任务处理完成后必须返回地面用户

## 2. 任务状态模型

### 2.1 状态定义
```
GENERATED    → 任务刚生成，等待分配
QUEUED       → 已分配给卫星，在队列中等待
PROCESSING   → 正在某颗卫星上处理
TRANSFERRING → 正在传输到另一颗卫星
RETRYING     → 发送失败，等待重发
RETURNING    → 处理完成，正在返回地面
COMPLETED    → 已返回地面，任务完成
FAILED       → 任务失败（超时或重发次数超限）
```

### 2.2 状态转换规则
```
GENERATED → QUEUED (分配给卫星)
GENERATED → RETRYING (没有卫星接收)

QUEUED → PROCESSING (开始处理)
QUEUED → TRANSFERRING (卸载到其他卫星)

PROCESSING → TRANSFERRING (部分处理后卸载)
PROCESSING → RETURNING (处理完成，返回地面)
PROCESSING → FAILED (处理失败)

TRANSFERRING → QUEUED (成功传输到目标卫星)
TRANSFERRING → FAILED (传输失败)

RETRYING → QUEUED (重发成功)
RETRYING → FAILED (重发次数超限)

RETURNING → COMPLETED (成功返回地面)
RETURNING → FAILED (返回失败)
```

## 3. 处理记录数据结构

### 3.1 单次处理记录
```
ProcessingRecord:
- satellite_id: 处理卫星ID
- start_time: 开始处理时间
- end_time: 结束处理时间  
- cpu_cycles_processed: 已处理的CPU周期数
- energy_consumed: 消耗的能量(焦耳)
- completion_ratio: 完成比例(0-1)
- is_partial: 是否为部分处理
```

### 3.2 传输记录
```
TransferRecord:
- from_satellite_id: 源卫星ID
- to_satellite_id: 目标卫星ID (None表示返回地面)
- transfer_time: 传输时间
- transfer_energy: 传输能耗
- success: 传输是否成功
```

### 3.3 重发记录
```
RetryRecord:
- retry_count: 当前重试次数
- last_retry_time: 最后重试时间
- retry_reason: 重试原因
- max_retries: 最大重试次数
```

## 4. 动态优先级算法理论

### 4.1 优先级计算公式
```
为应对卫星节点面临的多任务并发、资源受限与动态变化的挑战，传统的先进先出（FIFO）排队策略难以满足复杂的任务处理需求。因此，本节构建一个基于动态优先级评分的排队调度模型（Dynamic Priority Scoring based Queuing and Scheduling Model, DPSQ），以实现对不同任务的高效和差异化处理。

首先，我们对进入卫星节点等待队列的任务 Ti 的关键属性进行定义，包括其**静态优先级** P_i、**绝对截止时间** $D_i$、**数据大小** $S_i$、**计算复杂度** $C_i$ 以及任务处理失败的**丢弃惩罚** $W_i$。处理一个任务 $T_i$ 所需的预估总时延 $T_{proc,i}$ 是其通信时延与计算时延之和，即 $T_{proc,i}=\frac{S_i}{B_{link}}+\frac{C_i}{F_{sat}}$，其中 $B_{link}$ 和 $F_{sat}$ 分别代表当前可用的星地链路带宽和卫星的计算能力。

本模型的核心在于为等待队列 $Q$ 中的每个任务 $T_i$ 在当前时刻 $t_{now}$ 计算一个动态优先级分数，调度器依据此分数选择最优任务。其评分函数定义如下：

$Score(T_i,t_{now})=w_p \cdot f_p(P_i)+w_d \cdot f_d(D_i,t_{now})-w_c \cdot f_c(S_i,C_i)$

该函数由三个加权因子构成：

- **优先级因子 $f_p(P_i)=P_i$**: 直接反映任务的业务重要性。
- **紧迫性因子 $f_d(D_i,t_{now})=\frac{1}{(D_i-t_{now})+\epsilon}$**: 反映任务的时效性压力。随着任务接近其截止时间，该项分值会急剧升高，使其获得更高的调度优先级。$\epsilon$ 是一个防止分母为零的极小正常数。
- **成本因子 $f_c(S_i,C_i)=T_{proc,i}$**: 代表处理该任务所需的总时间开销。成本越高的任务，其优先级分数会相应降低，以避免长时间占用宝贵的计算和通信资源。

权重系数 $w_p,w_d,w_c$ 为可根据系统优化目标调整的超参数，用以平衡任务重要性、时效性和执行成本三者之间的关系。

调度流程如下：当卫星计算资源可用时，调度器为队列中所有未超时的任务计算动态优先级分数。它会优先选择得分最高的任务 $T_{best}$。在最终派发执行前，系统会进行一次可行性检查：若预估完成时间 $t_{now}+T_{proc,best}$ 超过其截止时间 $D_{best}$，则判定该任务已无法按时完成，它将被主动丢弃，系统承担其对应的丢弃惩罚 $W_{best}$，并立即重新在剩余任务中选择次优任务。
```


## 5. 重发机制理论设计

### 5.1 重发触发条件
- **无卫星接收**：任务发送后在超时时间内没有卫星确认接收


### 5.2 重发策略
```
- 第1次重发：第一个时隙未被接收
- 第2次重发：第二个时隙未被接收  
- 最大重发次数：2次
- 超过最大次数：任务标记为FAILED
```

## 6. 资源需求计算理论

### 6.1 CPU需求计算
```
总CPU周期数 = 数据大小(bits) × 每bit计算复杂度(cycles/bit)
剩余CPU周期数 = 总CPU周期数 - 已处理CPU周期数
当前处理进度 = 已处理CPU周期数 / 总CPU周期数
```

### 6.2 能耗计算模型
```
处理能耗 = CPU周期数 × 单位周期能耗系数
传输能耗 = 数据大小(bits) × 传输功率 × 传输时间
总能耗 = Σ(各卫星处理能耗) + Σ(各次传输能耗)
```

### 6.3 时延计算模型
```
处理时延 = CPU周期数 / 卫星CPU频率
传输时延 = 数据大小(bits) / 链路带宽 + 传播延迟
排队时延 = 在队列中等待的时间
总时延 = Σ(处理时延) + Σ(传输时延) + Σ(排队时延) + Σ(重发等待时延)
```

## 7. 任务完整生命周期示例

### 7.1 正常处理流程
```
1. GENERATED: 地面用户生成任务
2. QUEUED: 分配给卫星A，进入队列
3. PROCESSING: 卫星A开始处理(处理50%)
4. TRANSFERRING: 卫星A将任务卸载给卫星B
5. QUEUED: 任务在卫星B队列中等待
6. PROCESSING: 卫星B继续处理(处理剩余50%)
7. RETURNING: 卫星B将结果返回地面
8. COMPLETED: 地面用户收到结果
注意：这里写了两颗卫星处理仅作演示，实际可能由任意数量的卫星处理。
```

### 7.2 包含重发的流程
```
1. GENERATED: 地面用户生成任务
2. RETRYING: 发送给卫星A失败，进入重发
3. QUEUED: 重发成功，分配给卫星B
4. PROCESSING: 卫星B处理(处理30%)
5. RETRYING: 卫星B故障，任务重发
6. QUEUED: 重发给卫星C
7. PROCESSING: 卫星C继续处理(处理剩余70%)
8. RETURNING: 返回地面
9. COMPLETED: 完成
```

## 8. 性能统计指标定义

### 8.1 单任务指标
- **总处理时间**：所有卫星处理时间之和
- **总传输时间**：所有传输时间之和
- **总等待时间**：在队列中等待时间之和
- **总重发时间**：重发等待时间之和
- **端到端时延**：从生成到完成的总时间
- **总能耗**：处理能耗 + 传输能耗
- **处理卫星数量**：参与处理的卫星数量
- **重发次数**：总重发次数

## 9. 与其他模块的接口定义

### 9.1 接收task_generator的数据
- 任务ID、类型、数据大小、计算复杂度
- 截止时间、优先级、源地面站ID
- 创建Task对象并初始化状态为GENERATED

### 9.2 向satellite提供的接口
- 获取任务资源需求(CPU周期数)
- 更新任务处理进度
- 记录处理能耗和时间
- 处理任务卸载请求
- 处理任务完成通知

### 9.3 向satellite_env提供的接口
- 获取任务当前状态
- 获取任务统计信息
- 获取系统级性能指标
- 处理任务重发请求

### 9.4 系统级指标评估
- **任务完成率**：完成任务数 / 总任务数
- **平均端到端时延**：所有完成任务的平均时延
- **平均能耗**：所有任务的平均能耗
- **重发率**：发生重发的任务比例
- **卫星利用率**：各卫星的平均CPU利用率
- **负载均衡度**：卫星间负载分布的均匀程度

## 10. 关键设计决策

### 10.1 部分处理支持
- **理由**：卫星资源有限，可能无法一次性完成大任务
- **实现**：记录每个卫星的处理进度，支持任务在多个卫星间传递
- **好处**：提高资源利用率，避免大任务阻塞小任务

### 10.2 重发机制必要性
- **理由**：卫星网络不稳定，传输失败概率较高
- **实现**：指数退避重发，避免网络拥塞
- **好处**：提高任务完成率，增强系统可靠性

### 10.3 动态优先级的意义
- **理由**：静态优先级无法适应时间变化
- **实现**：结合剩余时间和处理进度动态调整
- **好处**：避免任务饿死，提高截止时间遵守率

这个理论分析专注于核心功能设计，去除了复杂的实现细节，为后续编程实现提供清晰的理论指导。
