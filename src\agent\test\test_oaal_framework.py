"""
OAAL框架集成测试
验证框架组件间的正确集成和修复后的功能
"""

import torch
import numpy as np
import unittest
import sys
import os
import tempfile
from unittest.mock import MagicMock, patch

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from oaal_framework import OAALFramework


class TestOAALFrameworkIntegration(unittest.TestCase):
    """OAAL框架集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.framework = OAALFramework(
            num_satellites=6,  # 减少数量用于测试
            num_domains=4,
            hidden_dim=128,
            learning_rate=3e-4,
            device='cpu'
        )
        
    def test_initialization(self):
        """测试框架初始化"""
        # 检查基本属性
        self.assertEqual(self.framework.num_satellites, 6)
        self.assertEqual(self.framework.num_domains, 4)
        
        # 检查组件初始化
        self.assertIsNotNone(self.framework.domain_manager)
        self.assertIsNotNone(self.framework.coordination_manager)
        self.assertEqual(len(self.framework.agents), 6)
        
        # 检查智能体正确初始化
        for agent_id, agent in self.framework.agents.items():
            self.assertEqual(agent.agent_id, agent_id)
            self.assertEqual(agent.obs_dim, 15)
            self.assertEqual(agent.task_feature_dim, 8)
            
        # 检查域管理器正确初始化
        self.assertEqual(len(self.framework.domain_manager.domains), 4)
        
    def test_agent_position_update(self):
        """测试智能体位置更新和域映射"""
        # 创建模拟位置数据
        satellite_positions = {
            0: (45.0, -170.0),  # 纬度, 经度
            1: (30.0, -100.0),
            2: (60.0, 0.0),
            3: (0.0, 120.0)
        }
        
        # 更新位置
        self.framework.update_agent_positions(satellite_positions)
        
        # 检查映射是否创建
        for agent_id in satellite_positions.keys():
            self.assertIn(agent_id, self.framework.agent_domain_mapping)
            domain_id = self.framework.agent_domain_mapping[agent_id]
            self.assertIsInstance(domain_id, int)
            self.assertGreaterEqual(domain_id, 0)
            self.assertLess(domain_id, 4)
            
        # 检查智能体是否被添加到域中
        for agent_id, domain_id in self.framework.agent_domain_mapping.items():
            domain = self.framework.domain_manager.domains[domain_id]
            self.assertIn(agent_id, domain.visiting_agents)
    
    def test_domain_transition(self):
        """测试域转换逻辑"""
        agent_id = 0
        
        # 初始位置
        initial_pos = {agent_id: (45.0, -170.0)}
        self.framework.update_agent_positions(initial_pos)
        initial_domain = self.framework.agent_domain_mapping[agent_id]
        
        # 移动到不同域
        new_pos = {agent_id: (45.0, 50.0)}  # 移动到不同经度
        self.framework.update_agent_positions(new_pos)
        new_domain = self.framework.agent_domain_mapping[agent_id]
        
        # 如果域发生变化，检查转换是否正确
        if new_domain != initial_domain:
            # 检查智能体从旧域移除
            old_domain_obj = self.framework.domain_manager.domains[initial_domain]
            self.assertNotIn(agent_id, old_domain_obj.visiting_agents)
            
            # 检查智能体添加到新域
            new_domain_obj = self.framework.domain_manager.domains[new_domain]
            self.assertIn(agent_id, new_domain_obj.visiting_agents)
    
    def test_action_selection(self):
        """测试动作选择"""
        # 设置智能体位置
        satellite_positions = {0: (45.0, -170.0), 1: (30.0, -100.0)}
        self.framework.update_agent_positions(satellite_positions)
        
        # 创建观测数据
        observations = {
            0: np.random.randn(15),
            1: np.random.randn(15)
        }
        
        # 创建任务队列
        task_queues = {
            0: [
                {'type_id': 1, 'data_size_mb': 10, 'priority': 2, 'deadline_timestamp': 100},
                {'type_id': 2, 'data_size_mb': 5, 'priority': 1, 'deadline_timestamp': 50}
            ],
            1: [
                {'type_id': 0, 'data_size_mb': 20, 'priority': 3, 'deadline_timestamp': 200}
            ]
        }
        
        # 创建有效动作
        valid_actions = {
            0: [[0, 1, 5, 10], [2, 3, 7, 15]],  # 每个任务的有效动作
            1: [[1, 4, 8, 20]]
        }
        
        # 选择动作
        actions = self.framework.select_actions(observations, task_queues, valid_actions)
        
        # 检查结果
        self.assertIn(0, actions)
        self.assertIn(1, actions)
        self.assertEqual(len(actions[0]), 2)  # 智能体0有2个任务
        self.assertEqual(len(actions[1]), 1)  # 智能体1有1个任务
        
        # 检查动作在有效范围内
        for agent_id, agent_actions in actions.items():
            valid_acts = valid_actions[agent_id]
            for i, action in enumerate(agent_actions):
                self.assertIn(action, valid_acts[i])
    
    def test_agent_update(self):
        """测试智能体更新"""
        # 设置智能体位置
        satellite_positions = {0: (45.0, -170.0)}
        self.framework.update_agent_positions(satellite_positions)
        
        # 创建经验数据
        experiences = {
            0: [
                {
                    'observation': np.random.randn(15),
                    'task_features': [np.random.randn(8), np.random.randn(8)],
                    'actions': [1, 5],
                    'reward': 0.5,
                    'next_observation': np.random.randn(15),
                    'done': False,
                    'info': {'log_probs': [0.1, 0.2], 'value': 0.3}
                },
                {
                    'observation': np.random.randn(15),
                    'task_features': [np.random.randn(8)],
                    'actions': [10],
                    'reward': 0.8,
                    'next_observation': np.random.randn(15),
                    'done': True,
                    'info': {'log_probs': [0.15], 'value': 0.4}
                }
            ]
        }
        
        # 记录更新前的参数
        agent = self.framework.agents[0]
        old_params = {name: param.clone() for name, param in agent.actor.named_parameters()}
        
        # 执行更新
        self.framework.update_agents(experiences)
        
        # 检查参数是否发生变化（如果有足够经验）
        if len(agent.memory) >= 32:  # 最小批次大小
            params_changed = False
            for name, param in agent.actor.named_parameters():
                if not torch.equal(old_params[name], param):
                    params_changed = True
                    break
            self.assertTrue(params_changed, "智能体参数应该在更新后发生变化")
    
    def test_domain_update(self):
        """测试域更新"""
        # 设置一些智能体在域中
        satellite_positions = {0: (45.0, -170.0), 1: (45.0, -170.0)}
        self.framework.update_agent_positions(satellite_positions)
        
        # 创建全局统计
        global_stats = {
            0: {
                'arrival_rate': 0.5,
                'avg_priority': 0.8,
                'completion_rate': 0.9
            },
            1: {
                'arrival_rate': 0.3,
                'avg_priority': 0.6,
                'completion_rate': 0.7
            }
        }
        
        # 执行域更新
        current_time = 3600.0  # 1小时
        self.framework.update_domains(current_time, global_stats)
        
        # 检查域特征是否更新
        for domain_id, domain in self.framework.domain_manager.domains.items():
            if domain_id in global_stats:
                stats = global_stats[domain_id]
                self.assertEqual(domain.features.task_arrival_rate, stats['arrival_rate'])
                self.assertEqual(domain.features.avg_task_priority, stats['avg_priority'])
                self.assertEqual(domain.features.task_completion_rate, stats['completion_rate'])
    
    def test_coordination(self):
        """测试协同功能"""
        # 创建卫星状态
        satellite_states = {
            0: {'position': (45.0, -170.0), 'energy': 0.8, 'cpu_load': 0.3},
            1: {'position': (46.0, -169.0), 'energy': 0.6, 'cpu_load': 0.7},
            2: {'position': (44.0, -168.0), 'energy': 0.9, 'cpu_load': 0.2}
        }
        
        # 创建通信链路
        communication_links = [
            {
                'source_id': 0,
                'target_id': 1,
                'distance_km': 1000,
                'data_rate_mbps': 50,
                'delay_ms': 10,
                'energy_cost_j': 0.1,
                'link_quality': 0.8
            },
            {
                'source_id': 1,
                'target_id': 2,
                'distance_km': 1200,
                'data_rate_mbps': 40,
                'delay_ms': 12,
                'energy_cost_j': 0.12,
                'link_quality': 0.7
            }
        ]
        
        # 执行协同
        coordination_decisions = self.framework.coordinate_agents(satellite_states, communication_links)
        
        # 检查结果
        self.assertIsInstance(coordination_decisions, dict)
        # 协同决策的具体内容取决于CoordinationManager的实现
    
    def test_statistics_collection(self):
        """测试统计信息收集"""
        # 添加一些测试指标
        self.framework.add_episode_metrics(0.85, 25.5, 0.78)
        self.framework.add_episode_metrics(0.92, 20.3, 0.82)
        self.framework.add_episode_metrics(0.88, 22.1, 0.80)
        
        # 获取统计信息
        stats = self.framework.get_episode_stats()
        
        # 检查统计计算
        self.assertIn('avg_completion_rate', stats)
        self.assertIn('avg_delay', stats)
        self.assertIn('avg_energy_efficiency', stats)
        
        # 检查计算正确性
        expected_completion = (0.85 + 0.92 + 0.88) / 3
        self.assertAlmostEqual(stats['avg_completion_rate'], expected_completion, places=5)
        
        expected_delay = (25.5 + 20.3 + 22.1) / 3
        self.assertAlmostEqual(stats['avg_delay'], expected_delay, places=5)
    
    def test_framework_status(self):
        """测试框架状态获取"""
        # 设置一些智能体
        satellite_positions = {0: (45.0, -170.0), 1: (30.0, -100.0)}
        self.framework.update_agent_positions(satellite_positions)
        
        # 获取状态
        status = self.framework.get_framework_status()
        
        # 检查状态信息
        self.assertEqual(status['num_satellites'], 6)
        self.assertEqual(status['num_domains'], 4)
        self.assertIn('agent_domain_mapping', status)
        self.assertIn('domain_status', status)
        
        # 检查域状态
        domain_status = status['domain_status']
        self.assertEqual(len(domain_status), 4)
        
        # 检查有访问者的域
        total_visitors = sum(ds['num_visitors'] for ds in domain_status.values())
        self.assertEqual(total_visitors, 2)  # 两个智能体
    
    def test_checkpoint_save_load(self):
        """测试检查点保存和加载"""
        # 设置一些状态
        satellite_positions = {0: (45.0, -170.0)}
        self.framework.update_agent_positions(satellite_positions)
        self.framework.add_episode_metrics(0.85, 25.5, 0.78)
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as f:
            checkpoint_path = f.name
        
        try:
            # 保存检查点
            self.framework.save_checkpoint(checkpoint_path)
            self.assertTrue(os.path.exists(checkpoint_path))
            
            # 创建新框架并加载
            new_framework = OAALFramework(
                num_satellites=6,
                num_domains=4,
                hidden_dim=128
            )
            new_framework.load_checkpoint(checkpoint_path)
            
            # 检查数据是否正确加载
            self.assertEqual(len(new_framework.episode_stats['task_completion_rate']), 1)
            self.assertEqual(new_framework.episode_stats['task_completion_rate'][0], 0.85)
            
        finally:
            # 清理临时文件
            if os.path.exists(checkpoint_path):
                os.unlink(checkpoint_path)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效经验数据
        invalid_experiences = {
            0: [
                {
                    'observation': np.random.randn(15),
                    # 缺少必需字段
                    'reward': 0.5
                }
            ]
        }
        
        # 应该不会崩溃
        self.framework.update_agents(invalid_experiences)
        
        # 测试无效域更新
        invalid_stats = None
        try:
            self.framework.update_domains(0.0, invalid_stats)
        except Exception as e:
            self.fail(f"域更新应该处理无效输入: {e}")


def run_oaal_framework_tests():
    """运行OAAL框架测试"""
    print("=== OAAL框架集成测试 ===\n")
    
    test_classes = [TestOAALFrameworkIntegration]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    
    for test_class in test_classes:
        print(f"运行 {test_class.__name__} 测试...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        total_failures += len(result.failures)
        total_errors += len(result.errors)
        
        print(f"{test_class.__name__}: {result.testsRun}个测试, {len(result.failures)}个失败, {len(result.errors)}个错误\n")
    
    # 总结
    print("=== 框架测试总结 ===")
    print(f"总测试数: {total_tests}")
    print(f"成功: {total_tests - total_failures - total_errors}")
    print(f"失败: {total_failures}")
    print(f"错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ 所有框架集成测试通过！")
        print("✅ OAAL框架修复成功，组件集成正确！")
    else:
        print("❌ 存在框架测试失败或错误")
    
    return total_failures == 0 and total_errors == 0


if __name__ == "__main__":
    run_oaal_framework_tests()