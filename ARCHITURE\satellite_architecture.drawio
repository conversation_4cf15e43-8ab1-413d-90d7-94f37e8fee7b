<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="Satellite Architecture" id="satellite-architecture">
    <mxGraphModel dx="1507" dy="736" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="main-function" value="main()&lt;br&gt;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="740" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="data-group" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="200" y="1580" width="1200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件&#xa;- 轨道参数&#xa;- 能量参数&#xa;- 通信参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
          <mxGeometry x="50" y="35" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="orbital-data" value="orbital_updater数据&#xa;轨道状态同步" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
          <mxGeometry x="300" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="communication-data" value="communication数据&#xa;网络状态同步" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
          <mxGeometry x="520" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="task-data" value="task数据&#xa;任务输入" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
          <mxGeometry x="740" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="task-generation-results" value="task_generation_results.json&#xa;任务生成结果" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
          <mxGeometry x="960" y="40" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="controller-group" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="450" y="150" width="700" height="140" as="geometry" />
        </mxCell>
        <mxCell id="satellite-node" value="SatelliteNode&#xa;卫星节点核心类&#xa;- 状态管理&#xa;- 任务调度&#xa;- 资源协调" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="controller-group" vertex="1">
          <mxGeometry x="250" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="states-group" value="卫星状态数据层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="120" y="350" width="1400" height="200" as="geometry" />
        </mxCell>
        <mxCell id="position-state" value="Position&#xa;位置状态&#xa;- latitude/longitude&#xa;- altitude&#xa;- timestamp&#xa;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="50" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="energy-state" value="EnergyState&#xa;能量状态&#xa;- current_battery_j&#xa;- battery_capacity_j&#xa;- solar_power_w&#xa;- base_power_consumption_w&#xa;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="250" y="40" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="resource-state" value="ResourceState&#xa;计算资源状态&#xa;- cpu_frequency_hz&#xa;- available_cpu_hz&#xa;- memory_total_mb&#xa;- memory_used_mb" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="480" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="communication-state" value="CommunicationState&#xa;通信状态&#xa;- visible_neighbors&#xa;- visible_ground_stations&#xa;- active_links" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="680" y="50" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="performance-metrics" value="PerformanceMetrics&#xa;性能指标&#xa;- total_tasks_received&#xa;- total_tasks_completed&#xa;- total_tasks_failed&#xa;- completion_rate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="900" y="40" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="satellite-status" value="SatelliteStatus&#xa;卫星状态枚举&#xa;- ACTIVE&#xa;- INACTIVE&#xa;- FAILED&#xa;- MAINTENANCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
          <mxGeometry x="1130" y="50" width="140" height="100" as="geometry" />
        </mxCell>
        <mxCell id="task-management-group" value="任务管理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="200" y="600" width="1200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="receive-task" value="receive_task()&#xa;接收新任务&#xa;- 队列容量检查&#xa;- 卫星状态检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="schedule-next-task" value="schedule_next_task()&#xa;调度下一个任务&#xa;- 优先级调度&#xa;- 超时检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
          <mxGeometry x="250" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="update-processing" value="update_processing()&#xa;更新任务处理&#xa;- 处理进度&#xa;- 完成检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
          <mxGeometry x="450" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="offload-task" value="offload_task()&#xa;任务卸载&#xa;- 邻居检查&#xa;- 传输成本计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
          <mxGeometry x="650" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="get-best-offload-target" value="get_best_offload_target()&#xa;选择最佳卸载目标&#xa;- 邻居选择&#xa;- 负载评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
          <mxGeometry x="850" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="state-update-group" value="状态更新层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="200" y="780" width="1200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="update-energy" value="update_energy()&#xa;能量更新&#xa;- 太阳能充电&#xa;- 基础功耗&#xa;- 处理功耗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="update-connectivity" value="update_connectivity()&#xa;连接状态更新&#xa;- 邻居列表&#xa;- 地面站列表" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
          <mxGeometry x="250" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="step" value="step()&#xa;时间步执行&#xa;- 状态更新&#xa;- 任务调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
          <mxGeometry x="450" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="sync-with-orbital" value="sync_with_orbital_state()&#xa;轨道状态同步&#xa;- 位置同步&#xa;- 光照状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
          <mxGeometry x="650" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="sync-with-communication" value="sync_with_communication_state()&#xa;网络状态同步&#xa;- 邻居更新&#xa;- 连接状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
          <mxGeometry x="880" y="40" width="190" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export-group" value="数据导出层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="300" y="960" width="1000" height="120" as="geometry" />
        </mxCell>
        <mxCell id="get-status-summary" value="get_status_summary()&#xa;状态摘要&#xa;- 位置信息&#xa;- 能量状态&#xa;- 任务统计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export-for-scheduling" value="export_for_scheduling()&#xa;调度数据导出&#xa;- 任务接收能力&#xa;- 资源状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
          <mxGeometry x="250" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="export-observation-data" value="export_observation_data()&#xa;观测数据导出&#xa;- 强化学习观测&#xa;- 完整状态数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
          <mxGeometry x="460" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="is-healthy" value="is_healthy()&#xa;健康检查&#xa;- 状态检查&#xa;- 电量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
          <mxGeometry x="680" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="can-accept-tasks" value="can_accept_tasks()&#xa;任务接收检查&#xa;- 健康状态&#xa;- 队列容量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
          <mxGeometry x="860" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="utility-group" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="300" y="1140" width="1000" height="120" as="geometry" />
        </mxCell>
        <mxCell id="load-config" value="_load_config()&#xa;配置加载&#xa;- 配置文件读取&#xa;- 默认配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
          <mxGeometry x="50" y="40" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="get-default-config" value="_get_default_config()&#xa;默认配置获取&#xa;- 系统参数&#xa;- 计算参数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
          <mxGeometry x="240" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="initialize-from-config" value="_initialize_from_config()&#xa;配置初始化&#xa;- 参数设置&#xa;- 状态初始化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
          <mxGeometry x="440" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="complete-current-task" value="_complete_current_task()&#xa;任务完成处理&#xa;- 统计更新&#xa;- 状态清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
          <mxGeometry x="650" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="fail-current-task" value="_fail_current_task()&#xa;任务失败处理&#xa;- 失败统计&#xa;- 状态清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
          <mxGeometry x="850" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="external-interface-group" value="外部模块接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="400" y="1320" width="800" height="120" as="geometry" />
        </mxCell>
        <mxCell id="set-external-modules" value="set_external_modules()&#xa;设置外部模块&#xa;- OrbitalUpdater接口&#xa;- CommunicationManager接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
          <mxGeometry x="50" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="sync-with-task-state" value="sync_with_task_state()&#xa;任务状态同步&#xa;- 新任务接收&#xa;- 任务状态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
          <mxGeometry x="280" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="orbital-updater-ref" value="orbital_updater&#xa;轨道更新器引用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
          <mxGeometry x="500" y="50" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="comm-manager-ref" value="comm_manager&#xa;通信管理器引用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
          <mxGeometry x="670" y="50" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="main-to-satellite" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="main-function" target="satellite-node" edge="1">
          <mxGeometry x="-0.5556" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="satellite-to-position" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="position-state" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="satellite-to-energy" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="energy-state" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="310" />
              <mxPoint x="455" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="satellite-to-resource" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="resource-state" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="receive-to-schedule" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="receive-task" target="schedule-next-task" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="schedule-to-processing" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="schedule-next-task" target="update-processing" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="processing-to-offload" value="Offload decision" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="update-processing" target="offload-task" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="step-to-energy" value="State update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="step" target="update-energy" edge="1">
          <mxGeometry x="-0.2" y="15" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="725" y="930" />
              <mxPoint x="360" y="930" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step-to-processing-update" value="Processing update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="step" target="update-processing" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="725" y="765" />
              <mxPoint x="680" y="765" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="orbital-data-to-sync" value="Orbital data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="orbital-data" target="sync-with-orbital" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="580" y="1520" />
              <mxPoint x="935" y="1520" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="comm-data-to-sync" value="Communication data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="communication-data" target="sync-with-communication" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="1550" />
              <mxPoint x="1500" y="1550" />
              <mxPoint x="1500" y="855" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="task-data-to-receive" value="Task input" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="task-data" target="receive-task" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1020" y="1770" />
              <mxPoint x="140" y="1770" />
              <mxPoint x="140" y="675" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="config-to-load" value="Config read" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="config-yaml" target="load-config" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="270" y="1215" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="load-to-initialize" value="Initialize" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="load-config" target="initialize-from-config" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="420" y="1120" />
              <mxPoint x="820" y="1120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="main-data-flow" value="Main data flow&#xa;(Time step loop)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="satellite-node" target="step" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="760" y="380" />
              <mxPoint x="760" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-group" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="20" y="40" width="200" height="220" as="geometry" />
        </mxCell>
        <mxCell id="legend-solid" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="40" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-dashed" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="65" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-thick" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="90" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-blue" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="115" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-green" value="绿色：数据状态" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="140" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-orange" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="165" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-purple" value="紫色：外部接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
          <mxGeometry x="10" y="190" width="120" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
