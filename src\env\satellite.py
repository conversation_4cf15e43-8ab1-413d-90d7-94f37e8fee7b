#!/usr/bin/env python3
"""
Satellite.py - SPACE-OAAL 卫星实体核心模块

基于satellite_processed_data1.csv轨道数据和task_generation_results.json任务数据
实现基础卫星实体功能，支持MVP环境仿真需求。

Author: SPACE-OAAL Team
Date: 2025-07-27
"""

import pandas as pd
import numpy as np
import yaml
import os
import time
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

# 导入已有模块
try:
    from .task import Task, TaskState
    from .orbital_updater import OrbitalUpdater
    from .communication import CommunicationManager
except ImportError:
    from task import Task, TaskState
    from orbital_updater import OrbitalUpdater
    from communication import CommunicationManager


class SatelliteStatus(Enum):
    """卫星状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive" 
    FAILED = "failed"
    MAINTENANCE = "maintenance"


@dataclass
class Position:
    """卫星位置状态"""
    latitude: float = 0.0
    longitude: float = 0.0
    altitude: float = 0.0
    timestamp: float = 0.0
    is_illuminated: bool = False


@dataclass 
class EnergyState:
    """卫星能量状态"""
    current_battery_j: float = 0.0
    battery_capacity_j: float = 0.0
    solar_power_w: float = 0.0
    base_power_consumption_w: float = 50.0  # 基础功耗
    is_illuminated: bool = False
    
    @property
    def battery_ratio(self) -> float:
        """电池电量比例"""
        if self.battery_capacity_j == 0:
            return 0.0
        return self.current_battery_j / self.battery_capacity_j
    
    @property
    def is_low_energy(self) -> bool:
        """是否低电量（小于20%）"""
        return self.battery_ratio < 0.2


@dataclass
class ResourceState:
    """卫星计算资源状态 - 简化为仅CPU管理"""
    cpu_frequency_hz: float = 0.0
    total_cpu_capacity: int = 100          # 总CPU容量（100%）
    available_cpu_capacity: int = 100      # 可用CPU容量（0-100%）
    
    # CPU分配槽位（5个并行任务槽）
    cpu_slots: List[Optional[str]] = None  # [task_id1, task_id2, None, None, None]
    cpu_allocations: Dict[str, int] = None # {task_id: cpu_percentage}
    
    # 保留原有字段以保持兼容性
    available_cpu_hz: float = 0.0
    memory_total_mb: float = 1000.0
    memory_used_mb: float = 0.0
    
    def __post_init__(self):
        if self.cpu_slots is None:
            self.cpu_slots = [None] * 5  # 5个任务槽
        if self.cpu_allocations is None:
            self.cpu_allocations = {}
    
    @property
    def cpu_utilization(self) -> float:
        """CPU利用率"""
        return (100 - self.available_cpu_capacity) / 100.0
    
    @property 
    def active_task_count(self) -> int:
        """当前活跃任务数量"""
        return len([slot for slot in self.cpu_slots if slot is not None])
    
    @property
    def available_slots(self) -> List[int]:
        """可用的任务槽位索引"""
        return [i for i, slot in enumerate(self.cpu_slots) if slot is None]
    
    @property
    def memory_utilization(self) -> float:
        """内存利用率（保留兼容性）"""
        if self.memory_total_mb == 0:
            return 0.0
        return self.memory_used_mb / self.memory_total_mb


@dataclass
class CommunicationState:
    """卫星通信状态"""
    visible_neighbors: List[str] = None
    visible_ground_stations: List[str] = None
    active_links: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.visible_neighbors is None:
            self.visible_neighbors = []
        if self.visible_ground_stations is None:
            self.visible_ground_stations = []
        if self.active_links is None:
            self.active_links = {}
    
    @property
    def neighbor_count(self) -> int:
        """邻居卫星数量"""
        return len(self.visible_neighbors)
    
    @property
    def ground_station_count(self) -> int:
        """可见地面站数量"""
        return len(self.visible_ground_stations)


@dataclass
class PerformanceMetrics:
    """卫星性能指标 - 修复：添加步长增量指标用于奖励计算"""
    # 累计指标（用于统计）
    total_tasks_received: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_energy_consumed_j: float = 0.0
    total_processing_time_s: float = 0.0
    
    # 修复：添加步长增量指标（用于RL奖励）
    step_tasks_completed: int = 0    # 当前步完成的任务数
    step_tasks_failed: int = 0       # 当前步失败的任务数
    step_energy_consumed_j: float = 0.0  # 当前步消耗的能量
    
    def reset_step_metrics(self):
        """重置步长指标 - 在每个时间步开始时调用"""
        self.step_tasks_completed = 0
        self.step_tasks_failed = 0
        self.step_energy_consumed_j = 0.0
    
    @property
    def completion_rate(self) -> float:
        """任务完成率"""
        if self.total_tasks_received == 0:
            return 0.0
        return self.total_tasks_completed / self.total_tasks_received
    
    @property
    def failure_rate(self) -> float:
        """任务失败率"""
        if self.total_tasks_received == 0:
            return 0.0
        return self.total_tasks_failed / self.total_tasks_received


class SatelliteNode:
    """
    卫星节点核心类
    
    基于satellite_processed_data1.csv提供的轨道数据实现卫星实体，
    支持基础的状态管理、任务队列、能量管理等功能。
    """
    
    def __init__(self, satellite_id: str, config: Dict):
        """
        初始化卫星节点 - 修复：强制依赖注入，移除配置加载职责
        
        Args:
            satellite_id: 卫星ID (如 "Satellite111")
            config: 配置字典（必需参数，不能为None）
        """
        # 验证config参数
        if config is None:
            raise ValueError("config参数不能为None，必须由外部注入")
        
        self.satellite_id = satellite_id
        self.config = config
        
        # 初始化状态
        self.status = SatelliteStatus.ACTIVE
        self.position = Position()
        self.energy_state = EnergyState()
        self.resource_state = ResourceState()
        self.communication_state = CommunicationState()
        self.performance_metrics = PerformanceMetrics()
        
        # 任务队列管理
        self.task_queue: List[Task] = []
        self.max_queue_size: int = 100
        # 修复：移除已弃用的单任务处理变量
        # self.current_processing_task 和 self.processing_start_time 已移除
        
        # 新增：多任务并行处理
        self.running_tasks: Dict[str, Task] = {}           # 正在运行的任务 {task_id: task}
        self.task_start_times: Dict[str, float] = {}       # 任务开始时间
        self.max_parallel_tasks: int = 5                   # 最大并行任务数
        
        # CPU分配的离散档位 (10%, 20%, 30%, 40%, 50%, 60%, 70%, 80%, 90%, 100%)
        self.cpu_allocation_levels = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        # 时间管理
        self.current_timeslot: int = 0
        self.current_time: float = 0.0
        
        # 外部模块接口
        self.orbital_updater: Optional[OrbitalUpdater] = None
        self.comm_manager: Optional[CommunicationManager] = None
        
        # 从配置初始化参数
        self._initialize_from_config()
    
    # 修复：移除_load_config和_get_default_config方法
    # 配置由外部统一管理并注入，不再SatelliteNode的职责
    
    def _initialize_from_config(self):
        """从配置文件初始化卫星参数"""
        # 位置初始化
        self.position.altitude = float(self.config['system']['leo_altitude_m']) / 1000.0  # 转换为km
        
        # 能量状态初始化
        battery_capacity = float(self.config['computation']['leo_battery_capacity_j'])
        self.energy_state.battery_capacity_j = battery_capacity
        self.energy_state.current_battery_j = battery_capacity  # 初始满电
        self.energy_state.solar_power_w = float(self.config['computation']['leo_solar_power_w'])
        self.energy_state.is_illuminated = True  # 初始假设光照
        
        # 调试信息：验证能量初始化
        if self.satellite_id in ['Satellite111', 'Satellite112', 'Satellite113']:
            # 移除能量初始化DEBUG输出
            pass
        
        # 计算资源初始化
        self.resource_state.cpu_frequency_hz = float(self.config['computation']['f_leo_hz'])
        self.resource_state.available_cpu_hz = self.resource_state.cpu_frequency_hz
    
    def set_external_modules(self, orbital_updater: OrbitalUpdater = None, 
                           comm_manager: CommunicationManager = None):
        """设置外部模块引用"""
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
    
    def get_available_cpu_slots(self) -> List[int]:
        """获取可用的CPU槽位"""
        return self.resource_state.available_slots
    
    def allocate_cpu_to_task(self, task_id: str, cpu_percentage: int) -> bool:
        """为任务分配CPU资源"""
        # 检查CPU档位是否有效
        if cpu_percentage not in self.cpu_allocation_levels:
            return False
        
        # 检查是否有足够的CPU容量
        if self.resource_state.available_cpu_capacity < cpu_percentage:
            return False
        
        # 检查是否有可用槽位
        available_slots = self.get_available_cpu_slots()
        if not available_slots:
            return False
        
        # 分配资源
        slot_index = available_slots[0]
        self.resource_state.cpu_slots[slot_index] = task_id
        self.resource_state.cpu_allocations[task_id] = cpu_percentage
        self.resource_state.available_cpu_capacity -= cpu_percentage
        
        return True
    
    def deallocate_cpu_from_task(self, task_id: str) -> bool:
        """释放任务的CPU资源"""
        if task_id not in self.resource_state.cpu_allocations:
            return False
        
        # 释放CPU容量
        cpu_percentage = self.resource_state.cpu_allocations[task_id]
        self.resource_state.available_cpu_capacity += cpu_percentage
        
        # 清空槽位
        for i, slot_task_id in enumerate(self.resource_state.cpu_slots):
            if slot_task_id == task_id:
                self.resource_state.cpu_slots[i] = None
                break
        
        # 清除分配记录
        del self.resource_state.cpu_allocations[task_id]
        return True
    
    def start_task_processing(self, task: Task, cpu_percentage: int) -> bool:
        """开始处理任务（并行模式）"""
        task_id = task.task_id
        
        # 检查是否已在处理
        if task_id in self.running_tasks:
            return False
        
        # 分配CPU资源
        if not self.allocate_cpu_to_task(task_id, cpu_percentage):
            return False
        
        # 开始处理
        self.running_tasks[task_id] = task
        self.task_start_times[task_id] = self.current_time
        
        # 更新任务状态 - 修复：传递current_time参数
        if hasattr(task, 'start_processing'):
            task.start_processing(self.satellite_id, self.current_time)
        
        # 只为Satellite0记录任务开始
        if self.satellite_id == 0:
            print(f"[监控] Satellite0 开始Task{task_id}")
        
        return True
    
    def complete_task_processing(self, task_id: str) -> bool:
        """完成任务处理"""
        if task_id not in self.running_tasks:
            return False
        
        task = self.running_tasks[task_id]
        
        # 完成任务 - 修复：传递current_time参数
        if hasattr(task, 'complete_processing'):
            task.complete_processing(self.current_time, False)
        
        # 释放资源
        released_cpu = self.deallocate_cpu_from_task(task_id)
        
        # 清理运行记录
        del self.running_tasks[task_id]
        if task_id in self.task_start_times:
            del self.task_start_times[task_id]
        
        # 更新统计 - 修复：同时更新步长指标
        self.performance_metrics.total_tasks_completed += 1
        self.performance_metrics.step_tasks_completed += 1
        
        # 只监控Satellite0的任务完成
        if self.satellite_id == 0:
            print(f"[监控] Satellite0 完成Task{task_id}, 总完成数: {self.performance_metrics.total_tasks_completed}")
        
        return True
    
    def update_energy(self, time_delta: float):
        """
        更新能量状态 - 修复：移除冲突的任务处理能耗计算
        
        Args:
            time_delta: 时间间隔（秒）
        """
        # 太阳能充电
        if self.energy_state.is_illuminated:
            charge_energy = self.energy_state.solar_power_w * time_delta
            self.energy_state.current_battery_j = min(
                self.energy_state.current_battery_j + charge_energy,
                self.energy_state.battery_capacity_j
            )
        
        # 基础功耗
        base_consumption = self.energy_state.base_power_consumption_w * time_delta
        self.energy_state.current_battery_j = max(
            self.energy_state.current_battery_j - base_consumption, 0.0
        )
        
        # 修复：移除与current_processing_task相关的冲突能耗计算
        # 所有任务处理能耗现在统一在update_running_tasks中计算
        
        # 检查电量状态
        if self.energy_state.current_battery_j <= 0:
            self.status = SatelliteStatus.FAILED
            print(f"Warning: {self.satellite_id} 电量耗尽")
    
    # 修复：移除已弃用的update_processing方法
    # 所有任务处理现在统一使用update_running_tasks()进行并行处理
    
    def update_connectivity(self, visibility_info: Dict):
        """
        更新连接状态
        
        Args:
            visibility_info: 可见性信息字典
        """
        self.communication_state.visible_neighbors = visibility_info.get('neighbors', [])
        self.communication_state.visible_ground_stations = visibility_info.get('ground_stations', [])
        
        # 更新活跃链路信息（如果有通信管理器）
        if self.comm_manager:
            self.communication_state.active_links = {}
            for neighbor in self.communication_state.visible_neighbors:
                try:
                    link_state = self.comm_manager.get_link_state(
                        self.satellite_id, neighbor, self.current_timeslot
                    )
                    if link_state:
                        self.communication_state.active_links[neighbor] = link_state
                except (AttributeError, KeyError, TypeError) as e:
                    import logging
                    logging.debug(f"Link state unavailable for {neighbor}: {e}")
                    pass  # 链路不可用，跳过
    
    def sort_task_queue_by_priority(self):
        """
        按优先级对任务队列进行排序 - 修复：确保动作应用到最高优先级任务
        
        优先级排序规则：
        1. 按动态优先级降序排列（优先级值越高越优先）
        2. 同优先级按紧急度降序排列
        3. 同紧急度按到达时间升序排列（先到先服务）
        """
        if not self.task_queue:
            return
        
        self.task_queue.sort(key=lambda task: (
            -task.get_dynamic_priority(self.current_time),  # 动态优先级降序
            -getattr(task, 'requirements_urgency', 1.0),    # 紧急度降序  
            getattr(task, 'arrival_time', 0.0)              # 到达时间升序
        ))
    
    def receive_task(self, task: Task) -> bool:
        """
        接收新任务
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 是否成功接收
        """
        task_id = getattr(task, 'task_id', 'unknown')
        
        if len(self.task_queue) >= self.max_queue_size:
            # 只为Satellite0记录队列满的情况
            if self.satellite_id == 0:
                print(f"[监控] Satellite0 队列满，拒绝Task{task_id}")
            return False
        
        if self.status != SatelliteStatus.ACTIVE:
            # 移除非活跃卫星拒绝任务的DEBUG输出
            pass
            return False
        
        # 添加到队列
        self.task_queue.append(task)
        
        # 更新任务状态 - 修复：传递current_time参数（暂时使用self.current_time，理想情况应由外部传入）
        if hasattr(task, 'update_state'):
            task.update_state(TaskState.QUEUED, self.satellite_id, self.current_time)
        
        # 更新统计
        self.performance_metrics.total_tasks_received += 1
        
        # 只为Satellite0记录任务接收
        if self.satellite_id == 0:
            print(f"[监控] Satellite0 接收Task{task_id}, 队列长度: {len(self.task_queue)}")
        
        return True
    
    def schedule_parallel_tasks(self) -> int:
        """调度多个任务并行处理"""
        scheduled_count = 0
        
        # 检查是否有空闲槽位
        available_slots = self.get_available_cpu_slots()
        if not available_slots:
            return 0
        
        # 从队列中选择任务进行调度
        eligible_tasks = []
        for task in self.task_queue:
            if (hasattr(task, 'deadline_timestamp') and hasattr(task, 'priority') and
                task.deadline_timestamp > self.current_time):
                eligible_tasks.append(task)
        
        if not eligible_tasks:
            return 0
        
        # 按优先级排序
        eligible_tasks.sort(key=lambda t: getattr(t, 'priority', 0), reverse=True)
        
        # 为每个可用槽位分配任务
        for slot_idx in available_slots:
            if not eligible_tasks:
                break
            
            task = eligible_tasks.pop(0)
            
            # 计算CPU分配：根据任务复杂度和剩余容量决定
            cpu_allocation = self._calculate_cpu_allocation(task)
            
            # 尝试开始任务处理
            if self.start_task_processing(task, cpu_allocation):
                self.task_queue.remove(task)
                scheduled_count += 1
        
        return scheduled_count
    
    def _calculate_cpu_allocation(self, task: Task) -> int:
        """计算任务的CPU分配百分比"""
        # 简单策略：根据任务复杂度和剩余CPU容量分配
        available_cpu = self.resource_state.available_cpu_capacity
        
        # 基于任务数据大小估算需要的CPU
        if hasattr(task, 'data_size_mb'):
            if task.data_size_mb > 50:  # 大任务
                preferred_cpu = 40
            elif task.data_size_mb > 20:  # 中任务  
                preferred_cpu = 30
            else:  # 小任务
                preferred_cpu = 20
        else:
            preferred_cpu = 20
        
        # 确保分配的CPU不超过可用量，并且在有效档位内
        max_allocatable = min(available_cpu, 100)
        valid_levels = [level for level in self.cpu_allocation_levels if level <= max_allocatable]
        
        if not valid_levels:
            return 10  # 最小分配
        
        # 选择最接近期望值的档位
        optimal_level = min(valid_levels, key=lambda x: abs(x - preferred_cpu))
        return optimal_level
    
    def update_running_tasks(self, time_delta: float, current_time: float):
        """更新所有运行中的任务 - 修复：统一物理能耗模型和进度计算"""
        completed_tasks = []
        total_task_energy_consumed = 0.0  # 统计本时间步的总能耗
        
        # 创建运行任务的副本来避免迭代时修改字典的错误
        running_tasks_copy = dict(self.running_tasks)
        
        for task_id, task in running_tasks_copy.items():
            # 检查任务是否仍在运行（可能已被其他地方移除）
            if task_id not in self.running_tasks:
                continue
                
            if task_id not in self.resource_state.cpu_allocations:
                continue
            
            # 获取分配的CPU百分比
            cpu_percentage = self.resource_state.cpu_allocations[task_id]
            
            # 计算实际可用的CPU周期数
            cpu_ratio = cpu_percentage / 100.0
            available_cycles = self.resource_state.cpu_frequency_hz * cpu_ratio * time_delta
            
            # 修复：计算实际能处理的周期数（考虑任务剩余需求）
            if hasattr(task, 'get_remaining_cycles'):
                remaining_cycles = task.get_remaining_cycles()
                actual_processed_cycles = min(int(available_cycles), remaining_cycles)
            else:
                actual_processed_cycles = int(available_cycles)
            
            # 修复：基于物理模型的能耗计算（使用能效系数）
            # P = ζ * f * C, 其中ζ是能效系数，f是频率，C是实际处理的周期数
            if hasattr(self.config, 'get') and 'computation' in self.config:
                zeta_leo = self.config['computation'].get('zeta_leo', 1.0e-28)
            else:
                zeta_leo = 1.0e-28  # 默认值
            
            frequency_used = self.resource_state.cpu_frequency_hz * cpu_ratio
            energy_consumed = zeta_leo * frequency_used * actual_processed_cycles * time_delta
            
            # 更新任务进度 - 修复：传递正确的仿真时间
            if hasattr(task, 'update_processing_progress'):
                task.update_processing_progress(
                    actual_processed_cycles, energy_consumed, current_time
                )
            
            # 统计能耗
            total_task_energy_consumed += energy_consumed
            
            # 检查任务是否完成
            if hasattr(task, 'is_completed') and task.is_completed():
                completed_tasks.append(task_id)
            # 检查任务是否超时
            elif (hasattr(task, 'deadline_timestamp') and 
                  current_time > task.deadline_timestamp):
                self._fail_task(task_id, "timeout", current_time)
                completed_tasks.append(task_id)
        
        # 修复：统一更新卫星能耗
        self.energy_state.current_battery_j = max(
            self.energy_state.current_battery_j - total_task_energy_consumed, 0.0
        )
        self.performance_metrics.total_energy_consumed_j += total_task_energy_consumed
        
        # 完成已结束的任务
        for task_id in completed_tasks:
            if task_id in self.running_tasks:  # 再次检查确保任务仍存在
                self.complete_task_processing(task_id)
    
    def _fail_task(self, task_id: str, reason: str, current_time: float):
        """任务处理失败 - 修复：传递仿真时间参数"""
        if task_id not in self.running_tasks:
            return
        
        task = self.running_tasks[task_id]
        
        # 更新任务状态 - 修复：传递current_time参数
        if hasattr(task, 'update_state'):
            from .task import TaskState
            task.update_state(TaskState.FAILED, None, current_time)
        
        # 释放资源
        self.deallocate_cpu_from_task(task_id)
        
        # 清理运行记录
        del self.running_tasks[task_id]
        if task_id in self.task_start_times:
            del self.task_start_times[task_id]
        
        # 更新统计 - 修复：同时更新步长指标
        self.performance_metrics.total_tasks_failed += 1
        self.performance_metrics.step_tasks_failed += 1
    
    def schedule_next_task(self) -> bool:
        """
        保留原有接口以保持兼容性 - 现在调用并行调度
        """
        return self.schedule_parallel_tasks() > 0
    
    # 修复：移除已弃用的_complete_current_task方法
    # 任务完成现在统一使用complete_task_processing()处理
    
    def _initiate_result_return(self, task):
        """启动结果返回流程（基础版本：直接返回给地面用户）"""
        try:
            # 获取任务结果信息
            result_info = task.get_result_info()
            if not result_info:
                return
            
            # 基础版本：模拟直接通信回传
            source_location_id = result_info['source_location_id']
            
            # 检查是否与源地面站可见
            if (hasattr(self, 'communication_state') and
                hasattr(self.communication_state, 'visible_ground_stations')):
                
                if str(source_location_id) in self.communication_state.visible_ground_stations:
                    # 直接返回给地面用户
                    success = self._return_to_ground_station(task, source_location_id)
                    if success:
                        return
            
            # 如果不能直接返回，尝试通过其他卫星中继（基础版本：简化处理）
            self._attempt_relay_return(task, source_location_id)
            
        except (AttributeError, KeyError, TypeError) as e:
            import logging
            logging.error(f"Failed to initiate result return for {task.task_id}: {e}")
            # 返回失败，标记任务为失败
            if hasattr(task, 'update_state'):
                task.update_state(TaskState.FAILED)
    
    def _return_to_ground_station(self, task, ground_station_id: int) -> bool:
        """直接返回结果给地面站（使用精确物理模型）"""
        try:
            # 获取任务结果信息
            result_info = task.get_result_info()
            result_size_mb = result_info['result_size_mb']
            
            # 使用CommunicationManager计算精确的传输时间和能耗
            if self.comm_manager:
                try:
                    # 获取卫星到地面站的链路状态
                    link_state = self.comm_manager.get_link_state(
                        self.satellite_id, str(ground_station_id), self.current_timeslot
                    )
                    
                    if link_state:
                        # 使用精确的物理模型计算传输时间和能耗
                        distance_km = link_state['distance_km']
                        data_rate_mbps = link_state['data_rate_mbps']
                        
                        # 使用CommunicationManager的精确计算方法
                        transmission_time_ms = self.comm_manager.calculate_transmission_delay(
                            distance_km, result_size_mb, data_rate_mbps
                        )
                        transmission_time_s = transmission_time_ms / 1000.0  # 转换为秒
                        
                        transmission_energy = self.comm_manager.calculate_transmission_energy(
                            distance_km, result_size_mb, 
                            self.comm_manager.satellite_to_user_power, data_rate_mbps
                        )
                    else:
                        # 链路状态不可用 - 任务无法完成，直接失败
                        import logging
                        logging.error(f"Link state unavailable for {self.satellite_id} -> {ground_station_id}, transmission impossible")
                        return False
                        
                except (AttributeError, KeyError, TypeError) as e:
                    import logging
                    logging.error(f"Failed to get precise link calculation: {e}, transmission failed")
                    return False
            else:
                # 没有通信管理器 - 系统配置错误，无法进行传输
                import logging
                logging.error(f"No CommunicationManager available, transmission impossible")
                return False
            
            # 检查是否有足够能量进行传输
            if self.energy_state.current_battery_j < transmission_energy:
                import logging
                logging.warning(f"Insufficient energy for transmission: need {transmission_energy}J, have {self.energy_state.current_battery_j}J")
                return False
            
            # 消耗能量
            self.energy_state.current_battery_j = max(
                self.energy_state.current_battery_j - transmission_energy, 0.0
            )
            
            # 更新能耗统计
            self.performance_metrics.total_energy_consumed_j += transmission_energy
            self.performance_metrics.step_energy_consumed_j += transmission_energy
            
            # 确认返回完成
            return_time = self.current_time + transmission_time_s
            success = task.confirm_return_to_ground(ground_station_id, return_time)
            
            return success
            
        except (AttributeError, KeyError, TypeError) as e:
            import logging
            logging.error(f"Failed to return to ground station {ground_station_id}: {e}")
            return False
    
    def _attempt_relay_return(self, task, ground_station_id: int):
        """尝试通过中继返回（基础版本：简化处理）"""
        # 基础版本：如果不能直接返回，标记为失败
        # 未来版本可以实现更复杂的中继逻辑
        if hasattr(task, 'update_state'):
            task.update_state(TaskState.FAILED)
    
    # 修复：移除已弃用的_fail_current_task方法
    # 任务失败现在统一使用_fail_task()处理
    
    def sync_with_orbital_state(self, orbital_satellite):
        """
        与orbital_updater.py同步轨道状态
        
        Args:
            orbital_satellite: 来自orbital_updater的卫星对象
        """
        # 同步位置信息
        self.position.latitude = orbital_satellite.latitude
        self.position.longitude = orbital_satellite.longitude
        self.position.timestamp = orbital_satellite.timestamp
        self.position.is_illuminated = orbital_satellite.illuminated
        
        # 同步光照状态到能量管理
        self.energy_state.is_illuminated = orbital_satellite.illuminated
        
        # 添加调试信息（仅在初始化时）
        if self.current_timeslot == 0:
            # 只监控Satellite0
            if self.satellite_id == 0:
                print(f"[监控] Satellite0 初始能量: {self.energy_state.battery_ratio:.0%}, 任务队列: {len(self.task_queue)}")
    
    # 修复：移除第一个重复的sync_with_communication_state方法
    # 保留第二个更完整的版本（在第886行）
    
    def step(self, current_time: float, time_delta: float):
        """
        执行一个时间步 - 支持5任务并行处理
        修复：简化接口，只接收仿真时间和时间间隔
        
        Args:
            current_time: 当前仿真时间（秒）
            time_delta: 时间间隔（秒）
        """
        # 修复：在步骤开始时重置步长指标
        self.performance_metrics.reset_step_metrics()
        
        # 修复：直接使用传入的仿真时间
        self.current_time = current_time
        self.current_timeslot = int(current_time / time_delta)  # 从时间推算时间步
        
        # 更新各个状态
        self.update_energy(time_delta)
        
        # 更新并行任务处理 - 修复：传递正确的current_time
        self.update_running_tasks(time_delta, self.current_time)
        
        # 调度新的并行任务
        self.schedule_parallel_tasks()
        
        # 只监控Satellite0，每10步输出一次状态
        if self.satellite_id == 0 and self.current_timeslot % 10 == 0:
            running_tasks = [f"Task{t.task_id}" for t in self.running_tasks.values()]
            queue_tasks = [f"Task{t.task_id}" for t in self.task_queue[:3]]  # 只显示前3个
            print(f"[Step {self.current_timeslot}] Satellite0: 执行{running_tasks} | 队列{queue_tasks} | 能量{self.energy_state.battery_ratio:.0%}")
    
    def get_status_summary(self) -> Dict[str, Any]:
        """
        获取卫星状态摘要
        
        Returns:
            Dict: 状态摘要信息
        """
        return {
            'satellite_id': self.satellite_id,
            'status': self.status.value,
            'position': {
                'latitude': self.position.latitude,
                'longitude': self.position.longitude,
                'altitude': self.position.altitude,
                'is_illuminated': self.position.is_illuminated
            },
            'energy': {
                'battery_ratio': self.energy_state.battery_ratio,
                'is_low_energy': self.energy_state.is_low_energy,
                'current_energy_mj': self.energy_state.current_battery_j / 1e6
            },
            'resources': {
                'cpu_utilization': self.resource_state.cpu_utilization,
                'memory_utilization': self.resource_state.memory_utilization
            },
            'tasks': {
                'queue_length': len(self.task_queue),
                'running_tasks': len(self.running_tasks),
                'running_task_ids': list(self.running_tasks.keys()),
                'available_cpu_capacity': self.resource_state.available_cpu_capacity,
                'total_received': self.performance_metrics.total_tasks_received,
                'total_completed': self.performance_metrics.total_tasks_completed,
                'completion_rate': self.performance_metrics.completion_rate
            },
            'communication': {
                'visible_neighbors': self.communication_state.neighbor_count,
                'visible_ground_stations': self.communication_state.ground_station_count
            },
            'current_timeslot': self.current_timeslot
        }
    
    def offload_task(self, task: Task, target_satellite_id: str) -> bool:
        """
        将任务卸载给其他卫星
        
        Args:
            task: 要卸载的任务
            target_satellite_id: 目标卫星ID
            
        Returns:
            bool: 是否成功开始卸载
        """
        if task not in self.task_queue:
            return False
        
        if target_satellite_id not in self.communication_state.visible_neighbors:
            return False
        
        # 计算传输成本（如果有通信管理器）
        if self.comm_manager:
            try:
                link_state = self.comm_manager.get_link_state(
                    self.satellite_id, target_satellite_id, self.current_timeslot
                )
                if not link_state:
                    return False
                
                # 估算传输能耗和时间
                data_size_mb = getattr(task, 'data_size_mb', 1.0)
                transfer_energy = link_state.get('transmission_energy_j', 0) * data_size_mb
                
                # 检查是否有足够能量
                if self.energy_state.current_battery_j < transfer_energy:
                    return False
                
                # 消耗传输能量
                self.energy_state.current_battery_j -= transfer_energy
                self.performance_metrics.total_energy_consumed_j += transfer_energy
                
            except (AttributeError, KeyError, TypeError) as e:
                import logging
                logging.warning(f"Failed to get link state for offload: {e}")
                # 如果无法获取链路状态，使用默认成本
                transfer_energy = 1000.0  # J
                self.energy_state.current_battery_j = max(
                    self.energy_state.current_battery_j - transfer_energy, 0
                )
        
        # 从队列移除任务
        self.task_queue.remove(task)
        
        # 更新任务状态（如果支持）
        if hasattr(task, 'transfer_to_satellite'):
            current_time = getattr(self, 'current_time', 0.0)  # 获取当前时间
            task.transfer_to_satellite(
                self.satellite_id, target_satellite_id, 
                0.1, transfer_energy if 'transfer_energy' in locals() else 1000.0, True, current_time
            )
        
        return True
    
    def offload_task_to_cloud(self, task: Task, cloud_center_id: str, cloud_server_manager) -> bool:
        """
        将任务卸载给云服务器
        
        Args:
            task: 要卸载的任务
            cloud_center_id: 目标云中心ID
            cloud_server_manager: 云服务器管理器
            
        Returns:
            bool: 是否成功卸载
        """
        if task not in self.task_queue:
            return False
        
        # 检查云服务器是否可见
        visible_clouds = cloud_server_manager.get_visible_cloud_centers(self.satellite_id, self.current_timeslot)
        if cloud_center_id not in visible_clouds:
            return False
        
        # 尝试发送任务到云服务器
        success = cloud_server_manager.send_task_to_cloud(
            self.satellite_id, task, self.current_timeslot, self.current_time
        )
        
        if success:
            # 从本地队列移除任务
            self.task_queue.remove(task)
            
            # 只为Satellite0记录云卸载
            if self.satellite_id == 0:
                print(f"[监控] Satellite0 卸载Task{task.task_id}到云{cloud_center_id}")
            
            return True
        
        return False
    
    def get_best_offload_target(self, task: Task) -> Optional[str]:
        """
        为任务选择最佳卸载目标
        
        Args:
            task: 要卸载的任务
            
        Returns:
            Optional[str]: 最佳目标卫星ID，如果没有合适目标则返回None
        """
        if not self.communication_state.visible_neighbors:
            return None
        
        # 简化的目标选择：选择第一个可见邻居
        # 在实际实现中，这里可以根据邻居的负载、能量状态等进行智能选择
        return self.communication_state.visible_neighbors[0] if self.communication_state.visible_neighbors else None
    
    def sync_with_communication_state(self, network_state: Dict, time_step: int):
        """
        与communication.py同步网络状态
        
        Args:
            network_state: 网络状态信息
            time_step: 当前时间步
        """
        # 获取邻居列表 - 修复：改进异常处理
        if self.comm_manager:
            try:
                neighbors = self.comm_manager.get_neighbors(self.satellite_id, time_step)
                self.communication_state.visible_neighbors = neighbors
            except (AttributeError, KeyError, TypeError) as e:
                import logging
                logging.warning(f"Failed to get neighbors for {self.satellite_id}: {e}")
                self.communication_state.visible_neighbors = []
        
        # 更新连接状态
        self.update_connectivity({
            'neighbors': self.communication_state.visible_neighbors,
            'ground_stations': network_state.get('ground_stations', [])
        })
    
    def sync_with_task_state(self, new_tasks: List[Task]):
        """
        与task.py同步，接收新任务
        
        Args:
            new_tasks: 新任务列表
        """
        for task in new_tasks:
            if self.can_accept_tasks():
                self.receive_task(task)
    
    def export_for_scheduling(self) -> Dict[str, Any]:
        """
        导出用于调度的状态信息
        
        Returns:
            Dict: 调度相关状态
        """
        return {
            'satellite_id': self.satellite_id,
            'can_accept_tasks': (len(self.task_queue) < self.max_queue_size and 
                               self.status == SatelliteStatus.ACTIVE),
            'available_cpu_hz': self.resource_state.available_cpu_hz,
            'energy_ratio': self.energy_state.battery_ratio,
            'queue_length': len(self.task_queue),
            # 修复：移除已弃用的current_processing_task引用
            'current_processing_task': None,  # 已不再使用单任务处理
            'visible_neighbors': self.communication_state.visible_neighbors,
            'position': (self.position.latitude, self.position.longitude),
            'performance_metrics': {
                'completion_rate': self.performance_metrics.completion_rate,
                'total_energy_consumed': self.performance_metrics.total_energy_consumed_j
            }
        }
    
    def export_observation_data(self) -> Dict[str, Any]:
        """
        导出用于强化学习观测的完整状态数据
        
        Returns:
            Dict: 完整的观测数据
        """
        # 任务队列特征
        queue_priorities = [getattr(task, 'priority', 0) for task in self.task_queue]
        queue_urgencies = []
        for task in self.task_queue:
            if hasattr(task, 'deadline_timestamp'):
                urgency = max(0, task.deadline_timestamp - self.current_time)
                queue_urgencies.append(urgency)
        
        return {
            'satellite_id': self.satellite_id,
            'position': {
                'latitude': self.position.latitude,
                'longitude': self.position.longitude,
                'altitude': self.position.altitude,
                'is_illuminated': self.position.is_illuminated
            },
            'energy': {
                'battery_ratio': self.energy_state.battery_ratio,
                'is_low_energy': self.energy_state.is_low_energy,
                'current_energy_j': self.energy_state.current_battery_j
            },
            'resources': {
                'cpu_utilization': self.resource_state.cpu_utilization,
                'available_cpu_hz': self.resource_state.available_cpu_hz,
                'memory_utilization': self.resource_state.memory_utilization
            },
            'tasks': {
                'queue_length': len(self.task_queue),
                # 修复：移除已弃用的单任务处理引用
                'current_processing': None,  # 已不再使用单任务处理
                'queue_avg_priority': np.mean(queue_priorities) if queue_priorities else 0.0,
                'queue_min_urgency': min(queue_urgencies) if queue_urgencies else 0.0,
                'total_received': self.performance_metrics.total_tasks_received,
                'completion_rate': self.performance_metrics.completion_rate
            },
            'communication': {
                'visible_neighbors': self.communication_state.visible_neighbors,
                'neighbor_count': self.communication_state.neighbor_count,
                'ground_station_count': self.communication_state.ground_station_count
            },
            'status': {
                'is_healthy': self.is_healthy(),
                'can_accept_tasks': self.can_accept_tasks(),
                'status': self.status.value
            },
            'time': {
                'current_timeslot': self.current_timeslot,
                'current_time': self.current_time
            }
        }
    
    def is_healthy(self) -> bool:
        """检查卫星是否健康"""
        return (self.status == SatelliteStatus.ACTIVE and 
                self.energy_state.current_battery_j > 0)
    
    def can_accept_tasks(self) -> bool:
        """检查是否可以接收新任务"""
        return (self.is_healthy() and 
                len(self.task_queue) < self.max_queue_size)
    
    def __repr__(self) -> str:
        return (f"SatelliteNode(id={self.satellite_id}, status={self.status.value}, "
                f"energy={self.energy_state.battery_ratio:.2%}, "
                f"queue={len(self.task_queue)}, pos=({self.position.latitude:.2f}, {self.position.longitude:.2f}))")


def main():
    """测试函数 - 修复：适配新的构造函数接口"""
    print("=== Satellite Node 基础功能测试 ===")
    
    # 创建测试配置
    test_config = {
        'system': {
            'leo_altitude_m': 1200000,
            'total_timeslots': 1000,
            'timeslot_duration_s': 10
        },
        'computation': {
            'f_leo_hz': 10e9,
            'leo_battery_capacity_j': 3600000,
            'leo_solar_power_w': 500,
            'zeta_leo': 1.0e-28
        },
        'communication': {
            'max_retries': 3
        }
    }
    
    # 创建测试卫星 - 传入必需的config参数
    satellite = SatelliteNode("Satellite111", test_config)
    
    print(f"初始状态: {satellite}")
    print(f"状态摘要: {satellite.get_status_summary()}")
    
    # 模拟几个时间步
    print("\n--- 运行仿真 ---")
    for step in range(5):
        current_time = step * 10.0
        satellite.step(step, 10.0, current_time)  # 传入仿真时间
        print(f"Step {step}: {satellite}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()