<mxfile host="65bd71144e">
    <diagram name="轨道更新模块架构图" id="orbital-updater-architecture">
        <mxGraphModel dx="2836" dy="2285" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="main-function" value="main()&#xa;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="790" y="40" width="100" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="data-group" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="250" y="1260" width="1000" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="210" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="ground-stations-csv" value="updated_global_ground_stations.csv&#xa;地面站数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="680" y="40" width="220" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-data-csv" value="satellite_processed_data.csv&#xa;卫星轨道数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="470" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="controller-group" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="417" y="150" width="800" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="orbital-updater" value="OrbitalUpdater&#xa;轨道更新器&#xa;- 卫星轨道管理&#xa;- 可见性计算&#xa;- 地面覆盖分析" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="controller-group" vertex="1">
                    <mxGeometry x="310" y="50" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="entity-group" value="数据模型层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="420" y="420" width="840" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-class" value="Satellite&#xa;卫星实体类&#xa;- satellite_id&#xa;- longitude/latitude&#xa;- illuminated&#xa;- timestamp&#xa;- velocity" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="entity-group" vertex="1">
                    <mxGeometry x="260" y="40" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="groundstation-class" value="GroundStation&#xa;地面站实体类&#xa;- station_id&#xa;- longitude/latitude&#xa;- name/type" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="entity-group" vertex="1">
                    <mxGeometry x="540" y="55" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="core-function-group" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="127" y="640" width="1400" height="100" as="geometry">
                        <mxRectangle x="127" y="640" width="100" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="get-satellites" value="get_satellites_at_time()&#xa;获取时间步卫星状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="50" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="update-positions" value="update_satellite_positions()&#xa;更新卫星位置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="250" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="inter-sat-visibility" value="build_inter_satellite_visibility_matrix()&#xa;卫星间可见性矩阵" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="450" y="40" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="sat-ground-visibility" value="build_satellite_ground_visibility_matrix()&#xa;卫星-地面站可见性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="690" y="40" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="ground-coverage" value="get_all_ground_coverage()&#xa;地面覆盖分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="core-function-group" vertex="1">
                    <mxGeometry x="930" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="compute-service-group" value="计算服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="180" y="820" width="1200" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="get-coverage" value="get_ground_coverage()&#xa;单卫星覆盖计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="927" y="40" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="calc-ground-visibility" value="calculate_satellite_ground_visibility()&#xa;地面可见性计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="630" y="40" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="calc-velocity" value="calculate_velocity()&#xa;速度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="100" y="40" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="calc-sat-visibility" value="calculate_satellite_visibility()&#xa;卫星可见性计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="compute-service-group" vertex="1">
                    <mxGeometry x="330" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="utility-group" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="250" y="1000" width="1000" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="calc-distance" value="_calculate_distance()&#xa;距离计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="360" y="40" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="load-config" value="_load_config()&#xa;配置加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="120" y="40" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="create-ground-stations" value="_create_ground_stations()&#xa;地面站创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="800" y="40" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="load-satellite-data" value="_load_satellite_data()&#xa;卫星数据加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="600" y="40" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="config-group" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="450" y="-130" width="800" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="earth-radius" value="earth_radius&#xa;地球半径" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="50" y="40" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-altitude" value="satellite_altitude&#xa;卫星高度" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="180" y="40" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="visibility-threshold" value="visibility_threshold&#xa;可见性阈值" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="330" y="40" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="timeslot-duration" value="timeslot_duration&#xa;时隙持续时间" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
                    <mxGeometry x="490" y="40" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="main-to-updater" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="main-function" target="orbital-updater" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="840" y="50"/>
                            <mxPoint x="840" y="50"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="csv-to-load-sat" value="数据读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="satellite-data-csv" target="load-satellite-data" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="yaml-to-load-config" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="config-yaml" target="load-config" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="ground-csv-to-create" value="地面站数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="ground-stations-csv" target="create-ground-stations" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="updater-to-satellite" value="创建对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="orbital-updater" target="satellite-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="updater-to-groundstation" value="创建对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="orbital-updater" target="groundstation-class" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="get-sat-to-update" value="位置更新" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="get-satellites" target="update-positions" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="update-to-visibility" value="可见性计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="update-positions" target="inter-sat-visibility" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="inter-to-ground-vis" value="地面可见性" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="inter-sat-visibility" target="sat-ground-visibility" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="ground-vis-to-coverage" value="覆盖分析" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="sat-ground-visibility" target="ground-coverage" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="inter-vis-to-calc-sat" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="inter-sat-visibility" target="calc-sat-visibility" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="650" y="800"/>
                            <mxPoint x="560" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="ground-vis-to-calc-ground" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="sat-ground-visibility" target="calc-ground-visibility" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="890" y="760"/>
                            <mxPoint x="940" y="760"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="update-to-calc-velocity" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="update-positions" target="calc-velocity" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="457" y="760"/>
                            <mxPoint x="320" y="760"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="coverage-to-get-coverage" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="ground-coverage" target="get-coverage" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="calc-sat-to-distance" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="calc-sat-visibility" target="calc-distance" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="590" y="980"/>
                            <mxPoint x="730" y="980"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="calc-ground-to-distance" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="calc-ground-visibility" target="calc-distance" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="900" y="980"/>
                            <mxPoint x="730" y="980"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="main-data-flow" value="主数据流&#xa;(1000次循环)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="orbital-updater" target="get-satellites" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="827" y="350"/>
                            <mxPoint x="257" y="350"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="legend-group" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="-30" y="-120" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="legend-solid" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="40" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-dashed" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="65" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-thick" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="90" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-blue" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="115" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-green" value="绿色：数据实体" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="140" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-orange" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="165" width="120" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>