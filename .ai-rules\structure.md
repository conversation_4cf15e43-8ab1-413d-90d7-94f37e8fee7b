---
title: Project Structure
description: "项目结构规范和文件组织原则"
inclusion: always
---

# 项目结构规范 - SPACE-DMPO

## 整体架构设计

### 核心设计原则
- **分层解耦**：环境模拟、算法实现、实验配置三层分离
- **模块化**：各功能模块独立，便于测试和维护
- **标准化**：遵循Gymnasium接口标准，支持多智能体扩展
- **可配置**：关键参数通过配置文件管理

## 目录结构详解

### 根目录结构
```
SPACE-DMPO1/
├── src/                    # 核心源代码
├── tests/                  # 测试代码
├── ARCHITURE/             # 架构设计文档
├── technical_files/       # 技术文档
├── result/                # 实验结果
├── utils/                 # 通用工具
├── requirements.txt       # 依赖管理
├── paper.md              # 论文内容
└── .ai-rules/            # AI开发指导文件
```

### src/ 核心代码结构

#### src/env/ - 环境模拟核心
```
src/env/
├── __init__.py           # 环境包初始化
├── env_interface.py      # Gym环境主接口 (OAAL Environment)
├── satellite.py          # 卫星节点建模
├── cloud_station.py      # 云节点建模
├── task.py               # 任务管理
├── communication.py      # 通信模型与链路计算
├── orbital_updater.py    # 轨道动力学更新
├── adapters.py           # 系统集成适配器
├── policy_domain.py      # 策略域机制实现
├── task_generator.py     # 基于霍克斯过程的任务生成
├── config.yaml           # 环境配置参数
├── satellite_processed_data1.csv # 卫星轨道数据
├── updated_global_ground_stations.csv # 地面站数据
├── task_generation_results.json    # 任务生成数据
├── regions.json          # 策略域划分数据
└── task_data/           # 任务数据文件
地面站、云处理中心、卫星位置更新均采用csv文件中的数据，task_generator是生成数据的文件，但程序运行时不直接采用该文件，采用的是task_generation_results.json文件中的数据，region.json是策略区域的数据。
```

**env_interface.py** - 核心环境接口
- 实现标准Gym接口：`reset()`, `step()`, `render()`, `close()`
- 支持多智能体并行决策
- 提供状态空间和动作空间信息接口
- 集成动态故障屏蔽机制

**satellite.py** - 卫星智能体建模
- 每颗LEO卫星作为独立智能体
- 本地状态管理：位置、资源、任务队列、能量

**task.py** - 任务系统
- 任务生命周期管理
- 动态优先级评分机制
- 任务失败与重试逻辑

#### src/agent/ - 智能体算法实现
```
src/agent/
├── LEO/                  # LEO卫星智能体
│   ├── DPPO/            # 分布式PPO算法
│   └── MAPPO/           # 多智能体PPO算法
└── GEO/                 # GEO卫星全局协调
```

#### src/algorithms/ - 强化学习算法
```
src/algorithms/
└── reinforcement_learning/
    └── dppo.py          # DPPO算法核心实现
```

### 环境接口设计规范

#### 状态空间标准化
```python
# 环境状态返回格式
state = {
    'global_state': {
        'timeslot': int,              # 当前时隙
        'total_tasks_in_system': int, # 系统总任务数
        'average_queue_length': float, # 平均队列长度
        'average_cpu_utilization': float, # 平均CPU利用率
        'average_energy_level': float,    # 平均能量水平
        'network_connectivity': float     # 网络连通性
    },
    'satellite_states': {
        'sat_id': {
            'position': [x, y, z],       # 卫星位置
            'status': str,               # 运行状态
            'resources': {...},          # 资源状态
            'energy': {...},             # 能量状态
            'task_queues': {...},        # 任务队列状态
            'links': {...},              # 链路状态
            'performance': {...}         # 性能指标
        }
    }
}
```

#### 动作空间标准化
```python
# 动态动作空间设计
actions = {
    'sat_id': action_value  # 动态范围，基于可见性计算
}

# 动作空间构成：
# 0: 本地处理
# 1 to N_visible_sats: 卫星间卸载（目标卫星ID基于ISL可见性）
# N_visible_sats+1 to N_visible_sats+N_visible_gs: 云端卸载（目标地面站基于可见性）

# 动作空间大小 = 1 + 当前可见卫星数 + 当前可见地面站数
action_space_size = 1 + len(visible_satellites)
 + len(visible_ground_stations)
```

#### 奖励机制设计
- **个体奖励**：每个智能体基于其决策效果获得局部奖励，最终的全局奖励通过加权求和得到
- **多目标优化**：平衡任务完成、延迟、能耗多个目标
- **权重可配置**：支持通过配置文件调整奖励权重

### 多智能体协调机制

#### 地面任务生成 → 星上决策处理流程

1. **任务生成阶段**
   ```json
   # task_generator.py 生成任务数据
   {
       "task_id": int,
       "type_id": int,
       "data_size_mb": float,
       "complexity_cycles_per_bit": int,
       "deadline_timestamp": float,
       "priority": int,
       "source_location_id": int
   }
   读取程序中生成的数据
   ```

2. **智能体状态观测**
   ```python
   # satellite.py 中每个智能体观测
   def get_local_observation() -> Dict:
       # 本地状态：队列、资源、能量
       # 邻居状态：通过ISL获取的邻居摘要
       # 网络状态：连接性、故障状态
   ```

3. **分布式决策**
   ```python
   # env_interface.py 中协调多智能体决策
   def step(actions: Dict[str, int]) -> Tuple:
       # 应用所有智能体的动作
       # 执行任务调度与处理
       # 计算奖励与下一状态
       # 检查终止条件
   ```

4. **任务执行与评估**
   - 本地处理：在决策卫星上直接处理
   - 星间卸载：通过ISL转发给邻居卫星
   - 云端卸载：通过星-云链路回传地面

### 容错与动态适应

#### 故障检测与恢复
卫星故障后自动全域感知，然后观察恢复情况。

#### 策略域机制
- **区域划分**：全球24个策略域
- **师生学习**：区域教师网络指导访客LEO卫星
- **经验共享**：LEO卫星贡献经验到区域经验池

### 配置管理规范

#### config.yaml 结构
```yaml
system:
  num_leo_satellites: 36
  num_users: 420
  total_timeslots: 1000

reward:
  w_success: 10.0
  w_delay: 1.0
  w_energy: 0.5
  task_failure_penalty: -100.0

communication:
  uplink_bandwidth: 100e6  # Hz
  isl_capacity: 50e9       # bps
  
environment:
  random_seed: 42
  render_mode: 'human'
```

### 实验与测试结构

#### tests/ 测试组织
```
tests/
├── env/                    # 环境模块测试
├── test_orbital_updater.py # 轨道更新测试
├── orbital_updater_validation.py # 轨道验证
└── utils/                  # 测试工具
```

#### 性能基准
- **仿真规模**：支持36颗卫星、420用户、1000时隙
- **实时性能**：单步执行时间 < 100ms
- **内存效率**：合理管理历史轨迹数据
- **收敛性**：多智能体策略在合理时间内收敛

### 扩展接口设计

#### 新算法接入规范
1. 继承环境接口，无需修改环境代码
2. 遵循Gym标准，支持自定义观测和动作处理
3. 利用现有的状态表示和奖励机制
4. 通过配置文件调整算法专用参数



### 文档与开发指南

#### 代码规范
- Python类型注解：所有公共接口使用类型提示
- Docstring规范：遵循Google风格文档字符串
- 单元测试：核心模块提供pytest测试用例

#### 开发工作流
1. 环境修改：优先考虑参数配置，避免硬编码
2. 算法集成：基于标准接口开发，确保模块解耦
3. 实验设计：通过配置文件管理实验参数
4. 结果分析：利用现有的指标收集和可视化功能