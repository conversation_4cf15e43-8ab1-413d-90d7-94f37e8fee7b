# 程序架构图生成提示词

请根据我提供的程序代码，生成一个详细的程序运行架构图。要求如下：

## 架构图要求

### 1. 程序模块完整性
- 识别所有独立的程序模块（如：轨道更新程序、任务生成程序、卫星实体程序等）
- 显示每个程序模块内的主要类（Class）、函数（Function）
- 标注每个模块的核心功能和职责
- 展示程序的文件结构和组织方式

### 2. 程序执行流程展示
- 清晰显示程序的启动顺序和执行流程
- 展示主程序与子程序/模块的调用关系
- 标明程序间的数据传递和接口调用
- 显示程序的运行时序和依赖关系

### 3. 接口调用关系
- 详细展示模块间的接口调用关系
- 用不同的线条样式区分不同类型的调用：
  - 实线箭头：直接函数调用
  - 虚线箭头：间接调用或数据传递
  - 双向箭头：相互调用关系
  - 粗线：主要数据流或频繁调用
- 标注接口名称、参数类型、返回值

### 4. 程序层次结构
- 按照程序的逻辑层次组织：
  - 主控程序层：程序入口、主要控制逻辑
  - 功能模块层：各个独立的功能程序
  - 工具函数层：被多个模块调用的公共函数
  - 数据处理层：数据存储、读取、处理相关程序
- 突出核心程序模块和辅助工具模块

### 5. 技术实现细节
- 标注重要函数的输入输出参数
- 显示关键数据结构和数据传递格式
- 标明程序间的通信方式（函数调用、文件读写、消息传递等）
- 注明重要的配置参数和初始化过程

### 6. 视觉要求（适配Draw.io）
- 提供适合draw.io绘制的详细描述和指导
- 建议使用的图形元素和颜色方案
- 合理的布局建议，避免线条交叉混乱
- 添加必要的图例和说明

## 输出格式

请按以下格式输出：

1. **程序架构概述**：简要描述整个程序系统的架构特点和运行逻辑

2. **Draw.io架构图绘制指导**：详细描述如何在draw.io中绘制架构图，包括：
   - 各个程序模块的形状建议（矩形表示程序模块、圆角矩形表示函数、菱形表示判断等）
   - 颜色方案建议（不同类型模块用不同颜色区分）
   - 程序模块的相对位置和布局（按执行顺序或调用层次排列）
   - 连接线的类型、方向、标签（标注接口名称和数据类型）
   - 分组和层次的组织方式

3. **程序模块详细描述**：为每个主要程序模块提供详细描述，包括：
   - 程序模块名称和文件位置
   - 主要功能和职责
   - 关键类和函数列表
   - 对外提供的接口
   - 依赖的其他模块

4. **程序执行流程说明**：描述主要的程序执行流程：
   - 程序启动和初始化流程
   - 主要业务逻辑的执行顺序
   - 模块间的调用时序
   - 数据处理和传递流程

5. **接口调用分析**：详细说明模块间的接口调用：
   - 调用方和被调用方
   - 接口函数名称和参数
   - 数据传递的格式和内容
   - 调用的频率和时机

## 程序架构分析重点

- 重点关注程序的实际运行逻辑，而不是理论概念
- 突出显示程序模块间的具体调用关系
- 展示数据在各个程序模块间的流转过程
- 标明程序的执行顺序和依赖关系
- 识别核心程序模块和辅助工具模块
- 显示程序的入口点和主要控制流程

## 示例场景说明

以你提到的场景为例：
- 轨道更新程序：提供轨道计算相关的接口函数
- 任务生成程序：提供任务创建和管理的接口函数  
- 卫星实体程序：作为主要的业务逻辑程序，需要调用轨道更新和任务生成程序的接口
- 通过连线展示：卫星实体程序 → 调用 → 轨道更新程序的某个函数
- 通过连线展示：卫星实体程序 → 调用 → 任务生成程序的某个函数

请确保架构图能够清晰地展示程序的实际运行逻辑和模块间的调用关系，让读者能够快速理解整个程序系统是如何协同工作的。