<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="SPACE-OAAL环境架构图" id="env-architecture">
    <mxGraphModel dx="9713" dy="5468" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="main-entry" value="SPACE-OAAL&#xa;环境系统入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="750" y="-1100" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="interface-group" value="接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="425" y="-960" width="800" height="280" as="geometry" />
        </mxCell>
        <mxCell id="env-interface" value="OAALEnvironmentInterface&#xa;环境接口&#xa;- reset() 重置环境&#xa;- step(actions) 执行动作&#xa;- _format_state() 格式化状态&#xa;- _apply_actions() 应用动作&#xa;- _calculate_rewards() 计算奖励&#xa;- render() 渲染环境" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#5b9bd5;fontSize=11;fontStyle=1" parent="interface-group" vertex="1">
          <mxGeometry x="292" y="60" width="220" height="180" as="geometry" />
        </mxCell>
        <mxCell id="adapter-group" value="适配器层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="180" y="-610" width="1290" height="260" as="geometry" />
        </mxCell>
        <mxCell id="satellite-adapter" value="SatelliteAdapter&#xa;卫星适配器&#xa;- sync_all_satellites() 同步卫星&#xa;- create_satellite_node() 创建节点&#xa;- update_satellite_node() 更新节点&#xa;- _update_communication_links() 更新链路" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" parent="adapter-group" vertex="1">
          <mxGeometry x="50" y="50" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="task-adapter" value="TaskAdapter&#xa;任务适配器&#xa;- generate_tasks_for_timeslot() 生成任务&#xa;- convert_generated_task() 转换任务&#xa;- get_enhanced_tasks() 获取增强任务&#xa;- _assign_tasks_to_satellites() 分配任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=11;fontStyle=1" parent="adapter-group" vertex="1">
          <mxGeometry x="990" y="50" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="integration-manager" value="IntegrationManager&#xa;集成管理器&#xa;- step(timeslot) 执行时间步&#xa;- reset() 重置系统&#xa;- get_system_state() 获取状态&#xa;- export_results() 导出结果&#xa;- _update_simulation_stats() 更新统计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" parent="adapter-group" vertex="1">
          <mxGeometry x="550" y="50" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="entity-group" value="实体层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="180" y="-270" width="1290" height="260" as="geometry">
            <mxRectangle x="180" y="-270" width="80" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="satellite-node" value="SatelliteNode&#xa;卫星节点&#xa;- Position, EnergyState 位置和能量状态&#xa;- ResourceState, task_queue 资源和任务队列&#xa;- neighbors, ground_stations 邻居和地面站&#xa;- process_task() 处理任务&#xa;- offload_task() 卸载任务&#xa;- get_best_offload_target() 获取最佳卸载目标" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="entity-group" vertex="1">
          <mxGeometry x="50" y="55" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="task-entity" value="Task&#xa;任务实体&#xa;- task_id, state, priority 任务属性&#xa;- data_size_mb, deadline 数据和截止时间&#xa;- processing_records[] 处理记录&#xa;- transfer_records[] 传输记录&#xa;- process() 处理任务&#xa;- transfer() 传输任务&#xa;- calculate_priority() 计算优先级" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="entity-group" vertex="1">
          <mxGeometry x="990" y="55" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="task-manager" value="TaskManager&#xa;任务管理器&#xa;- tasks: Dict[str, Task] 任务字典&#xa;- add_task() 添加任务&#xa;- remove_task() 移除任务&#xa;- get_task() 获取任务&#xa;- update_priorities() 更新优先级&#xa;- cleanup_expired_tasks() 清理过期任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="entity-group" vertex="1">
          <mxGeometry x="547" y="55" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="physics-group" value="物理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="180" y="80" width="1290" height="300" as="geometry" />
        </mxCell>
        <mxCell id="orbital-updater" value="OrbitalUpdater&#xa;轨道更新器&#xa;- satellite_data: DataFrame 卫星数据&#xa;- ground_stations: Dict 地面站&#xa;- get_satellites_at_time() 获取时刻卫星&#xa;- calculate_visibility_matrix() 计算可见性矩阵&#xa;- get_ground_coverage() 获取地面覆盖&#xa;- _load_satellite_data() 加载卫星数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="physics-group" vertex="1">
          <mxGeometry x="300" y="75" width="200" height="175" as="geometry" />
        </mxCell>
        <mxCell id="communication-manager" value="CommunicationManager&#xa;通信管理器&#xa;- calculate_link_properties() 计算链路属性&#xa;- get_network_state() 获取网络状态&#xa;- get_neighbors() 获取邻居&#xa;- get_link_state() 获取链路状态&#xa;- calculate_signal_strength() 计算信号强度&#xa;- _load_config() 加载配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="physics-group" vertex="1">
          <mxGeometry x="810" y="75" width="200" height="175" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;shape=flexArrow;" parent="1" source="config-group" target="physics-group" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="420" />
              <mxPoint x="825" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="config-group" value="配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="180" y="470" width="1290" height="210" as="geometry" />
        </mxCell>
        <mxCell id="satellite-data" value="satellite_processed_data.csv&#xa;卫星数据文件&#xa;- satellite_id 卫星ID&#xa;- timestamp 时间戳&#xa;- latitude 纬度&#xa;- longitude 经度&#xa;- illuminated 光照状态" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" parent="config-group" vertex="1">
          <mxGeometry x="10" y="40" width="210" height="150" as="geometry" />
        </mxCell>
        <mxCell id="ground-stations-data" value="updated_global_ground_stations.csv&#xa;地面站数据文件&#xa;- ID 地面站ID&#xa;- Latitude 纬度&#xa;- Longitude 经度&#xa;- RegionType 区域类型&#xa;- PurposeType 用途类型" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" parent="config-group" vertex="1">
          <mxGeometry x="260" y="40" width="210" height="150" as="geometry" />
        </mxCell>
        <mxCell id="task-data" value="task_generation_results.json&#xa;任务数据文件&#xa;- regions.json 区域数据&#xa;- 任务生成结果" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" parent="config-group" vertex="1">
          <mxGeometry x="760" y="40" width="210" height="150" as="geometry" />
        </mxCell>
        <mxCell id="config-yaml" value="config.yaml&#xa;系统配置文件&#xa;- system 系统配置&#xa;- communication 通信配置&#xa;- computation 计算配置&#xa;- queuing 队列配置&#xa;- reward 奖励配置&#xa;- fault 故障配置" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="config-group" vertex="1">
          <mxGeometry x="510" y="40" width="210" height="150" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-1" value="cloud_sattion.csv&lt;br&gt;地面云中心数据文件&lt;br&gt;- ID 云中心ID&lt;br&gt;- Latitude 纬度&lt;br&gt;- Longitude 经度&lt;div&gt;&lt;br/&gt;&lt;/div&gt;" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=10" parent="config-group" vertex="1">
          <mxGeometry x="1010" y="40" width="210" height="150" as="geometry" />
        </mxCell>
        <mxCell id="main-to-env" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="main-entry" target="env-interface" edge="1">
          <mxGeometry x="-0.5" y="25" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="-950" />
              <mxPoint x="825" y="-950" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="env-to-integration" value="主要调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1" parent="1" source="env-interface" target="integration-manager" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="827" y="-620" />
              <mxPoint x="827" y="-620" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="integration-to-satellite" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="integration-manager" target="satellite-adapter" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="integration-to-task" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="integration-manager" target="task-adapter" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="satellite-adapter-to-node" value="创建/更新节点" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-adapter" target="satellite-node" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="task-adapter-to-task" value="创建任务实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="task-adapter" target="task-entity" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="satellite-adapter-to-orbital" value="数据获取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="satellite-adapter" target="orbital-updater" edge="1">
          <mxGeometry x="0.2381" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="90" y="-480" />
              <mxPoint x="90" y="250" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="satellite-adapter-to-comm" value="网络状态" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="satellite-adapter" target="communication-manager" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="90" y="-480" />
              <mxPoint x="90" y="50" />
              <mxPoint x="1090" y="50" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="satellite-to-task-manager" value="任务管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="satellite-node" target="task-manager" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="620" y="-135" />
              <mxPoint x="620" y="-135" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="task-manager-to-task" value="管理任务实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="task-manager" target="task-entity" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1110" y="-140" />
              <mxPoint x="1110" y="-140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="legend-direct-call" value="直接调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=12;" parent="1" edge="1">
          <mxGeometry x="1220" y="480" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-main-flow" value="主要数据流" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#d62728;fontSize=12;" parent="1" edge="1">
          <mxGeometry x="1220" y="510" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-data-transfer" value="数据传递" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#666666;strokeDashArray=5 5;fontSize=12;" parent="1" edge="1">
          <mxGeometry x="1220" y="540" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="title" value="SPACE-OAAL 环境架构图&#xa;Environment Architecture Diagram" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="627" y="-1160" width="400" height="30" as="geometry" />
        </mxCell>
        <mxCell id="version" value="Version: 1.0.0 | Date: 2025-07-26 | SPACE-OAAL Team" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="625" y="-1120" width="400" height="20" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-73" value="main()&#xa;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="-932" y="750" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-74" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1380" y="1900" width="1000" height="100" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-75" value="config.yaml&#xa;系统配置文件" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="fI-K-m2AIIzt_EUsSEGo-74" vertex="1">
          <mxGeometry x="77" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-76" value="updated_global_ground_stations.csv&#xa;地面站数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="fI-K-m2AIIzt_EUsSEGo-74" vertex="1">
          <mxGeometry x="540" y="40" width="250" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-77" value="satellite_processed_data.csv&#xa;卫星轨道数据" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="fI-K-m2AIIzt_EUsSEGo-74" vertex="1">
          <mxGeometry x="310" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-130" value="&lt;span style=&quot;text-align: left;&quot;&gt;cloud_station.csv&lt;/span&gt;&lt;div style=&quot;text-align: left;&quot;&gt;地面云中心数据&lt;/div&gt;" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="fI-K-m2AIIzt_EUsSEGo-74" vertex="1">
          <mxGeometry x="820" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-78" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1443" y="860" width="1140" height="140" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-79" value="OrbitalUpdater&#xa;轨道更新器&#xa;- 卫星轨道管理&#xa;- 可见性计算&#xa;- 地面覆盖分析" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="fI-K-m2AIIzt_EUsSEGo-78" vertex="1">
          <mxGeometry x="463" y="45" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-80" value="数据模型层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1300" y="1110" width="840" height="180" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-81" value="Satellite&#xa;卫星实体类&#xa;- satellite_id&#xa;- longitude/latitude&#xa;- illuminated&#xa;- timestamp&#xa;- velocity" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="fI-K-m2AIIzt_EUsSEGo-80" vertex="1">
          <mxGeometry x="117" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-82" value="GroundStation&#xa;地面站实体类&#xa;- station_id&#xa;- longitude/latitude&#xa;- name/type" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="fI-K-m2AIIzt_EUsSEGo-80" vertex="1">
          <mxGeometry x="557" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-83" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1580" y="1350" width="1400" height="100" as="geometry">
            <mxRectangle x="127" y="640" width="100" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-84" value="get_satellites_at_time()&#xa;获取时间步卫星状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-83" vertex="1">
          <mxGeometry x="107" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-85" value="update_satellite_positions()&#xa;更新卫星位置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-83" vertex="1">
          <mxGeometry x="357" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-86" value="build_inter_satellite_visibility_matrix()&#xa;卫星间可见性矩阵" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-83" vertex="1">
          <mxGeometry x="600" y="40" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-87" value="build_satellite_ground_visibility_matrix()&#xa;卫星-地面站可见性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-83" vertex="1">
          <mxGeometry x="907" y="40" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-88" value="get_all_ground_coverage()&#xa;地面覆盖分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-83" vertex="1">
          <mxGeometry x="1207" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-89" value="计算服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1424.5" y="1530" width="1103" height="100" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-90" value="get_ground_coverage()&#xa;单卫星覆盖计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-89" vertex="1">
          <mxGeometry x="780" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-91" value="calculate_satellite_ground_visibility()&#xa;地面可见性计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-89" vertex="1">
          <mxGeometry x="530" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-92" value="calculate_velocity()&#xa;速度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-89" vertex="1">
          <mxGeometry x="110" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-93" value="calculate_satellite_visibility()&#xa;卫星可见性计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-89" vertex="1">
          <mxGeometry x="320" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-94" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1380" y="1710" width="1000" height="100" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-95" value="_calculate_distance()&#xa;距离计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-94" vertex="1">
          <mxGeometry x="240" y="40" width="130" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-96" value="_load_config()&#xa;配置加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-94" vertex="1">
          <mxGeometry x="60" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-97" value="_create_ground_stations()&#xa;地面站创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-94" vertex="1">
          <mxGeometry x="630" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-98" value="_load_satellite_data()&#xa;卫星数据加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-94" vertex="1">
          <mxGeometry x="450" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-131" value="_create_cloud_stations()&lt;br&gt;云中心创建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-94" vertex="1">
          <mxGeometry x="830" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-99" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="-1282" y="2030" width="800" height="100" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-100" value="earth_radius&#xa;地球半径" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-99" vertex="1">
          <mxGeometry x="59" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-101" value="satellite_altitude&#xa;卫星高度" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-99" vertex="1">
          <mxGeometry x="249" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-102" value="visibility_threshold&#xa;可见性阈值" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-99" vertex="1">
          <mxGeometry x="439" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-103" value="timeslot_duration&#xa;时隙持续时间" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="fI-K-m2AIIzt_EUsSEGo-99" vertex="1">
          <mxGeometry x="629" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-104" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-73" target="fI-K-m2AIIzt_EUsSEGo-79" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-882" y="760" />
              <mxPoint x="-882" y="760" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-105" value="数据读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-77" target="fI-K-m2AIIzt_EUsSEGo-98" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-940" y="1840" />
              <mxPoint x="-850" y="1840" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-106" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-75" target="fI-K-m2AIIzt_EUsSEGo-96" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-107" value="地面站数据" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-76" target="fI-K-m2AIIzt_EUsSEGo-97" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-108" value="创建对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-79" target="fI-K-m2AIIzt_EUsSEGo-81" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-109" value="创建对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-79" target="fI-K-m2AIIzt_EUsSEGo-82" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-110" value="位置更新" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-84" target="fI-K-m2AIIzt_EUsSEGo-85" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-111" value="可见性计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-85" target="fI-K-m2AIIzt_EUsSEGo-86" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-112" value="地面可见性" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-86" target="fI-K-m2AIIzt_EUsSEGo-87" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-113" value="覆盖分析" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-87" target="fI-K-m2AIIzt_EUsSEGo-88" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-114" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-86" target="fI-K-m2AIIzt_EUsSEGo-93" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-880" y="1510" />
              <mxPoint x="-983" y="1510" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-115" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-87" target="fI-K-m2AIIzt_EUsSEGo-91" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-573" y="1490" />
              <mxPoint x="-782" y="1490" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-116" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-85" target="fI-K-m2AIIzt_EUsSEGo-92" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1143" y="1500" />
              <mxPoint x="-1203" y="1500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-117" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-88" target="fI-K-m2AIIzt_EUsSEGo-90" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-293" y="1500" />
              <mxPoint x="-533" y="1500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-118" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-93" target="fI-K-m2AIIzt_EUsSEGo-95" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1075" y="1720" />
              <mxPoint x="-1075" y="1720" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-119" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-91" target="fI-K-m2AIIzt_EUsSEGo-95" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-763" y="1690" />
              <mxPoint x="-1075" y="1690" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-120" value="主数据流&#xa;(1000次循环)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="fI-K-m2AIIzt_EUsSEGo-79" target="fI-K-m2AIIzt_EUsSEGo-84" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-880" y="1060" />
              <mxPoint x="-1393" y="1060" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-137" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="fI-K-m2AIIzt_EUsSEGo-130" target="fI-K-m2AIIzt_EUsSEGo-131" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-480" y="1900" />
              <mxPoint x="-480" y="1900" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="fI-K-m2AIIzt_EUsSEGo-138" value="地面云中心&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="fI-K-m2AIIzt_EUsSEGo-137" vertex="1" connectable="0">
          <mxGeometry x="0.0026" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-49" value="main()&#xa;程序入口&#xa;测试和演示" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="2305" y="760" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-50" value="外部依赖层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1812.5" y="870" width="1125" height="120" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-51" value="config.yaml&#xa;通信配置文件&#xa;功率/带宽参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="80" y="50" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-52" value="orbital_updater&#xa;轨道更新模块&#xa;卫星位置/可见性" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="280" y="45" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-53" value="numpy&#xa;矩阵计算" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="480" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-54" value="yaml&#xa;配置解析" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="620" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-55" value="math&#xa;数学计算" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="760" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-56" value="typing&#xa;类型注解" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=11" parent="JceWLuwn25X_usttGbFP-50" vertex="1">
          <mxGeometry x="900" y="50" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-57" value="支持的5种链路类型" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1875" y="1700" width="1000" height="170" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-58" value="inter_satellite&#xa;卫星间链路&#xa;激光通信&#xa;50Gbps固定速率" style="rhombus;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10;fontStyle=1" parent="JceWLuwn25X_usttGbFP-57" vertex="1">
          <mxGeometry x="45" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-59" value="user_to_satellite&#xa;用户→卫星上行&#xa;p_u_w: 5W&#xa;b_us_hz: 100MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-57" vertex="1">
          <mxGeometry x="220" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-60" value="satellite_to_user&#xa;卫星→用户下行&#xa;p_su_w: 10W&#xa;b_su_hz: 120MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-57" vertex="1">
          <mxGeometry x="410" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-61" value="satellite_to_cloud&#xa;卫星→云中心下行&#xa;p_sc_w: 15W&#xa;b_sc_hz: 150MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=10" parent="JceWLuwn25X_usttGbFP-57" vertex="1">
          <mxGeometry x="610" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-62" value="cloud_to_satellite&#xa;云中心→卫星上行&#xa;p_c_w: 50W&#xa;b_cs_hz: 180MHz" style="rhombus;whiteSpace=wrap;html=1;fillColor=#5b9bd5;fontColor=#ffffff;strokeColor=#2e5c8a;fontSize=10" parent="JceWLuwn25X_usttGbFP-57" vertex="1">
          <mxGeometry x="810" y="50" width="140" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-63" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1975" y="1070" width="800" height="170" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-64" value="CommunicationManager&#xa;通信管理器&#xa;- 基于orbital_updater的可见性矩阵&#xa;- 支持5种双向链路类型计算&#xa;- 物理模型准确的链路状态计算&#xa;- 统一的链路查询接口" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=11;fontStyle=1" parent="JceWLuwn25X_usttGbFP-63" vertex="1">
          <mxGeometry x="280" y="40" width="240" height="120" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-65" value="核心接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1830" y="1320" width="1030" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-66" value="get_all_link_states()&#xa;获取所有链路状态&#xa;[主要接口]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10;fontStyle=1" parent="JceWLuwn25X_usttGbFP-65" vertex="1">
          <mxGeometry x="100" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-67" value="get_link_state()&#xa;获取特定链路状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-65" vertex="1">
          <mxGeometry x="350" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-68" value="get_neighbors()&#xa;获取节点邻居" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-65" vertex="1">
          <mxGeometry x="577" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-69" value="set_orbital_updater()&#xa;设置轨道更新器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-65" vertex="1">
          <mxGeometry x="800" y="40" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-70" value="核心物理计算层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1780" y="1580" width="1190" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-71" value="calculate_signal_strength_and_snr()&#xa;信号强度和SNR计算&#xa;[自由空间路径损耗+SNR]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10;fontStyle=1" parent="JceWLuwn25X_usttGbFP-70" vertex="1">
          <mxGeometry x="120" y="40" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-72" value="calculate_data_rate()&#xa;数据速率计算&#xa;[香农公式]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10;fontStyle=1" parent="JceWLuwn25X_usttGbFP-70" vertex="1">
          <mxGeometry x="400" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-73" value="calculate_transmission_delay()&#xa;传输延迟计算&#xa;[传播+传输+处理延迟]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="JceWLuwn25X_usttGbFP-70" vertex="1">
          <mxGeometry x="690" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-74" value="calculate_transmission_energy()&#xa;传输能耗计算&#xa;[功率×时间模型]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f4b942;fontColor=#ffffff;strokeColor=#f4b942;fontSize=10" parent="JceWLuwn25X_usttGbFP-70" vertex="1">
          <mxGeometry x="930" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-75" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-49" target="JceWLuwn25X_usttGbFP-64" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-76" value="数据依赖" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-52" target="JceWLuwn25X_usttGbFP-64" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2180" y="1050" />
              <mxPoint x="2360" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-77" value="主要接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="JceWLuwn25X_usttGbFP-64" target="JceWLuwn25X_usttGbFP-66" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2030.0769230769233" y="1290" as="targetPoint" />
            <Array as="points">
              <mxPoint x="2375" y="1275" />
              <mxPoint x="2030" y="1275" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-78" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-67" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-79" value="调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-68" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2030" y="1460" />
              <mxPoint x="2425" y="1460" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-80" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-71" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2030" y="1550" />
              <mxPoint x="2030" y="1550" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-81" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2030" y="1440" />
              <mxPoint x="2260" y="1440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-82" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-73" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2030" y="1490" />
              <mxPoint x="2570" y="1490" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-83" value="物理计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-66" target="JceWLuwn25X_usttGbFP-74" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2030" y="1530" />
              <mxPoint x="2790" y="1530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-84" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1680" y="1050" width="200" height="200" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-85" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="40" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-86" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="65" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-87" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="90" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-88" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="115" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-89" value="绿色/紫色：链路类型" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="140" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-90" value="橙色：核心接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-84" vertex="1">
          <mxGeometry x="10" y="165" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-92" value="main()&#xa;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="2749.8" y="-1360" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-93" value="管理控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2369.8" y="-1240" width="900" height="190" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-94" value="TaskLoader&#xa;任务数据加载器&#xa;- JSON数据解析&#xa;- 时隙任务提取&#xa;- Task实例创建" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="JceWLuwn25X_usttGbFP-93" vertex="1">
          <mxGeometry x="150" y="50" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-95" value="TaskManager&#xa;任务管理器&#xa;- 活跃任务管理&#xa;- 状态跟踪&#xa;- 调度器接口&#xa;- 系统统计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="JceWLuwn25X_usttGbFP-93" vertex="1">
          <mxGeometry x="450" y="50" width="200" height="110" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-96" value="数据模型层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2209.8" y="-830" width="1150" height="200" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-97" value="TaskState&#xa;任务状态枚举&#xa;- GENERATED&#xa;- QUEUED&#xa;- PROCESSING&#xa;- TRANSFERRING&#xa;- RETRYING&#xa;- RETURNING&#xa;- COMPLETED&#xa;- FAILED" style="rhombus;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-96" vertex="1">
          <mxGeometry x="30" y="30" width="160" height="160" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-98" value="Task&#xa;任务实体核心类&#xa;- task_id: str&#xa;- source_location_id: int&#xa;- data_size_mb: float&#xa;- complexity_cycles_per_bit: int&#xa;- deadline_timestamp: float&#xa;- priority: int&#xa;- state: TaskState&#xa;- processing_records: List&#xa;- transfer_records: List&#xa;- retry_record: RetryRecord" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-96" vertex="1">
          <mxGeometry x="220" y="40" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-99" value="ProcessingRecord&#xa;@dataclass&#xa;处理记录&#xa;- satellite_id: str&#xa;- start_time: float&#xa;- end_time: float&#xa;- cpu_cycles_processed: int&#xa;- energy_consumed: float&#xa;- completion_ratio: float&#xa;- is_partial: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-96" vertex="1">
          <mxGeometry x="490" y="40" width="180" height="150" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-100" value="TransferRecord&#xa;@dataclass&#xa;传输记录&#xa;- from_satellite_id: Optional[str]&#xa;- to_satellite_id: Optional[str]&#xa;- transfer_time: float&#xa;- transfer_energy: float&#xa;- success: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-96" vertex="1">
          <mxGeometry x="700" y="40" width="180" height="150" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-101" value="RetryRecord&#xa;@dataclass&#xa;重试记录&#xa;- retry_count: int = 0&#xa;- last_retry_time: float = 0.0&#xa;- retry_reason: str = &quot;&quot;&#xa;- max_retries: int = 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" parent="JceWLuwn25X_usttGbFP-96" vertex="1">
          <mxGeometry x="930" y="40" width="150" height="150" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-102" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="1961.8" y="-530" width="1690" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-103" value="load_tasks_for_timeslot()&#xa;按时隙加载任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="68" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-104" value="update_state()&#xa;任务状态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="318" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-105" value="calculate_dynamic_priority()&#xa;动态优先级计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="568" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-106" value="start_processing()&#xa;开始任务处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="818" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-107" value="transfer_to_satellite()&#xa;卫星间传输" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="1058" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-108" value="attempt_retry()&#xa;重试尝试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="1308" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-109" value="complete_processing()&#xa;完成处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-102" vertex="1">
          <mxGeometry x="1518" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-110" value="业务服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2206.8" y="-310" width="1200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-111" value="get_high_priority_tasks()&#xa;高优先级任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="50" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-112" value="get_tasks_by_satellite()&#xa;按卫星分组查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="250" y="40" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-113" value="export_tasks_for_scheduling()&#xa;调度器接口导出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="450" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-114" value="get_overdue_tasks()&#xa;超期任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="670" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-115" value="get_tasks_by_state()&#xa;按状态查询任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="850" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-116" value="get_system_statistics()&#xa;系统统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="JceWLuwn25X_usttGbFP-110" vertex="1">
          <mxGeometry x="1030" y="40" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-117" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2306.8" y="-40" width="1000" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-118" value="_load_config()&#xa;配置文件加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="JceWLuwn25X_usttGbFP-117" vertex="1">
          <mxGeometry x="63" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-119" value="to_dict()&#xa;字典格式转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="JceWLuwn25X_usttGbFP-117" vertex="1">
          <mxGeometry x="293" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-120" value="get_statistics()&#xa;任务统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="JceWLuwn25X_usttGbFP-117" vertex="1">
          <mxGeometry x="573" y="40" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-121" value="get_resource_requirements()&#xa;资源需求查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="JceWLuwn25X_usttGbFP-117" vertex="1">
          <mxGeometry x="803" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-122" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2349.8" y="-1480" width="900" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-123" value="w_priority&#xa;优先级权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="50" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-124" value="w_urgency&#xa;紧迫性权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="180" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-125" value="w_cost&#xa;成本权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="310" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-126" value="f_leo_hz&#xa;LEO计算频率" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="440" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-127" value="max_retries&#xa;最大重试次数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="570" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-128" value="epsilon_urgency&#xa;紧迫性防零参数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="JceWLuwn25X_usttGbFP-122" vertex="1">
          <mxGeometry x="700" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-129" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-92" target="JceWLuwn25X_usttGbFP-95" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2906.8" y="-1330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-130" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="JceWLuwn25X_usttGbFP-159" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2919.8" y="205" as="sourcePoint" />
            <mxPoint x="2429.8" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="2869.8" y="205" />
              <mxPoint x="2869.8" y="100" />
              <mxPoint x="2429.8" y="100" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-131" value="创建任务对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-94" target="JceWLuwn25X_usttGbFP-98" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2509.8" y="-1150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-132" value="管理任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-95" target="JceWLuwn25X_usttGbFP-98" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2879.8" y="-970" />
              <mxPoint x="2589.8" y="-970" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-133" value="记录处理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-98" target="JceWLuwn25X_usttGbFP-99" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-134" value="记录传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-98" target="JceWLuwn25X_usttGbFP-100" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2529.8" y="-580" />
              <mxPoint x="2999.8" y="-580" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-135" value="记录重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-98" target="JceWLuwn25X_usttGbFP-101" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2579.8" y="-550" />
              <mxPoint x="3194.8" y="-550" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-136" value="主要数据流&#xa;(任务生命周期)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="JceWLuwn25X_usttGbFP-94" target="JceWLuwn25X_usttGbFP-103" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2479.8" y="-1150" />
              <mxPoint x="2479.8" y="-980" />
              <mxPoint x="2099.8" y="-980" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-137" value="状态管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-103" target="JceWLuwn25X_usttGbFP-104" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-138" value="优先级计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-104" target="JceWLuwn25X_usttGbFP-105" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-139" value="处理决策" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-105" target="JceWLuwn25X_usttGbFP-106" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-140" value="任务传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-106" target="JceWLuwn25X_usttGbFP-107" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-141" value="失败重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-107" target="JceWLuwn25X_usttGbFP-108" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-142" value="任务完成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-108" target="JceWLuwn25X_usttGbFP-109" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-143" value="查询调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-95" target="JceWLuwn25X_usttGbFP-111" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2879.8" y="-1270" />
              <mxPoint x="2289.8" y="-1270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-144" value="分组查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-95" target="JceWLuwn25X_usttGbFP-112" edge="1">
          <mxGeometry x="-0.6566" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2919.8" y="-380" />
              <mxPoint x="2536.8" y="-380" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-145" value="调度接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1" parent="1" source="JceWLuwn25X_usttGbFP-95" target="JceWLuwn25X_usttGbFP-113" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2919.8" y="-970" />
              <mxPoint x="3669.8" y="-970" />
              <mxPoint x="3669.8" y="-360" />
              <mxPoint x="2746.8" y="-360" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-146" value="统计查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-95" target="JceWLuwn25X_usttGbFP-116" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="3309.8" y="-1160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-147" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-105" target="JceWLuwn25X_usttGbFP-121" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2629.8" y="-180" />
              <mxPoint x="3189.8" y="-180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-148" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="JceWLuwn25X_usttGbFP-98" target="JceWLuwn25X_usttGbFP-120" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1889.8" y="-875" />
              <mxPoint x="1889.8" y="-150" />
              <mxPoint x="2949.8" y="-150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-149" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2029.8" y="-1430" width="200" height="220" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-150" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="40" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-151" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="65" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-152" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="90" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-153" value="深蓝色：管理控制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="115" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-154" value="绿色：数据实体" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="140" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-155" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="165" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-156" value="紫色：配置参数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="JceWLuwn25X_usttGbFP-149" vertex="1">
          <mxGeometry x="10" y="190" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-157" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
          <mxGeometry x="2426.8" y="140" width="760" height="100" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-158" value="task_generation_results.json&#xa;预生成任务数据&#xa;包含1000时隙任务" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="JceWLuwn25X_usttGbFP-157" vertex="1">
          <mxGeometry x="80" y="40" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-159" value="config.yaml&#xa;系统配置文件&#xa;排队/计算/通信参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="JceWLuwn25X_usttGbFP-157" vertex="1">
          <mxGeometry x="473" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="JceWLuwn25X_usttGbFP-160" value="数据读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="JceWLuwn25X_usttGbFP-158" target="JceWLuwn25X_usttGbFP-94" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2386.69" y="180" as="sourcePoint" />
            <mxPoint x="2276.69" y="-1080" as="targetPoint" />
            <Array as="points">
              <mxPoint x="2559.8" y="180" />
              <mxPoint x="2559.8" y="-105" />
              <mxPoint x="1819.8" y="-105" />
              <mxPoint x="1819.8" y="-1150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-1" value="main()&lt;br&gt;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="-950" y="-1540" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-2" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1490" width="1200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-3" value="config.yaml&#xa;系统配置文件&#xa;- 轨道参数&#xa;- 能量参数&#xa;- 通信参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-2">
          <mxGeometry x="50" y="35" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-4" value="orbital_updater数据&#xa;轨道状态同步" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-2">
          <mxGeometry x="300" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-5" value="communication数据&#xa;网络状态同步" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-2">
          <mxGeometry x="520" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-6" value="task数据&#xa;任务输入" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-2">
          <mxGeometry x="740" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-7" value="task_generation_results.json&#xa;任务生成结果" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-2">
          <mxGeometry x="960" y="40" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-8" value="核心控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1240" y="-1430" width="700" height="140" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-9" value="SatelliteNode&#xa;卫星节点核心类&#xa;- 状态管理&#xa;- 任务调度&#xa;- 资源协调" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-8">
          <mxGeometry x="250" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-10" value="卫星状态数据层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1570" y="-1230" width="1400" height="200" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-11" value="Position&#xa;位置状态&#xa;- latitude/longitude&#xa;- altitude&#xa;- timestamp&#xa;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="50" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-12" value="EnergyState&#xa;能量状态&#xa;- current_battery_j&#xa;- battery_capacity_j&#xa;- solar_power_w&#xa;- base_power_consumption_w&#xa;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="250" y="40" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-13" value="ResourceState&#xa;计算资源状态&#xa;- cpu_frequency_hz&#xa;- available_cpu_hz&#xa;- memory_total_mb&#xa;- memory_used_mb" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="480" y="40" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-14" value="CommunicationState&#xa;通信状态&#xa;- visible_neighbors&#xa;- visible_ground_stations&#xa;- active_links" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="680" y="50" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-15" value="PerformanceMetrics&#xa;性能指标&#xa;- total_tasks_received&#xa;- total_tasks_completed&#xa;- total_tasks_failed&#xa;- completion_rate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="900" y="40" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-16" value="SatelliteStatus&#xa;卫星状态枚举&#xa;- ACTIVE&#xa;- INACTIVE&#xa;- FAILED&#xa;- MAINTENANCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-10">
          <mxGeometry x="1130" y="50" width="140" height="100" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-17" value="任务管理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1490" y="-980" width="1200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-18" value="receive_task()&#xa;接收新任务&#xa;- 队列容量检查&#xa;- 卫星状态检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-17">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-19" value="schedule_next_task()&#xa;调度下一个任务&#xa;- 优先级调度&#xa;- 超时检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-17">
          <mxGeometry x="250" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-20" value="update_processing()&#xa;更新任务处理&#xa;- 处理进度&#xa;- 完成检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-17">
          <mxGeometry x="450" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-21" value="offload_task()&#xa;任务卸载&#xa;- 邻居检查&#xa;- 传输成本计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-17">
          <mxGeometry x="650" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-22" value="get_best_offload_target()&#xa;选择最佳卸载目标&#xa;- 邻居选择&#xa;- 负载评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-17">
          <mxGeometry x="850" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-23" value="状态更新层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1490" y="-800" width="1200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-24" value="update_energy()&#xa;能量更新&#xa;- 太阳能充电&#xa;- 基础功耗&#xa;- 处理功耗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-23">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-25" value="update_connectivity()&#xa;连接状态更新&#xa;- 邻居列表&#xa;- 地面站列表" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-23">
          <mxGeometry x="250" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-26" value="step()&#xa;时间步执行&#xa;- 状态更新&#xa;- 任务调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-23">
          <mxGeometry x="450" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-27" value="sync_with_orbital_state()&#xa;轨道状态同步&#xa;- 位置同步&#xa;- 光照状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-23">
          <mxGeometry x="650" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-28" value="sync_with_communication_state()&#xa;网络状态同步&#xa;- 邻居更新&#xa;- 连接状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-23">
          <mxGeometry x="880" y="40" width="190" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-29" value="数据导出层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1390" y="-620" width="1000" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-30" value="get_status_summary()&#xa;状态摘要&#xa;- 位置信息&#xa;- 能量状态&#xa;- 任务统计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-29">
          <mxGeometry x="50" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-31" value="export_for_scheduling()&#xa;调度数据导出&#xa;- 任务接收能力&#xa;- 资源状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-29">
          <mxGeometry x="250" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-32" value="export_observation_data()&#xa;观测数据导出&#xa;- 强化学习观测&#xa;- 完整状态数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-29">
          <mxGeometry x="460" y="40" width="170" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-33" value="is_healthy()&#xa;健康检查&#xa;- 状态检查&#xa;- 电量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-29">
          <mxGeometry x="680" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-34" value="can_accept_tasks()&#xa;任务接收检查&#xa;- 健康状态&#xa;- 队列容量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-29">
          <mxGeometry x="860" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-35" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1390" y="-440" width="1000" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-36" value="_load_config()&#xa;配置加载&#xa;- 配置文件读取&#xa;- 默认配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-35">
          <mxGeometry x="50" y="40" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-37" value="_get_default_config()&#xa;默认配置获取&#xa;- 系统参数&#xa;- 计算参数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-35">
          <mxGeometry x="240" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-38" value="_initialize_from_config()&#xa;配置初始化&#xa;- 参数设置&#xa;- 状态初始化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-35">
          <mxGeometry x="440" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-39" value="_complete_current_task()&#xa;任务完成处理&#xa;- 统计更新&#xa;- 状态清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-35">
          <mxGeometry x="650" y="40" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-40" value="_fail_current_task()&#xa;任务失败处理&#xa;- 失败统计&#xa;- 状态清理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-35">
          <mxGeometry x="850" y="40" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-41" value="外部模块接口层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1290" y="-260" width="800" height="120" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-42" value="set_external_modules()&#xa;设置外部模块&#xa;- OrbitalUpdater接口&#xa;- CommunicationManager接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-41">
          <mxGeometry x="50" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-43" value="sync_with_task_state()&#xa;任务状态同步&#xa;- 新任务接收&#xa;- 任务状态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-41">
          <mxGeometry x="280" y="40" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-44" value="orbital_updater&#xa;轨道更新器引用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-41">
          <mxGeometry x="500" y="50" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-45" value="comm_manager&#xa;通信管理器引用" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-41">
          <mxGeometry x="670" y="50" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-46" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-1" target="L4zUCvBfHY8NB0LTNxBj-9">
          <mxGeometry x="-0.5556" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-47" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-9" target="L4zUCvBfHY8NB0LTNxBj-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-48" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-9" target="L4zUCvBfHY8NB0LTNxBj-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-890" y="-1270" />
              <mxPoint x="-1235" y="-1270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-49" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-9" target="L4zUCvBfHY8NB0LTNxBj-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-50" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-18" target="L4zUCvBfHY8NB0LTNxBj-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-51" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-19" target="L4zUCvBfHY8NB0LTNxBj-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-52" value="Offload decision" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-20" target="L4zUCvBfHY8NB0LTNxBj-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-53" value="State update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-26" target="L4zUCvBfHY8NB0LTNxBj-24">
          <mxGeometry x="-0.2" y="15" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-965" y="-650" />
              <mxPoint x="-1330" y="-650" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-54" value="Processing update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-26" target="L4zUCvBfHY8NB0LTNxBj-20">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-965" y="-815" />
              <mxPoint x="-1010" y="-815" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-55" value="Orbital data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-4" target="L4zUCvBfHY8NB0LTNxBj-27">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1110" y="-60" />
              <mxPoint x="-755" y="-60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-56" value="Communication data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-5" target="L4zUCvBfHY8NB0LTNxBj-28">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-890" y="-30" />
              <mxPoint x="-190" y="-30" />
              <mxPoint x="-190" y="-725" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-57" value="Task input" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-6" target="L4zUCvBfHY8NB0LTNxBj-18">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-670" y="190" />
              <mxPoint x="-1550" y="190" />
              <mxPoint x="-1550" y="-905" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-58" value="Config read" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-3" target="L4zUCvBfHY8NB0LTNxBj-36">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1420" y="-365" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-59" value="Initialize" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-36" target="L4zUCvBfHY8NB0LTNxBj-38">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1270" y="-460" />
              <mxPoint x="-870" y="-460" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-60" value="Main data flow&#xa;(Time step loop)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" edge="1" parent="1" source="L4zUCvBfHY8NB0LTNxBj-9" target="L4zUCvBfHY8NB0LTNxBj-26">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-930" y="-1200" />
              <mxPoint x="-930" y="-1200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-61" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1670" y="-1540" width="200" height="220" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-62" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="40" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-63" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="65" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-64" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="90" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-65" value="深蓝色：主控制器" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="115" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-66" value="绿色：数据状态" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="140" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-67" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="165" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="L4zUCvBfHY8NB0LTNxBj-68" value="紫色：外部接口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="L4zUCvBfHY8NB0LTNxBj-61">
          <mxGeometry x="10" y="190" width="120" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
