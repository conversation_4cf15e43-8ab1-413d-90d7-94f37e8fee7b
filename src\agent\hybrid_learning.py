"""
混合学习算法
实现PPO强化学习与知识蒸馏的混合优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import deque
import logging

from .transformer_scheduler import TransformerScheduler
from .strategy_domain import StrategyDomain

logger = logging.getLogger(__name__)


class HybridLearningAgent:
    """混合学习智能体 - 结合RL和知识蒸馏"""
    
    def __init__(self,
                 agent_id: int,
                 obs_dim: int = 15,
                 task_feature_dim: int = 8,
                 hidden_dim: int = 256,
                 lr_actor: float = 3e-4,
                 lr_critic: float = 1e-3,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_ratio: float = 0.2,
                 kl_coef: float = 0.5,
                 temperature: float = 3.0):
        
        self.agent_id = agent_id
        self.obs_dim = obs_dim
        self.task_feature_dim = task_feature_dim
        
        # 超参数
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.kl_coef = kl_coef  # 知识蒸馏系数
        self.temperature = temperature  # 蒸馏温度
        
        # 策略网络 (Actor) - 使用Transformer调度器
        self.actor = TransformerScheduler(
            obs_dim=obs_dim,
            task_feature_dim=task_feature_dim,
            d_model=hidden_dim,
            num_actions=42  # 本地(1) + 卫星(36) + 云(5)
        )
        
        # 价值网络 (Critic)
        self.critic = self._build_critic_network(hidden_dim)
        
        # 优化器
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=lr_actor)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=lr_critic)
        
        # 经验缓冲区
        self.memory = []
        
        # 自适应平衡因子
        self.adaptive_lambda = 0.5  # 初始值
        self.performance_history = deque(maxlen=100)
        
    def _build_critic_network(self, hidden_dim: int) -> nn.Module:
        """构建价值网络"""
        return nn.Sequential(
            nn.Linear(self.obs_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, 1)
        )
    
    def select_actions(self, observation: np.ndarray, task_queue: List[Dict],
                      valid_actions_per_task: List[List[int]]) -> Tuple[List[int], Dict]:
        """选择动作序列"""
        if not task_queue:
            return [], {}
            
        # 转换为张量
        obs_tensor = torch.FloatTensor(observation).unsqueeze(0)
        
        # 提取任务特征
        task_features = []
        for task in task_queue:
            features = self._extract_task_features(task)
            task_features.append(features)
        task_tensor = torch.FloatTensor(task_features).unsqueeze(0)
        
        # 构建动作掩码
        num_tasks = len(task_queue)
        action_mask = torch.zeros(1, num_tasks, 42, dtype=torch.bool)
        for i, valid_actions in enumerate(valid_actions_per_task):
            for action in valid_actions:
                action_mask[0, i, action] = True
        
        # 获取动作概率
        with torch.no_grad():
            action_probs = self.actor(obs_tensor, task_tensor, action_mask)
            value = self.critic(obs_tensor)
        
        # 采样动作
        actions = []
        log_probs = []
        
        for i in range(num_tasks):
            probs = action_probs[0, i]
            dist = torch.distributions.Categorical(probs)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            
            actions.append(action.item())
            log_probs.append(log_prob.item())
        
        info = {
            'value': value.item(),
            'log_probs': log_probs,
            'action_probs': action_probs[0].cpu().numpy()
        }
        
        return actions, info
    
    def store_transition(self, observation: np.ndarray, task_features: List[np.ndarray],
                        actions: List[int], rewards: float, next_observation: np.ndarray,
                        done: bool, info: Dict):
        """存储经验"""
        self.memory.append({
            'observation': observation,
            'task_features': task_features,
            'actions': actions,
            'rewards': rewards,
            'next_observation': next_observation,
            'done': done,
            'log_probs': info.get('log_probs', []),
            'value': info.get('value', 0)
        })
    
    def update(self, domain_policy: Optional[StrategyDomain] = None) -> Dict[str, float]:
        """更新策略 - 修复损失函数组合错误"""
        if len(self.memory) < 32:  # 最小批次大小
            return {}
            
        # 准备训练数据
        batch = self._prepare_batch()
        
        # 计算优势和回报
        advantages, returns = self._compute_gae(batch)
        
        # 统一计算所有损失
        ppo_actor_loss, critic_loss, distill_loss = self._calculate_losses(
            batch, advantages, returns, domain_policy
        )
        
        # Actor损失组合 - PPO损失和蒸馏损失加权求和
        combined_actor_loss = (1 - self.adaptive_lambda) * ppo_actor_loss + \
                             self.adaptive_lambda * distill_loss
        
        # Actor更新 - 只进行一次反向传播和优化
        self.actor_optimizer.zero_grad()
        combined_actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 0.5)
        self.actor_optimizer.step()
        
        # Critic更新 - 独立进行一次反向传播和优化
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 0.5)
        self.critic_optimizer.step()
        
        # 总损失（用于监控）
        total_loss = combined_actor_loss + critic_loss
        
        # 更新自适应平衡因子
        self._update_adaptive_lambda()
        
        # 清空经验缓冲
        self.memory.clear()
        
        return {
            'ppo_actor_loss': ppo_actor_loss.item(),
            'critic_loss': critic_loss.item(),
            'distill_loss': distill_loss.item() if torch.is_tensor(distill_loss) else distill_loss,
            'combined_actor_loss': combined_actor_loss.item(),
            'total_loss': total_loss.item(),
            'adaptive_lambda': self.adaptive_lambda
        }
    
    def _prepare_batch(self) -> Dict[str, torch.Tensor]:
        """准备批次数据"""
        observations = []
        task_features = []
        actions = []
        rewards = []
        values = []
        log_probs = []
        dones = []
        
        for exp in self.memory:
            observations.append(exp['observation'])
            task_features.append(exp['task_features'])
            actions.append(exp['actions'])
            rewards.append(exp['rewards'])
            values.append(exp['value'])
            log_probs.append(exp['log_probs'])
            dones.append(exp['done'])
        
        # 处理变长任务序列的padding
        padded_tasks, padded_actions, padded_log_probs, task_mask = self._pad_and_stack_tasks(
            task_features, actions, log_probs
        )
        
        return {
            'observations': torch.FloatTensor(observations),
            'task_features': padded_tasks,
            'actions': padded_actions,
            'rewards': torch.FloatTensor(rewards),
            'values': torch.FloatTensor(values),
            'log_probs': padded_log_probs,
            'dones': torch.FloatTensor(dones),
            'task_mask': task_mask
        }
    
    def _pad_and_stack_tasks(self, task_features_list: List[List[np.ndarray]], 
                           actions_list: List[List[int]], 
                           log_probs_list: List[List[float]]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """对变长任务序列进行padding并堆叠"""
        batch_size = len(task_features_list)
        max_seq_len = max(len(seq) for seq in task_features_list) if task_features_list else 1
        
        # 初始化padded张量
        padded_tasks = torch.zeros(batch_size, max_seq_len, self.task_feature_dim)
        padded_actions = torch.zeros(batch_size, max_seq_len, dtype=torch.long)
        padded_log_probs = torch.zeros(batch_size, max_seq_len)
        task_mask = torch.zeros(batch_size, max_seq_len, dtype=torch.bool)
        
        for i, (task_seq, action_seq, log_prob_seq) in enumerate(zip(task_features_list, actions_list, log_probs_list)):
            seq_len = len(task_seq)
            if seq_len > 0:
                # 填充任务特征
                for j, task_feat in enumerate(task_seq):
                    padded_tasks[i, j] = torch.FloatTensor(task_feat)
                
                # 填充动作
                for j, action in enumerate(action_seq):
                    padded_actions[i, j] = action
                
                # 填充log概率
                for j, log_prob in enumerate(log_prob_seq):
                    padded_log_probs[i, j] = log_prob
                
                # 设置掩码（True表示有效位置）
                task_mask[i, :seq_len] = True
        
        return padded_tasks, padded_actions, padded_log_probs, task_mask
    
    def _compute_gae(self, batch: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算广义优势估计(GAE)"""
        rewards = batch['rewards']
        values = batch['values']
        dones = batch['dones']
        
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        gae = 0
        next_value = 0
        
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_value = 0
            else:
                next_value = values[t + 1]
                
            delta = rewards[t] + self.gamma * next_value * (1 - dones[t]) - values[t]
            gae = delta + self.gamma * self.gae_lambda * (1 - dones[t]) * gae
            
            advantages[t] = gae
            returns[t] = advantages[t] + values[t]
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return advantages, returns
    
    def _calculate_losses(self, batch: Dict, advantages: torch.Tensor, 
                         returns: torch.Tensor, domain_policy: Optional[StrategyDomain] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """统一计算PPO和知识蒸馏损失 - 修复架构失配和向量化处理"""
        observations = batch['observations']  # [batch_size, obs_dim]
        task_features = batch['task_features']  # [batch_size, max_seq_len, task_feature_dim]
        actions = batch['actions']  # [batch_size, max_seq_len]
        old_log_probs = batch['log_probs']  # [batch_size, max_seq_len]
        task_mask = batch['task_mask']  # [batch_size, max_seq_len]
        
        batch_size, max_seq_len = task_features.shape[:2]
        
        # 1. 前向传播获取新的动作概率
        action_probs = self.actor(observations, task_features)  # [batch_size, max_seq_len, num_actions]
        
        # 2. 计算PPO损失 - 向量化实现
        ppo_actor_loss = self._compute_ppo_loss(action_probs, actions, old_log_probs, advantages, task_mask)
        
        # 3. 计算Critic损失
        values = self.critic(observations).squeeze(-1)  # [batch_size]
        critic_loss = F.mse_loss(values, returns)
        
        # 4. 计算知识蒸馏损失（如果有域策略）
        distill_loss = torch.tensor(0.0, device=action_probs.device)
        if domain_policy is not None:
            distill_loss = self._compute_distillation_loss(
                observations, task_features, action_probs, task_mask, domain_policy
            )
        
        return ppo_actor_loss, critic_loss, distill_loss
    
    def _compute_ppo_loss(self, action_probs: torch.Tensor, actions: torch.Tensor,
                         old_log_probs: torch.Tensor, advantages: torch.Tensor, 
                         task_mask: torch.Tensor) -> torch.Tensor:
        """计算向量化的PPO损失 - 修正序列概率计算"""
        batch_size, max_seq_len = actions.shape
        
        # 为每个动作创建分布并计算log概率
        action_dists = torch.distributions.Categorical(action_probs)
        new_log_probs = action_dists.log_prob(actions)  # [batch_size, max_seq_len]
        
        # 应用掩码，只考虑有效的任务位置
        new_log_probs = new_log_probs * task_mask.float()
        old_log_probs = old_log_probs * task_mask.float()
        
        # 计算序列的联合对数概率 - 使用sum而不是mean
        new_log_probs_seq = new_log_probs.sum(dim=1)  # [batch_size]
        old_log_probs_seq = old_log_probs.sum(dim=1)  # [batch_size]
        
        # 计算概率比率
        ratio = torch.exp(new_log_probs_seq - old_log_probs_seq)  # [batch_size]
        
        # PPO截断目标
        clipped_ratio = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio)
        
        # 计算损失（取负数因为我们要最大化期望回报）
        surrogate1 = ratio * advantages
        surrogate2 = clipped_ratio * advantages
        ppo_loss = -torch.min(surrogate1, surrogate2).mean()
        
        return ppo_loss
    
    def _compute_distillation_loss(self, observations: torch.Tensor, task_features: torch.Tensor,
                                  student_action_probs: torch.Tensor, task_mask: torch.Tensor,
                                  domain_policy: StrategyDomain) -> torch.Tensor:
        """计算知识蒸馏损失 - 完全向量化，移除性能瓶颈"""
        
        with torch.no_grad():
            # 直接使用批处理张量接口获取教师网络输出 - 无需循环和数据转换
            teacher_action_probs = domain_policy.get_action_probs(
                observations, task_features, task_mask
            )
        
        # 只对有效位置计算KL散度
        valid_positions = task_mask.unsqueeze(-1).expand_as(student_action_probs)
        
        # 温度缩放的softmax
        student_log_probs = F.log_softmax(
            torch.log(student_action_probs + 1e-8) / self.temperature, dim=-1
        )
        teacher_probs_scaled = F.softmax(
            torch.log(teacher_action_probs + 1e-8) / self.temperature, dim=-1
        )
        
        # 计算KL散度损失，只对有效位置
        kl_div = F.kl_div(
            student_log_probs, teacher_probs_scaled, reduction='none'
        )  # [batch_size, max_seq_len, num_actions]
        
        # 应用掩码并求平均
        masked_kl = kl_div * valid_positions.float()
        distill_loss = masked_kl.sum() / valid_positions.sum()
        
        return distill_loss
    
    
    def _update_adaptive_lambda(self):
        """更新自适应平衡因子"""
        if len(self.performance_history) < 10:
            return
            
        # 基于性能趋势调整lambda
        recent_perf = list(self.performance_history)[-10:]
        perf_trend = np.polyfit(range(10), recent_perf, 1)[0]
        
        # 如果性能下降，增加对域策略的依赖
        if perf_trend < 0:
            self.adaptive_lambda = min(0.8, self.adaptive_lambda + 0.05)
        else:
            self.adaptive_lambda = max(0.2, self.adaptive_lambda - 0.05)
    
    def update_performance(self, reward: float):
        """更新性能历史"""
        self.performance_history.append(reward)
    
    def _extract_task_features(self, task: Dict) -> np.ndarray:
        """提取任务特征（与TransformerScheduler保持一致）"""
        features = np.zeros(self.task_feature_dim)
        
        features[0] = task.get('type_id', 0) / 5.0
        features[1] = min(task.get('data_size_mb', 0) / 100.0, 1.0)
        features[2] = min(task.get('complexity_cycles_per_bit', 100) / 1000.0, 1.0)
        features[3] = task.get('priority', 1) / 3.0
        
        deadline = task.get('deadline_timestamp', float('inf'))
        current_time = task.get('current_time', 0)
        urgency = max(0, min(1, (deadline - current_time) / 100.0))
        features[4] = 1.0 - urgency
        
        arrival_time = task.get('arrival_time', current_time)
        wait_time = (current_time - arrival_time) / 100.0
        features[5] = min(wait_time, 1.0)
        
        features[6] = min(task.get('estimated_duration', 10.0) / 50.0, 1.0)
        features[7] = float(task.get('is_divisible', False))
        
        return features
    
    def get_policy_params(self) -> List[torch.Tensor]:
        """获取策略网络参数（用于策略域更新）"""
        return [param.clone() for param in self.actor.parameters()]