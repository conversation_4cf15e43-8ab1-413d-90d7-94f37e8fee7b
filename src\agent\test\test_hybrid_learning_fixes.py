"""
HybridLearningAgent修复验证测试
验证修复的损失函数组合、知识蒸馏架构失配、向量化PPO等问题
"""

import torch
import numpy as np
import unittest
import sys
import os
from unittest.mock import MagicMock, patch

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hybrid_learning import HybridLearningAgent
from strategy_domain import StrategyDomain


class TestHybridLearningFixes(unittest.TestCase):
    """HybridLearningAgent修复验证测试"""
    
    def setUp(self):
        """测试初始化"""
        self.agent = HybridLearningAgent(
            agent_id=0,
            obs_dim=15,
            task_feature_dim=8,
            hidden_dim=128,  # 减小模型用于测试
            lr_actor=3e-4,
            lr_critic=1e-3
        )
        
        # 创建域策略用于知识蒸馏测试
        self.domain_policy = StrategyDomain(
            domain_id=0,
            bounds=(-180, -165, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=1,
            num_decoder_layers=1,
            num_actions=42
        )
        
    def test_padding_functionality(self):
        """测试变长序列padding功能"""
        # 创建变长的任务序列
        task_features_list = [
            [np.random.randn(8), np.random.randn(8), np.random.randn(8)],  # 3个任务
            [np.random.randn(8)],  # 1个任务
            [np.random.randn(8), np.random.randn(8)]  # 2个任务
        ]
        actions_list = [
            [1, 5, 10],
            [2],
            [3, 7]
        ]
        log_probs_list = [
            [0.1, 0.2, 0.3],
            [0.4],
            [0.5, 0.6]
        ]
        
        padded_tasks, padded_actions, padded_log_probs, task_mask = self.agent._pad_and_stack_tasks(
            task_features_list, actions_list, log_probs_list
        )
        
        # 检查形状
        self.assertEqual(padded_tasks.shape, (3, 3, 8))  # [batch_size=3, max_seq_len=3, task_dim=8]
        self.assertEqual(padded_actions.shape, (3, 3))
        self.assertEqual(padded_log_probs.shape, (3, 3))
        self.assertEqual(task_mask.shape, (3, 3))
        
        # 检查掩码正确性
        expected_mask = torch.tensor([
            [True, True, True],   # 第一个样本：3个任务都有效
            [True, False, False], # 第二个样本：只有第一个任务有效
            [True, True, False]   # 第三个样本：前两个任务有效
        ])
        self.assertTrue(torch.equal(task_mask, expected_mask))
        
        # 检查动作填充正确性
        self.assertEqual(padded_actions[0, 0].item(), 1)
        self.assertEqual(padded_actions[0, 1].item(), 5)
        self.assertEqual(padded_actions[0, 2].item(), 10)
        self.assertEqual(padded_actions[1, 0].item(), 2)
        self.assertEqual(padded_actions[1, 1].item(), 0)  # padding
        
    def test_loss_calculation_consistency(self):
        """测试损失计算的一致性和正确性"""
        # 添加一些经验到memory
        for i in range(5):
            obs = np.random.randn(15)
            task_features = [np.random.randn(8) for _ in range(2)]  # 2个任务
            actions = [np.random.randint(0, 42) for _ in range(2)]
            log_probs = [np.random.randn() for _ in range(2)]
            
            self.agent.store_transition(
                observation=obs,
                task_features=task_features,
                actions=actions,
                rewards=np.random.randn(),
                next_observation=np.random.randn(15),
                done=False,
                info={'log_probs': log_probs, 'value': np.random.randn()}
            )
        
        # 准备批次并计算损失
        batch = self.agent._prepare_batch()
        advantages, returns = self.agent._compute_gae(batch)
        
        # 测试损失计算
        ppo_loss, critic_loss, distill_loss = self.agent._calculate_losses(
            batch, advantages, returns, self.domain_policy
        )
        
        # 检查损失是有限值
        self.assertTrue(torch.isfinite(ppo_loss))
        self.assertTrue(torch.isfinite(critic_loss))
        self.assertTrue(torch.isfinite(distill_loss))
        
        # 检查损失是标量
        self.assertEqual(ppo_loss.dim(), 0)
        self.assertEqual(critic_loss.dim(), 0)
        self.assertEqual(distill_loss.dim(), 0)
        
    def test_vectorized_ppo_computation(self):
        """测试向量化PPO计算的正确性"""
        batch_size, max_seq_len = 3, 4
        
        # 创建模拟数据
        action_probs = torch.rand(batch_size, max_seq_len, 42)
        action_probs = torch.softmax(action_probs, dim=-1)
        actions = torch.randint(0, 42, (batch_size, max_seq_len))
        old_log_probs = torch.randn(batch_size, max_seq_len)
        advantages = torch.randn(batch_size)
        task_mask = torch.tensor([
            [True, True, True, False],
            [True, True, False, False], 
            [True, False, False, False]
        ])
        
        # 计算PPO损失
        ppo_loss = self.agent._compute_ppo_loss(
            action_probs, actions, old_log_probs, advantages, task_mask
        )
        
        # 检查结果
        self.assertTrue(torch.isfinite(ppo_loss))
        self.assertEqual(ppo_loss.dim(), 0)
        
    def test_sequence_probability_calculation(self):
        """测试序列概率计算使用sum而不是mean"""
        batch_size, max_seq_len = 2, 3
        
        # 创建模拟数据
        action_probs = torch.rand(batch_size, max_seq_len, 42)
        action_probs = torch.softmax(action_probs, dim=-1)
        actions = torch.randint(0, 42, (batch_size, max_seq_len))
        task_mask = torch.tensor([
            [True, True, True],
            [True, True, False]
        ])
        
        # 计算log概率
        action_dists = torch.distributions.Categorical(action_probs)
        log_probs = action_dists.log_prob(actions)
        
        # 应用掩码
        masked_log_probs = log_probs * task_mask.float()
        
        # 计算序列概率 - 应该使用sum
        seq_log_probs = masked_log_probs.sum(dim=1)
        
        # 检查形状和有效性
        self.assertEqual(seq_log_probs.shape, (batch_size,))
        self.assertTrue(torch.all(torch.isfinite(seq_log_probs)))
        
        # 检查第二个序列的概率只包含前两个任务（因为第三个被掩码）
        manual_calc = log_probs[1, 0] + log_probs[1, 1]  # 只有前两个任务
        self.assertAlmostEqual(seq_log_probs[1].item(), manual_calc.item(), places=5)
        
    def test_knowledge_distillation_architecture_compatibility(self):
        """测试知识蒸馏的架构兼容性"""
        # 创建模拟数据
        observations = torch.randn(2, 15)
        task_features = torch.randn(2, 3, 8)
        student_action_probs = torch.rand(2, 3, 42)
        student_action_probs = torch.softmax(student_action_probs, dim=-1)
        task_mask = torch.tensor([
            [True, True, True],
            [True, True, False]
        ])
        
        # 计算知识蒸馏损失
        distill_loss = self.agent._compute_distillation_loss(
            observations, task_features, student_action_probs, task_mask, self.domain_policy
        )
        
        # 检查结果
        self.assertTrue(torch.isfinite(distill_loss))
        self.assertEqual(distill_loss.dim(), 0)
        self.assertGreaterEqual(distill_loss.item(), 0)  # KL散度应该非负
        
    def test_update_method_single_optimization(self):
        """测试update方法只进行一次优化器调用"""
        # 添加足够的经验
        for i in range(35):  # 超过最小批次大小32
            obs = np.random.randn(15)
            task_features = [np.random.randn(8) for _ in range(2)]
            actions = [np.random.randint(0, 42) for _ in range(2)]
            log_probs = [np.random.randn() for _ in range(2)]
            
            self.agent.store_transition(
                observation=obs,
                task_features=task_features,
                actions=actions,
                rewards=np.random.randn(),
                next_observation=np.random.randn(15),
                done=False,
                info={'log_probs': log_probs, 'value': np.random.randn()}
            )
        
        # 记录优化器调用次数
        original_actor_step = self.agent.actor_optimizer.step
        original_critic_step = self.agent.critic_optimizer.step
        actor_step_count = 0
        critic_step_count = 0
        
        def count_actor_step():
            nonlocal actor_step_count
            actor_step_count += 1
            return original_actor_step()
            
        def count_critic_step():
            nonlocal critic_step_count
            critic_step_count += 1
            return original_critic_step()
        
        self.agent.actor_optimizer.step = count_actor_step
        self.agent.critic_optimizer.step = count_critic_step
        
        # 执行更新
        result = self.agent.update(self.domain_policy)
        
        # 检查优化器只被调用一次
        self.assertEqual(actor_step_count, 1, "Actor optimizer should be called exactly once")
        self.assertEqual(critic_step_count, 1, "Critic optimizer should be called exactly once")
        
        # 检查返回值
        self.assertIn('ppo_actor_loss', result)
        self.assertIn('critic_loss', result)
        self.assertIn('distill_loss', result)
        self.assertIn('combined_actor_loss', result)
        self.assertIn('total_loss', result)
        
        # 检查所有损失都是有限值
        for key, value in result.items():
            if key != 'adaptive_lambda':
                self.assertTrue(np.isfinite(value), f"{key} should be finite")
        
    def test_gradient_flow(self):
        """测试梯度流动的正确性"""
        # 添加经验
        for i in range(35):
            obs = np.random.randn(15)
            task_features = [np.random.randn(8) for _ in range(2)]
            actions = [np.random.randint(0, 42) for _ in range(2)]
            log_probs = [np.random.randn() for _ in range(2)]
            
            self.agent.store_transition(
                observation=obs,
                task_features=task_features,
                actions=actions,
                rewards=np.random.randn(),
                next_observation=np.random.randn(15),
                done=False,
                info={'log_probs': log_probs, 'value': np.random.randn()}
            )
        
        # 记录更新前的参数
        actor_params_before = {name: param.clone() for name, param in self.agent.actor.named_parameters()}
        critic_params_before = {name: param.clone() for name, param in self.agent.critic.named_parameters()}
        
        # 执行更新
        self.agent.update(self.domain_policy)
        
        # 检查参数确实发生了变化
        actor_changed = False
        for name, param in self.agent.actor.named_parameters():
            if not torch.equal(actor_params_before[name], param):
                actor_changed = True
                break
        
        critic_changed = False
        for name, param in self.agent.critic.named_parameters():
            if not torch.equal(critic_params_before[name], param):
                critic_changed = True
                break
        
        self.assertTrue(actor_changed, "Actor parameters should change after update")
        self.assertTrue(critic_changed, "Critic parameters should change after update")


class TestHybridLearningPerformance(unittest.TestCase):
    """HybridLearningAgent性能测试"""
    
    def setUp(self):
        """性能测试初始化"""
        self.agent = HybridLearningAgent(
            agent_id=0,
            obs_dim=15,
            task_feature_dim=8,
            hidden_dim=256
        )
        
    def test_vectorized_vs_loop_performance(self):
        """测试向量化实现相比循环的性能提升"""
        import time
        
        # 创建大批次数据
        batch_size = 100
        max_seq_len = 10
        
        observations = torch.randn(batch_size, 15)
        task_features = torch.randn(batch_size, max_seq_len, 8)
        actions = torch.randint(0, 42, (batch_size, max_seq_len))
        old_log_probs = torch.randn(batch_size, max_seq_len)
        advantages = torch.randn(batch_size)
        returns = torch.randn(batch_size)
        task_mask = torch.randint(0, 2, (batch_size, max_seq_len)).bool()
        
        batch = {
            'observations': observations,
            'task_features': task_features,
            'actions': actions,
            'log_probs': old_log_probs,
            'task_mask': task_mask
        }
        
        # 测试向量化实现的速度（包含知识蒸馏）
        start_time = time.time()
        for _ in range(10):  # 多次运行取平均
            ppo_loss, critic_loss, distill_loss = self.agent._calculate_losses(
                batch, advantages, returns, self.domain_policy
            )
        vectorized_time = (time.time() - start_time) / 10
        
        print(f"Vectorized implementation time (with distillation): {vectorized_time:.4f}s")
        
        # 基本性能检查
        self.assertLess(vectorized_time, 1.0, "Vectorized implementation should be fast")
        
    def test_batch_domain_policy_performance(self):
        """测试域策略批处理接口的性能"""
        import time
        
        batch_size = 50
        max_seq_len = 8
        
        observations = torch.randn(batch_size, 15)
        task_features = torch.randn(batch_size, max_seq_len, 8)
        task_mask = torch.randint(0, 2, (batch_size, max_seq_len)).bool()
        
        # 测试批处理接口
        start_time = time.time()
        for _ in range(20):
            teacher_probs = self.domain_policy.get_action_probs(
                observations, task_features, task_mask
            )
        batch_time = (time.time() - start_time) / 20
        
        # 测试循环接口进行对比
        start_time = time.time()
        for _ in range(20):
            for i in range(batch_size):
                obs = observations[i].numpy()
                valid_mask = task_mask[i].numpy()
                valid_tasks = task_features[i, valid_mask].numpy()
                if len(valid_tasks) > 0:
                    self.domain_policy.get_action_probs(obs, valid_tasks)
        loop_time = (time.time() - start_time) / 20
        
        print(f"Batch domain policy time: {batch_time:.4f}s")
        print(f"Loop domain policy time: {loop_time:.4f}s")
        print(f"Speedup: {loop_time/batch_time:.2f}x")
        
        # 批处理应该更快
        self.assertLess(batch_time, loop_time, "Batch processing should be faster than loops")
        
    def test_memory_efficiency(self):
        """测试内存效率 - 验证retain_graph移除的效果"""
        import gc
        import torch
        
        # 添加经验
        for i in range(35):
            obs = np.random.randn(15)
            task_features = [np.random.randn(8) for _ in range(3)]
            actions = [np.random.randint(0, 42) for _ in range(3)]
            log_probs = [np.random.randn() for _ in range(3)]
            
            self.agent.store_transition(
                observation=obs,
                task_features=task_features,
                actions=actions,
                rewards=np.random.randn(),
                next_observation=np.random.randn(15),
                done=False,
                info={'log_probs': log_probs, 'value': np.random.randn()}
            )
        
        # 记录更新前的内存使用
        gc.collect()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        initial_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # 执行更新
        result = self.agent.update(self.domain_policy)
        
        # 记录更新后的内存使用
        gc.collect()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        final_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        print(f"Memory change: {final_memory - initial_memory} bytes")
        
        # 检查更新成功
        self.assertIn('total_loss', result)
        self.assertTrue(np.isfinite(result['total_loss']))


def run_hybrid_learning_tests():
    """运行HybridLearning修复测试"""
    print("=== HybridLearningAgent修复验证测试 ===\n")
    
    test_classes = [
        TestHybridLearningFixes,
        TestHybridLearningPerformance
    ]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    
    for test_class in test_classes:
        print(f"运行 {test_class.__name__} 测试...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        total_failures += len(result.failures)
        total_errors += len(result.errors)
        
        print(f"{test_class.__name__}: {result.testsRun}个测试, {len(result.failures)}个失败, {len(result.errors)}个错误\n")
    
    # 总结
    print("=== 修复验证测试总结 ===")
    print(f"总测试数: {total_tests}")
    print(f"成功: {total_tests - total_failures - total_errors}")
    print(f"失败: {total_failures}")
    print(f"错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ 所有修复验证测试通过！")
        print("✅ 损失函数组合、知识蒸馏架构、向量化PPO均修复成功！")
    else:
        print("❌ 存在修复验证测试失败或错误")
    
    return total_failures == 0 and total_errors == 0


if __name__ == "__main__":
    run_hybrid_learning_tests()