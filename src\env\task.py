#!/usr/bin/env python3
"""
Task.py - SPACE-OAAL 任务实体核心模块

基于实际任务生成数据的任务实体实现，支持完整的任务生命周期管理、
动态优先级计算、处理历史追踪和性能监控。

Author: SPACE-OAAL Team
Date: 2025-07-18
"""

from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Optional, Any
import time
import json
import yaml
import os


class TaskState(Enum):
    """任务状态枚举"""
    GENERATED = "generated"
    QUEUED = "queued"
    PROCESSING = "processing"
    TRANSFERRING = "transferring"
    RETRYING = "retrying"
    RETURNING = "returning"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ProcessingRecord:
    """单次处理记录"""
    satellite_id: str
    start_time: float
    end_time: float
    cpu_cycles_processed: int
    energy_consumed: float
    completion_ratio: float
    is_partial: bool


@dataclass
class TransferRecord:
    """传输记录"""
    from_satellite_id: Optional[str]
    to_satellite_id: Optional[str]
    transfer_time: float
    transfer_energy: float
    success: bool


@dataclass
class RetryRecord:
    """重发记录"""
    retry_count: int = 0
    last_retry_time: float = 0.0
    retry_reason: str = ""
    max_retries: int = 2


class Task:
    """任务实体核心类"""
    
    def __init__(self, task_data: Dict, source_location_id: int, generation_timestamp: float, config: Dict):
        """
        基于任务生成数据初始化任务实例 - 修复：强制依赖注入配置
        
        Args:
            task_data: 来自task_generation_results.json的任务数据
            source_location_id: 源地面站ID
            generation_timestamp: 生成时间戳（时隙）
            config: 配置参数字典（必需参数，不能为None）
        """
        # 验证config参数
        if config is None:
            raise ValueError("config参数不能为None，必须由外部注入")
        
        # 验证task_data完整性
        required_fields = ["task_id", "type_id", "data_size_mb", "complexity_cycles_per_bit", 
                          "deadline_timestamp", "priority"]
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"任务数据缺少必需字段: {field}")
        
        # 验证数据类型和范围
        if not isinstance(source_location_id, int) or source_location_id < 0:
            raise ValueError(f"source_location_id必须是非负整数: {source_location_id}")
        
        if not isinstance(generation_timestamp, (int, float)) or generation_timestamp < 0:
            raise ValueError(f"generation_timestamp必须是非负数: {generation_timestamp}")
            
        # 从任务数据中提取基本属性
        self.task_id = task_data["task_id"]
        self.task_type = task_data["type_id"]
        self.data_size_mb = float(task_data["data_size_mb"])
        self.complexity_cycles_per_bit = int(task_data["complexity_cycles_per_bit"])
        self.deadline_timestamp = float(task_data["deadline_timestamp"])
        self.priority = int(task_data["priority"])
        
        # 验证提取的数据
        if self.data_size_mb <= 0:
            raise ValueError(f"data_size_mb必须为正数: {self.data_size_mb}")
        if self.complexity_cycles_per_bit <= 0:
            raise ValueError(f"complexity_cycles_per_bit必须为正数: {self.complexity_cycles_per_bit}")
        if self.deadline_timestamp <= generation_timestamp:
            raise ValueError(f"deadline_timestamp必须大于generation_timestamp")
        self.source_location_id = source_location_id
        self.generation_timestamp = generation_timestamp
        
        # 状态管理
        self.state = TaskState.GENERATED
        self.current_satellite_id: Optional[str] = None
        
        # 计算属性
        self.data_size_bits = self.data_size_mb * 8 * 1024 * 1024  # MB转bits
        self.total_cpu_cycles = int(self.data_size_bits * self.complexity_cycles_per_bit)
        self.processed_cpu_cycles = 0
        
        # 结果数据管理（基础版本：处理后数据量变为1/10）
        self.result_data_size_mb = None  # 处理完成后的结果大小
        self.result_data_bits = None
        self.result_generated = False
        self.result_compression_ratio = 0.1  # 基础版本固定为1/10
        
        # 处理历史
        self.processing_records: List[ProcessingRecord] = []
        self.transfer_records: List[TransferRecord] = []
        self.retry_record = RetryRecord()
        
        # 性能统计
        self.total_processing_time = 0.0
        self.total_transfer_time = 0.0
        
        # 云服务器处理相关属性
        self.processing_location = None  # 'satellite' 或 'cloud_server'
        self.processed_by = None  # 'satellite' 或 'cloud_server'
        self.processing_satellite_id = None  # 处理的卫星/云中心ID
        self.cloud_processing_record = None  # 云处理记录
        self.total_queue_time = 0.0
        self.total_energy_consumed = 0.0
        self.queue_start_time: Optional[float] = None
        
        # 配置参数 - 修复：直接使用注入的配置，不再自行加载
        self.config = config
        self._init_config_params()
    
    # 移除_load_config和_get_default_config方法 - 配置由外部注入
    
    def _init_config_params(self):
        """初始化配置参数 - 所有参数都从config.yaml读取"""
        queuing_config = self.config.get('queuing', {})
        self.w_priority = float(queuing_config.get('w_priority'))
        self.w_urgency = float(queuing_config.get('w_urgency'))  
        self.w_cost = float(queuing_config.get('w_cost'))
        self.epsilon_urgency = float(queuing_config.get('epsilon_urgency'))
        
        comp_config = self.config.get('computation', {})
        self.f_leo_hz = float(comp_config.get('f_leo_hz'))
        self.zeta_leo = float(comp_config.get('zeta_leo'))
        
        comm_config = self.config.get('communication', {})
        self.retry_record.max_retries = int(comm_config.get('max_retries'))
    
    def calculate_dynamic_priority(self, current_time: float, estimated_processing_time: float = None) -> float:
        """
        计算动态优先级分数
        Score(Ti, t_now) = w_p * f_p(Pi) + w_d * f_d(Di, t_now) - w_c * f_c(Si, Ci)
        """
        # 优先级因子
        priority_factor = self.priority
        
        # 紧迫性因子 - 修复：使用大数值而非无穷大，确保数值稳定性
        time_remaining = self.deadline_timestamp - current_time
        if time_remaining <= 0:
            urgency_factor = 1e10  # 使用大数值而非无穷大
        else:
            urgency_factor = 1.0 / (time_remaining + self.epsilon_urgency)
        
        # 成本因子（预估处理时间）
        if estimated_processing_time is None:
            remaining_cycles = self.get_remaining_cycles()
            estimated_processing_time = remaining_cycles / self.f_leo_hz
        
        cost_factor = estimated_processing_time
        
        # 最终分数
        score = (self.w_priority * priority_factor + 
                self.w_urgency * urgency_factor - 
                self.w_cost * cost_factor)
        
        return score
    
    def get_dynamic_priority(self, current_time: float) -> float:
        """获取动态优先级 - 为compatibility提供的别名方法"""
        return self.calculate_dynamic_priority(current_time)
    
    def can_complete_by_deadline(self, current_time: float, estimated_processing_time: float) -> bool:
        """检查任务是否能在截止时间前完成"""
        return (current_time + estimated_processing_time) <= self.deadline_timestamp
    
    def update_state(self, new_state: TaskState, satellite_id: Optional[str] = None, current_time: float = None) -> bool:
        """更新任务状态 - 修复：使用仿真时间而非物理时间"""
        valid_transitions = {
            TaskState.GENERATED: [TaskState.QUEUED, TaskState.RETRYING],
            TaskState.QUEUED: [TaskState.PROCESSING, TaskState.TRANSFERRING, TaskState.FAILED],
            TaskState.PROCESSING: [TaskState.TRANSFERRING, TaskState.RETURNING, TaskState.FAILED],
            TaskState.TRANSFERRING: [TaskState.QUEUED, TaskState.FAILED],
            TaskState.RETRYING: [TaskState.QUEUED, TaskState.FAILED],
            TaskState.RETURNING: [TaskState.COMPLETED, TaskState.FAILED],
            TaskState.COMPLETED: [],
            TaskState.FAILED: []
        }
        
        if new_state not in valid_transitions[self.state]:
            return False
        
        # 队列时间统计 - 修复：使用仿真时间
        if current_time is not None:
            if new_state == TaskState.QUEUED and self.queue_start_time is None:
                self.queue_start_time = current_time
            elif self.state == TaskState.QUEUED and new_state != TaskState.QUEUED:
                if self.queue_start_time is not None:
                    self.total_queue_time += current_time - self.queue_start_time
                    self.queue_start_time = None
        
        self.state = new_state
        if satellite_id:
            self.current_satellite_id = satellite_id
        
        return True
    
    def start_processing(self, satellite_id: str, start_time: float) -> bool:
        """开始处理任务"""
        if not self.update_state(TaskState.PROCESSING, satellite_id, start_time):
            return False
        
        record = ProcessingRecord(
            satellite_id=satellite_id,
            start_time=start_time,
            end_time=0.0,
            cpu_cycles_processed=0,
            energy_consumed=0.0,
            completion_ratio=self.get_completion_ratio(),
            is_partial=False
        )
        self.processing_records.append(record)
        return True
    
    def update_processing_progress(self, cycles_processed: int, energy_consumed: float, current_time: float):
        """更新处理进度"""
        if self.state != TaskState.PROCESSING or not self.processing_records:
            return
        
        self.processed_cpu_cycles = min(self.processed_cpu_cycles + cycles_processed, self.total_cpu_cycles)
        self.total_energy_consumed += energy_consumed
        
        current_record = self.processing_records[-1]
        current_record.cpu_cycles_processed += cycles_processed
        current_record.energy_consumed += energy_consumed
        current_record.completion_ratio = self.get_completion_ratio()
        current_record.end_time = current_time
    
    def complete_processing(self, end_time: float, is_partial: bool = False):
        """完成当前处理阶段"""
        if self.state != TaskState.PROCESSING or not self.processing_records:
            return
        
        current_record = self.processing_records[-1]
        current_record.end_time = end_time
        current_record.is_partial = is_partial
        
        processing_time = end_time - current_record.start_time
        self.total_processing_time += processing_time
        
        # 如果任务完全完成，生成结果数据（基础版本：1/10压缩）
        if self.is_completed() and not self.result_generated:
            self.generate_result_data()
    
    def generate_result_data(self):
        """生成结果数据（基础版本：数据量变为原始的1/10）"""
        if not self.result_generated:
            self.result_data_size_mb = self.data_size_mb * self.result_compression_ratio
            self.result_data_bits = self.result_data_size_mb * 8 * 1024 * 1024
            self.result_generated = True
            
            # 更新状态为准备返回（需要外部调用时传入current_time）
            # self.update_state(TaskState.RETURNING) - 移除自动状态更新，由外部控制
    
    def transfer_to_satellite(self, from_satellite_id: str, to_satellite_id: str, 
                            transfer_time: float, transfer_energy: float, success: bool, current_time: float):
        """记录传输操作 - 修复：接收仿真时间参数"""
        # 先更新状态为传输中
        self.update_state(TaskState.TRANSFERRING, None, current_time)
        
        record = TransferRecord(
            from_satellite_id=from_satellite_id,
            to_satellite_id=to_satellite_id,
            transfer_time=transfer_time,
            transfer_energy=transfer_energy,
            success=success
        )
        self.transfer_records.append(record)
        self.total_transfer_time += transfer_time
        self.total_energy_consumed += transfer_energy
        
        if success:
            self.update_state(TaskState.QUEUED, to_satellite_id, current_time)
        else:
            self.update_state(TaskState.FAILED, None, current_time)
    
    def attempt_retry(self, current_time: float, reason: str) -> bool:
        """尝试重发"""
        if self.retry_record.retry_count >= self.retry_record.max_retries:
            self.update_state(TaskState.FAILED, None, current_time)
            return False
        
        self.retry_record.retry_count += 1
        self.retry_record.last_retry_time = current_time
        self.retry_record.retry_reason = reason
        self.update_state(TaskState.RETRYING, None, current_time)
        return True
    
    def get_completion_ratio(self) -> float:
        """获取完成比例"""
        if self.total_cpu_cycles == 0:
            return 1.0
        return min(1.0, self.processed_cpu_cycles / self.total_cpu_cycles)
    
    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return self.get_completion_ratio() >= 1.0
    
    def confirm_return_to_ground(self, ground_station_id: int, return_time: float) -> bool:
        """确认结果已返回给地面用户（基础版本）"""
        if self.state != TaskState.RETURNING or not self.result_generated:
            return False
        
        # 验证是否返回给正确的地面用户
        if ground_station_id != self.source_location_id:
            return False
        
        # 记录返回时间并完成任务
        self.return_time = return_time
        self.update_state(TaskState.COMPLETED, None, return_time)
        return True
    
    def get_result_info(self) -> Dict[str, Any]:
        """获取结果信息"""
        if not self.result_generated:
            return {}
        
        return {
            'task_id': self.task_id,
            'original_size_mb': self.data_size_mb,
            'result_size_mb': self.result_data_size_mb,
            'compression_ratio': self.result_compression_ratio,
            'source_location_id': self.source_location_id,
            'processing_satellite': self.current_satellite_id,
            'result_ready': self.result_generated
        }
    
    def get_remaining_cycles(self) -> int:
        """获取剩余CPU周期数"""
        return max(0, self.total_cpu_cycles - self.processed_cpu_cycles)
    
    def get_statistics(self, current_time: float = None) -> Dict[str, Any]:
        """获取任务统计信息 - 修复：使用仿真时间计算延迟"""
        # 计算端到端延迟 - 使用仿真时间
        end_to_end_delay = None
        if self.state == TaskState.COMPLETED and hasattr(self, 'return_time'):
            end_to_end_delay = self.return_time - self.generation_timestamp
        elif current_time is not None:
            end_to_end_delay = current_time - self.generation_timestamp
            
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'state': self.state.value,
            'completion_ratio': self.get_completion_ratio(),
            'total_processing_time': self.total_processing_time,
            'total_transfer_time': self.total_transfer_time,
            'total_queue_time': self.total_queue_time,
            'total_energy_consumed': self.total_energy_consumed,
            'processing_satellites_count': len(set(r.satellite_id for r in self.processing_records)),
            'retry_count': self.retry_record.retry_count,
            'deadline_met': self.state == TaskState.COMPLETED,
            'end_to_end_delay': end_to_end_delay,
            'data_size_mb': self.data_size_mb,
            'complexity': self.complexity_cycles_per_bit,
            'priority': self.priority
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于序列化和数据交换"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'data_size_mb': self.data_size_mb,
            'data_size_bits': self.data_size_bits,
            'complexity_cycles_per_bit': self.complexity_cycles_per_bit,
            'total_cpu_cycles': self.total_cpu_cycles,
            'processed_cpu_cycles': self.processed_cpu_cycles,
            'deadline_timestamp': self.deadline_timestamp,
            'priority': self.priority,
            'source_location_id': self.source_location_id,
            'generation_timestamp': self.generation_timestamp,
            'state': self.state.value,
            'current_satellite_id': self.current_satellite_id,
            'completion_ratio': self.get_completion_ratio(),
            'remaining_cycles': self.get_remaining_cycles(),
            'is_completed': self.is_completed()
        }
    
    def get_resource_requirements(self, current_time: float) -> Dict[str, Any]:
        """获取任务资源需求，便于调度器使用 - 修复：使用仿真时间计算紧急度"""
        return {
            'task_id': self.task_id,
            'cpu_cycles_remaining': self.get_remaining_cycles(),
            'data_size_bits': self.data_size_bits,
            'estimated_processing_time': self.get_remaining_cycles() / self.f_leo_hz,
            'priority': self.priority,
            'deadline_timestamp': self.deadline_timestamp,
            'urgency_score': 1.0 / (max(self.deadline_timestamp - current_time, 0.1) + self.epsilon_urgency)
        }
    
    def __repr__(self) -> str:
        return (f"Task(id={self.task_id}, type={self.task_type}, state={self.state.value}, "
                f"completion={self.get_completion_ratio():.2f}, priority={self.priority})")


class TaskLoader:
    """任务数据加载器"""
    
    def __init__(self, data_file: str = "src/env/task_data/task_generation_results.json"):
        self.data_file = data_file
        self.task_data = None
        self.current_timeslot = 0
        # 预加载配置，避免每个任务都重新加载
        self.shared_config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置文件（只加载一次） - 修复：改进异常处理"""
        config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except (FileNotFoundError, yaml.YAMLError, IOError) as e:
            import logging
            logging.warning(f"加载配置文件失败: {e}, 使用默认配置")
            return {
                'queuing': {'w_priority': 1.0, 'w_urgency': 2.0, 'w_cost': 0.5, 'epsilon_urgency': 1e-6},
                'computation': {'f_leo_hz': 10e9, 'zeta_leo': 1.0e-28},
                'communication': {'max_retries': 2}
            }
    
    def load_task_data(self) -> Dict:
        """加载任务生成数据"""
        if self.task_data is None:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.task_data = json.load(f)
        return self.task_data
    
    def get_tasks_for_timeslot(self, timeslot: int, config: Dict = None) -> List[Task]:
        """获取指定时隙的所有任务"""
        data = self.load_task_data()
        
        if timeslot >= len(data["simulation_results"]):
            return []
        
        timeslot_data = data["simulation_results"][timeslot]
        tasks = []
        
        # 使用预加载的配置，避免每个任务都重新加载
        task_config = config or self.shared_config
        
        for location in timeslot_data["locations"]:
            location_id = location["location_id"]
            for task_data in location["generated_tasks"]:
                task = Task(
                    task_data=task_data,
                    source_location_id=location_id,
                    generation_timestamp=float(timeslot_data["timestamp"]),
                    config=task_config
                )
                tasks.append(task)
        
        return tasks
    
    def get_metadata(self) -> Dict:
        """获取仿真元数据"""
        data = self.load_task_data()
        return data["simulation_metadata"]


class TaskManager:
    """任务管理器"""
    
    def __init__(self, task_loader: TaskLoader = None):
        self.task_loader = task_loader or TaskLoader()
        self.active_tasks: Dict[int, Task] = {}
        self.completed_tasks: List[Task] = []
        self.failed_tasks: List[Task] = []
        self.current_timeslot = 0
        
        # 添加索引结构优化查找性能
        self._satellite_task_index: Dict[str, set] = {}  # 卫星ID -> 任务ID集合
        self._state_task_index: Dict[TaskState, set] = {}  # 状态 -> 任务ID集合
        
        # 初始化状态索引
        for state in TaskState:
            self._state_task_index[state] = set()
        
        # 任务历史管理参数
        self.max_history_tasks = 10000  # 最大保留的历史任务数量
    
    def _add_task_to_indexes(self, task: Task):
        """将任务添加到索引结构中"""
        # 添加到状态索引
        self._state_task_index[task.state].add(task.task_id)
        
        # 添加到卫星索引（如果有关联卫星）
        if task.current_satellite_id:
            if task.current_satellite_id not in self._satellite_task_index:
                self._satellite_task_index[task.current_satellite_id] = set()
            self._satellite_task_index[task.current_satellite_id].add(task.task_id)
    
    def _remove_task_from_indexes(self, task: Task):
        """从索引结构中移除任务"""
        # 从状态索引中移除
        self._state_task_index[task.state].discard(task.task_id)
        
        # 从卫星索引中移除
        if task.current_satellite_id and task.current_satellite_id in self._satellite_task_index:
            self._satellite_task_index[task.current_satellite_id].discard(task.task_id)
            # 如果该卫星没有任务了，清理空集合
            if not self._satellite_task_index[task.current_satellite_id]:
                del self._satellite_task_index[task.current_satellite_id]
    
    def _update_task_indexes(self, task: Task, old_state: TaskState = None, old_satellite_id: str = None):
        """更新任务在索引中的位置"""
        # 从旧索引中移除
        if old_state is not None:
            self._state_task_index[old_state].discard(task.task_id)
        if old_satellite_id and old_satellite_id in self._satellite_task_index:
            self._satellite_task_index[old_satellite_id].discard(task.task_id)
            if not self._satellite_task_index[old_satellite_id]:
                del self._satellite_task_index[old_satellite_id]
        
        # 添加到新索引
        self._add_task_to_indexes(task)
    
    def _cleanup_old_tasks(self):
        """清理过多的历史任务，防止内存泄漏"""
        # 清理完成任务历史
        if len(self.completed_tasks) > self.max_history_tasks:
            # 保留最近的任务，清理较旧的任务
            excess_count = len(self.completed_tasks) - self.max_history_tasks
            self.completed_tasks = self.completed_tasks[excess_count:]
        
        # 清理失败任务历史
        if len(self.failed_tasks) > self.max_history_tasks:
            excess_count = len(self.failed_tasks) - self.max_history_tasks
            self.failed_tasks = self.failed_tasks[excess_count:]
    
    def load_tasks_for_timeslot(self, timeslot: int, config: Dict = None) -> List[Task]:
        """加载指定时隙的任务并添加到管理器 - 维护索引"""
        tasks = self.task_loader.get_tasks_for_timeslot(timeslot, config)
        for task in tasks:
            self.active_tasks[task.task_id] = task
            self._add_task_to_indexes(task)  # 添加到索引
        self.current_timeslot = timeslot
        return tasks
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        return self.active_tasks.get(task_id)
    
    def update_task_state(self, task_id: int, new_state: TaskState, current_time: float = None) -> bool:
        """更新任务状态 - 修复：支持仿真时间参数，维护索引"""
        task = self.get_task(task_id)
        if not task:
            return False
        
        old_state = task.state
        old_satellite_id = task.current_satellite_id
        
        success = task.update_state(new_state, None, current_time)
        
        if success:
            # 移动完成/失败的任务
            if new_state == TaskState.COMPLETED:
                self._remove_task_from_indexes(task)
                self.completed_tasks.append(task)
                del self.active_tasks[task_id]
                # 定期清理历史任务
                if len(self.completed_tasks) % 1000 == 0:
                    self._cleanup_old_tasks()
            elif new_state == TaskState.FAILED:
                self._remove_task_from_indexes(task)
                self.failed_tasks.append(task)
                del self.active_tasks[task_id]
                # 定期清理历史任务
                if len(self.failed_tasks) % 1000 == 0:
                    self._cleanup_old_tasks()
            else:
                # 更新索引
                self._update_task_indexes(task, old_state, old_satellite_id)
        
        return success
    
    def get_queued_tasks_for_satellite(self, satellite_id: str) -> List[Task]:
        """获取指定卫星的排队任务 - 优化：使用索引"""
        if satellite_id not in self._satellite_task_index:
            return []
        
        # 获取该卫星的任务ID集合与排队状态任务ID集合的交集
        satellite_task_ids = self._satellite_task_index[satellite_id]
        queued_task_ids = self._state_task_index[TaskState.QUEUED]
        common_task_ids = satellite_task_ids.intersection(queued_task_ids)
        
        return [self.active_tasks[task_id] for task_id in common_task_ids]
    
    def get_tasks_by_state(self, state: TaskState) -> List[Task]:
        """获取指定状态的所有任务 - 优化：使用索引"""
        task_ids = self._state_task_index[state]
        return [self.active_tasks[task_id] for task_id in task_ids]
    
    def get_high_priority_tasks(self, current_time: float, top_n: int = 10) -> List[Task]:
        """获取当前时刻优先级最高的N个任务"""
        active_tasks = list(self.active_tasks.values())
        active_tasks.sort(key=lambda t: t.calculate_dynamic_priority(current_time), reverse=True)
        return active_tasks[:top_n]
    
    def get_tasks_by_satellite(self, satellite_id: str) -> Dict[str, List[Task]]:
        """获取指定卫星相关的所有任务，按状态分组 - 优化：使用索引"""
        satellite_tasks = {
            'queued': [],
            'processing': [],
            'transferring': []
        }
        
        if satellite_id not in self._satellite_task_index:
            return satellite_tasks
        
        satellite_task_ids = self._satellite_task_index[satellite_id]
        
        # 使用索引快速查找各状态的任务
        for state_name, state in [('queued', TaskState.QUEUED), 
                                  ('processing', TaskState.PROCESSING), 
                                  ('transferring', TaskState.TRANSFERRING)]:
            state_task_ids = self._state_task_index[state]
            common_task_ids = satellite_task_ids.intersection(state_task_ids)
            satellite_tasks[state_name] = [self.active_tasks[task_id] for task_id in common_task_ids]
        
        return satellite_tasks
    
    def get_overdue_tasks(self, current_time: float) -> List[Task]:
        """获取已超期的任务"""
        return [task for task in self.active_tasks.values() 
                if task.deadline_timestamp < current_time]
    
    def export_tasks_for_scheduling(self, satellite_id: str, current_time: float) -> List[Dict]:
        """导出指定卫星的任务信息供调度器使用"""
        queued_tasks = self.get_queued_tasks_for_satellite(satellite_id)
        return [
            {
                'task_id': task.task_id,
                'priority_score': task.calculate_dynamic_priority(current_time),
                'resource_req': task.get_resource_requirements(current_time),
                'can_complete_on_time': task.can_complete_by_deadline(
                    current_time, task.get_remaining_cycles() / task.f_leo_hz
                )
            }
            for task in queued_tasks
        ]
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统级统计信息"""
        total_tasks = len(self.active_tasks) + len(self.completed_tasks) + len(self.failed_tasks)
        
        if total_tasks == 0:
            return {"total_tasks": 0, "completion_rate": 0, "average_delay": 0, "average_energy": 0}
        
        completion_rate = len(self.completed_tasks) / total_tasks
        
        if self.completed_tasks:
            avg_delay = sum(t.total_processing_time + t.total_transfer_time + t.total_queue_time 
                          for t in self.completed_tasks) / len(self.completed_tasks)
            avg_energy = sum(t.total_energy_consumed for t in self.completed_tasks) / len(self.completed_tasks)
        else:
            avg_delay = 0
            avg_energy = 0
        
        # 状态分布统计
        state_distribution = {}
        for state in TaskState:
            state_distribution[state.value] = len(self.get_tasks_by_state(state))
        
        return {
            'total_tasks': total_tasks,
            'active_tasks': len(self.active_tasks),
            'completed_tasks': len(self.completed_tasks),
            'failed_tasks': len(self.failed_tasks),
            'completion_rate': completion_rate,
            'average_delay': avg_delay,
            'average_energy': avg_energy,
            'retry_rate': sum(t.retry_record.retry_count for t in self.completed_tasks + self.failed_tasks) / total_tasks,
            'current_timeslot': self.current_timeslot,
            'state_distribution': state_distribution
        }
