---
title: Product Vision
description: "定义项目核心目的、目标用户和主要功能"
inclusion: always
---

# 产品愿景 - SPACE-DMPO 科研仿真平台

## 核心定位
SPACE-DMPO 是一个专为科研用途设计的卫星边缘计算仿真平台，专注于"地面生成任务，星上决策并处理"的核心流程研究。

## 项目价值主张

### 主要功能
1. **地面任务生成**：模拟真实世界中地面用户产生的计算任务请求
2. **星上智能决策**：每颗LEO卫星作为独立智能体，基于本地状态和邻居信息做出任务调度决策
3. **分布式任务处理**：在卫星网络中进行任务的本地处理、星间卸载或云端回传
4. **多智能体协调**：LEO卫星间通过有限信息交换实现协同优化

### 科研价值
- **理论验证**：验证分布式多智能体强化学习在动态网络拓扑中的效果
- **算法对比**：为不同MARL算法提供标准化测试环境
- **性能评估**：通过高保真仿真评估系统在不同场景下的表现
- **策略研究**：研究"轨道感知自适应学习"(OAAL)框架的有效性

## 目标用户
- **学术研究人员**：研究卫星边缘计算、多智能体系统的学者
- **算法工程师**：开发和测试分布式强化学习算法的工程师
- **系统设计师**：设计未来天地一体化网络架构的研究人员

## 核心业务流程

### 仿真流程
1. **环境初始化**：建立36颗LEO卫星星座和420个地面用户
2. **任务生成**：基于霍克斯过程生成具有时空相关性的任务
3. **智能体决策**：LEO卫星根据本地状态做出调度决策
4. **任务执行**：在选定节点上处理任务并返回结果
5. **性能评估**：收集延迟、能耗、完成率等关键指标

### 关键特性
- **高度动态性**：模拟卫星轨道运动导致的网络拓扑变化
- **容错能力**：支持随机故障和区域性大规模故障场景
- **可扩展性**：支持不同规模的卫星星座配置
- **真实性**：基于真实卫星物理参数和通信模型

## 成功指标
- **仿真准确性**：与理论分析结果的一致性
- **算法性能**：任务完成率、平均延迟、能耗效率
- **系统鲁棒性**：在故障场景下的服务连续性
- **收敛效果**：多智能体学习算法的收敛速度和稳定性

## 非目标
- 不面向工业生产部署
- 不提供实时在线服务
- 不处理实际卫星硬件接口
- 不涉及具体的商业应用场景