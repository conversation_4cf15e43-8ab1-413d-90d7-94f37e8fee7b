"""
图注意力协同机制
实现基于GAT的动态邻居协调
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Set
import networkx as nx
from dataclasses import dataclass
import logging

# PyTorch Geometric imports for efficient GAT implementation
try:
    from torch_geometric.nn import GATConv
    from torch_geometric.data import Data
    HAS_TORCH_GEOMETRIC = True
except ImportError:
    HAS_TORCH_GEOMETRIC = False
    print("Warning: torch_geometric not found. Using fallback implementation.")

logger = logging.getLogger(__name__)


@dataclass
class LinkInfo:
    """链路信息"""
    source_id: int
    target_id: int
    distance_km: float
    data_rate_mbps: float
    delay_ms: float
    energy_cost_j: float
    link_quality: float
    
    def to_edge_features(self) -> np.ndarray:
        """转换为边特征向量"""
        return np.array([
            self.distance_km / 2000.0,      # 归一化距离
            self.data_rate_mbps / 100.0,    # 归一化数据率
            self.delay_ms / 50.0,            # 归一化延迟
            self.energy_cost_j / 1.0,        # 归一化能耗
            self.link_quality                # 链路质量已归一化
        ])


class GraphAttentionLayer(nn.Module):
    """高效图注意力层 - 使用torch_geometric优化实现"""
    
    def __init__(self, 
                 in_features: int, 
                 out_features: int, 
                 heads: int = 1,
                 edge_features: int = 5,
                 dropout: float = 0.1,
                 concat: bool = True):
        super().__init__()
        
        self.in_features = in_features
        self.out_features = out_features
        self.heads = heads
        self.concat = concat
        self.dropout = dropout
        
        if HAS_TORCH_GEOMETRIC:
            # 使用torch_geometric的高效GAT实现
            self.gat_conv = GATConv(
                in_channels=in_features,
                out_channels=out_features,
                heads=heads,
                edge_dim=edge_features,
                dropout=dropout,
                concat=concat
            )
            self.use_efficient = True
        else:
            # 回退到原始实现
            self.use_efficient = False
            self._init_fallback_layers(in_features, out_features, edge_features, dropout)
            
    def _init_fallback_layers(self, in_features, out_features, edge_features, dropout):
        """初始化回退实现的层"""
        # 节点特征变换
        self.W_node = nn.Linear(in_features, out_features, bias=False)
        # 边特征变换
        self.W_edge = nn.Linear(edge_features, out_features, bias=False)
        # 注意力参数
        self.a = nn.Parameter(torch.zeros(size=(2 * out_features + out_features, 1)))
        nn.init.xavier_uniform_(self.a.data, gain=1.414)
        # LeakyReLU激活
        self.leakyrelu = nn.LeakyReLU(0.2)
        
    def forward(self, node_features: torch.Tensor, edge_index: torch.Tensor = None,
                edge_attr: torch.Tensor = None, adj_matrix: torch.Tensor = None,
                edge_features: torch.Tensor = None) -> torch.Tensor:
        """
        前向传播 - 支持两种输入格式
        
        PyG格式:
            node_features: [num_nodes, in_features]
            edge_index: [2, num_edges] - 边索引
            edge_attr: [num_edges, edge_features] - 边特征
            
        传统格式:
            node_features: [num_nodes, in_features]
            adj_matrix: [num_nodes, num_nodes] - 邻接矩阵
            edge_features: [num_nodes, num_nodes, edge_features] - 边特征矩阵
        """
        if self.use_efficient and edge_index is not None:
            # 使用torch_geometric高效实现
            return self.gat_conv(node_features, edge_index, edge_attr)
        else:
            # 使用传统格式（向后兼容）
            if adj_matrix is None or edge_features is None:
                raise ValueError("adj_matrix and edge_features required for fallback implementation")
            return self._forward_fallback(node_features, edge_features, adj_matrix)
            
    def _forward_fallback(self, node_features: torch.Tensor, edge_features: torch.Tensor,
                         adj_matrix: torch.Tensor) -> torch.Tensor:
        """回退实现的前向传播"""
        num_nodes = node_features.size(0)
        
        # 节点特征变换
        h = self.W_node(node_features)
        
        # 使用向量化方式计算注意力（优化版本）
        attention_scores = self._compute_attention_vectorized(h, edge_features, adj_matrix)
        
        # 应用dropout
        attention_scores = F.dropout(attention_scores, self.dropout, training=self.training)
        
        # 聚合邻居特征
        h_prime = torch.matmul(attention_scores, h)
        
        if self.concat:
            return F.elu(h_prime)
        else:
            return h_prime
    
    def _compute_attention_vectorized(self, h: torch.Tensor, edge_features: torch.Tensor,
                                     adj_matrix: torch.Tensor) -> torch.Tensor:
        """向量化注意力计算 - 消除双重循环"""
        num_nodes = h.size(0)
        
        # 找到所有有效边
        edge_indices = torch.nonzero(adj_matrix, as_tuple=False)  # [num_edges, 2]
        
        if edge_indices.size(0) == 0:
            return torch.eye(num_nodes).to(h.device)
        
        # 批量处理边特征
        src_indices, tgt_indices = edge_indices[:, 0], edge_indices[:, 1]
        edge_feat_batch = edge_features[src_indices, tgt_indices]  # [num_edges, edge_dim]
        e_batch = self.W_edge(edge_feat_batch)  # [num_edges, out_features]
        
        # 批量构建注意力输入
        h_src = h[src_indices]  # [num_edges, out_features]
        h_tgt = h[tgt_indices]  # [num_edges, out_features]
        
        # 拼接特征: [h_i || h_j || e_ij]
        cat_features = torch.cat([h_src, h_tgt, e_batch], dim=1)  # [num_edges, 3*out_features]
        
        # 计算注意力分数
        e_scores = self.leakyrelu(torch.matmul(cat_features, self.a).squeeze(1))  # [num_edges]
        
        # 构建稀疏注意力矩阵
        attention = torch.full((num_nodes, num_nodes), float('-inf')).to(h.device)
        attention[src_indices, tgt_indices] = e_scores
        
        # Softmax归一化
        attention = F.softmax(attention, dim=1)
        
        # 处理NaN值
        attention = torch.where(torch.isnan(attention), torch.zeros_like(attention), attention)
        
        return attention


class GraphAttentionCoordination(nn.Module):
    """简化的图注意力协同网络 - 降低模型复杂度"""
    
    def __init__(self,
                 node_feature_dim: int = 15,
                 edge_feature_dim: int = 5,
                 hidden_dim: int = 128,
                 num_heads: int = 4,
                 dropout: float = 0.1):
        super().__init__()
        
        self.node_feature_dim = node_feature_dim
        self.edge_feature_dim = edge_feature_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # 单层多头注意力
        self.gat_layer = GraphAttentionLayer(
            in_features=node_feature_dim,
            out_features=hidden_dim,
            heads=num_heads,
            edge_features=edge_feature_dim,
            dropout=dropout,
            concat=True
        )
        
        # 简化的输出层 - 使用线性层替代完整GAT层
        final_dim = hidden_dim * num_heads if self.gat_layer.concat else hidden_dim
        self.output_projection = nn.Sequential(
            nn.Linear(final_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 协同决策层
        self.coordination_mlp = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.LayerNorm(hidden_dim // 2),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 3)  # 3种协同动作：接受/拒绝/转发
        )
        
    def forward(self, node_features: torch.Tensor, 
                edge_index: torch.Tensor = None, edge_attr: torch.Tensor = None,
                edge_features: torch.Tensor = None, adj_matrix: torch.Tensor = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播 - 支持PyG和传统格式
        
        Returns:
            node_embeddings: [num_nodes, hidden_dim] 节点嵌入
            coordination_probs: [num_nodes, 3] 协同决策概率
        """
        # GAT特征提取
        if edge_index is not None and edge_attr is not None:
            # PyG格式
            gat_output = self.gat_layer(node_features, edge_index=edge_index, edge_attr=edge_attr)
        elif edge_features is not None and adj_matrix is not None:
            # 传统格式
            gat_output = self.gat_layer(node_features, adj_matrix=adj_matrix, edge_features=edge_features)
        else:
            raise ValueError("Must provide either (edge_index, edge_attr) or (edge_features, adj_matrix)")
        
        # 输出投影
        node_embeddings = self.output_projection(gat_output)
        
        # 协同决策
        coordination_logits = self.coordination_mlp(node_embeddings)
        coordination_probs = F.softmax(coordination_logits, dim=-1)
        
        return node_embeddings, coordination_probs


class CoordinationManager:
    """协同管理器 - 管理卫星间的协作"""
    
    def __init__(self, num_satellites: int = 36, hidden_dim: int = 128):
        self.num_satellites = num_satellites
        self.gat_network = GraphAttentionCoordination(hidden_dim=hidden_dim)
        
        # 协同请求队列
        self.coordination_requests = {}  # {satellite_id: [requests]}
        
        # ID映射管理 - 解决状态依赖问题
        self.id_to_index_mapping = {}  # 存储当前的ID到索引映射
        self.index_to_id_mapping = {}  # 存储当前的索引到ID映射
        
        # 性能统计
        self.coordination_stats = {
            'requests_sent': 0,
            'requests_accepted': 0,
            'tasks_offloaded': 0,
            'total_reward': 0.0
        }
        
    def build_satellite_graph(self, satellite_states: Dict[int, Dict],
                            communication_links: Dict[Tuple[int, int], LinkInfo]) -> Optional[nx.Graph]:
        """构建卫星网络图 - 可选使用，主要用于邻居查找"""
        if not HAS_TORCH_GEOMETRIC:
            # 只有在没有PyG时才使用networkx构建完整图
            G = nx.Graph()
            
            # 添加节点
            for sat_id, state in satellite_states.items():
                G.add_node(sat_id, **state)
            
            # 添加边
            for (src, tgt), link_info in communication_links.items():
                if link_info.link_quality > 0.5:  # 只保留高质量链路
                    G.add_edge(src, tgt, **{
                        'distance': link_info.distance_km,
                        'data_rate': link_info.data_rate_mbps,
                        'delay': link_info.delay_ms,
                        'quality': link_info.link_quality
                    })
            
            return G
        else:
            # 使用PyG时，只构建轻量级邻接表用于邻居查找
            return self._build_lightweight_adjacency(satellite_states, communication_links)
    
    def _build_lightweight_adjacency(self, satellite_states: Dict[int, Dict],
                                   communication_links: Dict[Tuple[int, int], LinkInfo]) -> Dict[int, Set[int]]:
        """构建轻量级邻接表，避免networkx开销"""
        adjacency = {sat_id: set() for sat_id in satellite_states.keys()}
        
        for (src, tgt), link_info in communication_links.items():
            if link_info.link_quality > 0.5:  # 只保留高质量链路
                if src in adjacency and tgt in adjacency:
                    adjacency[src].add(tgt)
                    adjacency[tgt].add(src)
        
        return adjacency
    
    def coordinate_satellites(self, satellite_states: Dict[int, Dict],
                            communication_links: Dict[Tuple[int, int], LinkInfo]) -> Dict[int, Dict]:
        """执行卫星协同 - 使用安全的ID映射"""
        # 构建图
        graph = self.build_satellite_graph(satellite_states, communication_links)
        
        # 准备输入数据并获取ID映射
        node_features, edge_data, id_mapping = self._prepare_gat_input_safe(
            graph, satellite_states, communication_links
        )
        
        # 保存映射以便后续使用
        self.id_to_index_mapping = id_mapping
        self.index_to_id_mapping = {idx: sat_id for sat_id, idx in id_mapping.items()}
        
        # GAT前向传播
        if HAS_TORCH_GEOMETRIC and isinstance(edge_data, tuple):
            # 使用PyG格式
            edge_index, edge_attr = edge_data
            node_embeddings, coordination_probs = self.gat_network(
                node_features, edge_index, edge_attr
            )
        else:
            # 使用传统格式
            edge_features, adj_matrix = edge_data
            node_embeddings, coordination_probs = self.gat_network(
                node_features, edge_features, adj_matrix
            )
        
        # 使用安全映射生成协同决策
        coordination_decisions = self._generate_coordination_decisions(
            satellite_states, graph, coordination_probs, id_mapping
        )
        
        return coordination_decisions
    
    def _prepare_gat_input_safe(self, graph: nx.Graph, satellite_states: Dict,
                               communication_links: Dict) -> Tuple[torch.Tensor, Tuple, Dict[int, int]]:
        """安全准备GAT输入数据 - 返回ID映射以避免依赖问题"""
        sorted_ids = sorted(satellite_states.keys())
        id_to_idx = {sat_id: i for i, sat_id in enumerate(sorted_ids)}
        num_nodes = len(sorted_ids)
        
        # 节点特征
        node_features = []
        for sat_id in sorted_ids:
            state = satellite_states[sat_id]
            # 使用现有的15维观测特征
            features = state.get('observation', np.zeros(15))
            node_features.append(features)
        
        node_features = torch.FloatTensor(node_features)
        
        if HAS_TORCH_GEOMETRIC:
            # 使用PyG格式（更高效）
            edge_data = self._build_pyg_edges(communication_links, id_to_idx)
        else:
            # 使用传统格式
            edge_data = self._build_traditional_edges(communication_links, id_to_idx, num_nodes)
        
        return node_features, edge_data, id_to_idx
    
    def _build_pyg_edges(self, communication_links: Dict, id_to_idx: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """构建PyTorch Geometric格式的边数据"""
        edge_list = []
        edge_attrs = []
        
        for (src, tgt), link_info in communication_links.items():
            if src in id_to_idx and tgt in id_to_idx:
                i, j = id_to_idx[src], id_to_idx[tgt]
                edge_feat = link_info.to_edge_features()
                
                # 添加双向边
                edge_list.extend([[i, j], [j, i]])
                edge_attrs.extend([edge_feat, edge_feat])
        
        if not edge_list:
            # 如果没有边，创建空的边索引
            edge_index = torch.zeros((2, 0), dtype=torch.long)
            edge_attr = torch.zeros((0, 5), dtype=torch.float)
        else:
            edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
            edge_attr = torch.tensor(edge_attrs, dtype=torch.float)
        
        return edge_index, edge_attr
    
    def _build_traditional_edges(self, communication_links: Dict, id_to_idx: Dict, 
                                num_nodes: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """构建传统格式的边数据"""
        edge_features = torch.zeros(num_nodes, num_nodes, 5)
        adj_matrix = torch.zeros(num_nodes, num_nodes)
        
        for (src, tgt), link_info in communication_links.items():
            if src in id_to_idx and tgt in id_to_idx:
                i, j = id_to_idx[src], id_to_idx[tgt]
                edge_feat = link_info.to_edge_features()
                edge_features[i, j] = torch.FloatTensor(edge_feat)
                edge_features[j, i] = torch.FloatTensor(edge_feat)
                adj_matrix[i, j] = 1
                adj_matrix[j, i] = 1
        
        return edge_features, adj_matrix
    
    def _generate_coordination_decisions(self, satellite_states: Dict, graph,
                                       coordination_probs: torch.Tensor, 
                                       id_mapping: Dict[int, int]) -> Dict[int, Dict]:
        """使用安全映射生成协同决策"""
        coordination_decisions = {}
        
        for sat_id, state in satellite_states.items():
            if sat_id not in id_mapping:
                logger.warning(f"Satellite {sat_id} not found in ID mapping, skipping")
                continue
                
            idx = id_mapping[sat_id]  # 使用安全映射
            load_ratio = state.get('queue_length', 0) / 10.0
            
            if load_ratio > 0.8:  # 高负载，需要协助
                # 获取邻居 - 支持两种图格式
                if isinstance(graph, nx.Graph):
                    neighbors = list(graph.neighbors(sat_id))
                elif isinstance(graph, dict):
                    neighbors = list(graph.get(sat_id, set()))
                else:
                    neighbors = []
                
                if neighbors:
                    best_neighbor = self._select_best_neighbor_safe(
                        sat_id, neighbors, satellite_states, coordination_probs, id_mapping
                    )
                    
                    if best_neighbor is not None:
                        coordination_decisions[sat_id] = {
                            'action': 'request_help',
                            'target': best_neighbor,
                            'load_ratio': load_ratio
                        }
                        self.coordination_stats['requests_sent'] += 1
            
            elif load_ratio < 0.3:  # 低负载，可以提供协助
                if sat_id in self.coordination_requests:
                    for request in self.coordination_requests[sat_id]:
                        accept_prob = coordination_probs[idx, 0].item()  # 使用安全索引
                        
                        if accept_prob > 0.6:
                            coordination_decisions[sat_id] = {
                                'action': 'accept_request',
                                'from': request['from'],
                                'accept_prob': accept_prob
                            }
                            self.coordination_stats['requests_accepted'] += 1
                            break
        
        return coordination_decisions
    
    def _select_best_neighbor_safe(self, sat_id: int, neighbors: List[int],
                                  satellite_states: Dict, coordination_probs: torch.Tensor,
                                  id_mapping: Dict[int, int]) -> Optional[int]:
        """安全选择最佳协助邻居 - 使用显式ID映射"""
        best_neighbor = None
        best_score = -float('inf')
        
        for neighbor_id in neighbors:
            if neighbor_id not in id_mapping:
                logger.warning(f"Neighbor {neighbor_id} not found in ID mapping, skipping")
                continue
                
            neighbor_state = satellite_states.get(neighbor_id, {})
            neighbor_load = neighbor_state.get('queue_length', 0) / 10.0
            
            # 使用安全映射获取索引
            idx = id_mapping[neighbor_id]
            accept_prob = coordination_probs[idx, 0].item()
            score = (1 - neighbor_load) * accept_prob
            
            if score > best_score:
                best_score = score
                best_neighbor = neighbor_id
        
        return best_neighbor if best_score > 0.3 else None
    
    def add_coordination_request(self, target_id: int, request: Dict):
        """添加协同请求"""
        if target_id not in self.coordination_requests:
            self.coordination_requests[target_id] = []
        self.coordination_requests[target_id].append(request)
    
    def clear_requests(self):
        """清空请求队列"""
        self.coordination_requests.clear()
    
    def get_coordination_reward(self, successful_offload: bool, 
                              task_priority: float = 1.0) -> float:
        """计算协同奖励"""
        if successful_offload:
            reward = 0.1 * task_priority  # 基础协同奖励
            self.coordination_stats['tasks_offloaded'] += 1
        else:
            reward = -0.05  # 失败惩罚
        
        self.coordination_stats['total_reward'] += reward
        return reward
    
    def get_current_mapping(self) -> Tuple[Dict[int, int], Dict[int, int]]:
        """获取当前ID映射，供外部使用"""
        return self.id_to_index_mapping.copy(), self.index_to_id_mapping.copy()
    
    def validate_mapping_consistency(self, satellite_states: Dict[int, Dict]) -> bool:
        """验证映射一致性"""
        expected_ids = set(satellite_states.keys())
        mapped_ids = set(self.id_to_index_mapping.keys())
        
        if expected_ids != mapped_ids:
            logger.error(f"Mapping inconsistency detected. Expected: {expected_ids}, Mapped: {mapped_ids}")
            return False
        
        return True