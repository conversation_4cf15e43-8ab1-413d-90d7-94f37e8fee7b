---
title: Technical Stack
description: "项目技术栈规范，包括框架选择、环境配置和开发标准"
inclusion: always
---

# 技术栈规范 - SPACE-DMPO

## 核心技术框架

### 强化学习环境框架
- **主要框架**：Gymnasium (gym) - 多智能体强化学习环境的标准实现
- **版本要求**：gymnasium>=0.26.0
- **设计原则**：遵循OpenAI Gym标准接口，确保与主流MARL算法兼容
- **环境标准**：实现标准的 `reset()`, `step()`, `render()`, `close()` 接口

### 深度学习框架
- **主框架**：PyTorch >= 1.12.0
- **配套工具**：torchvision, torchaudio
- **网络架构**：支持Actor-Critic, DPPO, MAPPO等主流MARL算法

### 科学计算栈
- **数值计算**：NumPy >= 1.21.0, SciPy >= 1.7.0
- **数据处理**：Pandas >= 1.3.0
- **卫星轨道计算**：pyorbital >= 1.7.0, pyephem >= 4.1.0

### 可视化与实验跟踪
- **绘图库**：matplotlib, seaborn, plotly
- **实验跟踪**：wandb, tensorboard
- **配置管理**：PyYAML, hydra-core

## 环境设计规范

### Gym环境标准实现

#### 核心接口
```python
class SPACE_Environment(gym.Env):
    def reset() -> Dict[str, Any]
    def step(actions: Dict[str, int]) -> Tuple[Dict, Dict, bool, Dict]
    def render(mode: str = 'human') -> Optional[Any]
    def close() -> None
```

#### 状态空间设计
- **全局状态**：系统级指标（时隙、总任务数、平均负载等）
- **智能体状态**：每个LEO卫星的本地状态
  - 位置信息：轨道位置、速度
  - 资源状态：CPU利用率、内存占用、电池电量
  - 任务队列：待处理、处理中、已完成任务统计
  - 网络状态：邻居连接、链路质量

#### 动作空间设计
- **动作类型**：离散动作空间
- **动作选项**：
  - 0: 本地处理 (local_processing)
  - 1: 卫星卸载 (satellite_offload)
  - 2: 云端卸载 (cloud_offload)
- **动态屏蔽**：根据网络故障状态动态屏蔽无效动作

#### 奖励函数设计
- **多目标优化**：任务完成奖励 + 延迟惩罚 + 能耗惩罚 + 失败惩罚
- **权重可配置**：通过配置文件调整不同目标的权重
- **本地化奖励**：每个智能体获得基于其决策效果的局部奖励

### 多智能体支持

#### 环境特性
- **并行执行**：支持多个智能体同时决策
- **部分可观测**：每个智能体仅观测局部状态
- **通信建模**：通过邻居状态摘要模拟有限信息交换
- **异构智能体**：支持不同类型的卫星智能体

#### MARL算法兼容性
- **接口标准**：与RLLib、MAPPO、MADDPG等主流库兼容
- **批量处理**：支持vectorized环境提高训练效率
- **经验收集**：提供标准的trajectory数据格式

## 系统架构设计

### 模块化设计
```
src/env/
├── env_interface.py      # Gym环境主接口
├── satellite.py          # 卫星节点模拟
├── task.py              # 任务生成与管理
├── communication.py      # 通信模型
├── orbital_updater.py    # 轨道更新模块
├── adapters.py          # 系统集成适配器
└── config.yaml          # 环境配置文件
```

### 配置管理
- **YAML配置**：所有环境参数通过YAML文件配置
- **分层配置**：支持系统级、算法级、实验级配置
- **参数验证**：配置加载时进行参数有效性检查

### 数据流设计
1. **任务生成**：基于霍克斯过程的任务流生成
2. **状态观测**：实时收集智能体状态信息
3. **动作执行**：将智能体动作映射到系统操作
4. **奖励计算**：基于系统状态变化计算即时奖励
5. **状态更新**：更新环境状态供下一时步使用

## 开发与测试标准

### 代码规范
- **类型注解**：所有函数使用Python类型提示
- **文档规范**：遵循Google docstring格式
- **代码质量**：使用pytest进行单元测试

### 性能要求
- **仿真速度**：支持1000时隙以上的大规模仿真
- **内存效率**：合理管理历史数据和经验回放
- **并发支持**：支持多进程并行训练

### 扩展性设计
- **插件化**：支持新的MARL算法接入
- **参数化**：关键参数可通过配置文件调整
- **模块化**：各组件松耦合设计，便于独立测试和替换

## 部署与运行环境

### Python环境
- **最低版本**：Python 3.8+
- **推荐版本**：Python 3.9+
- **虚拟环境**：强烈建议使用venv或conda环境

### 硬件要求
- **CPU**：多核处理器（推荐8核以上）
- **内存**：32GB+（大规模仿真）
- **GPU**：可选，用于加速神经网络训练
- **存储**：SSD推荐，用于快速I/O

### 依赖管理
- **requirements.txt**：明确列出所有依赖版本
- **兼容性测试**：定期测试依赖库版本兼容性
- **镜像源**：支持国内pypi镜像源加速安装