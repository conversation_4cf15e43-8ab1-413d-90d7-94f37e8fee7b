<mxfile host="65bd71144e">
    <diagram name="Satellite Architecture" id="satellite-architecture">
        <mxGraphModel dx="2836" dy="2285" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                
                <!-- Program Entry Point -->
                <mxCell id="main-function" value="main()&#10;Program Entry&#10;Test Function" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="790" y="40" width="120" height="70" as="geometry"/>
                </mxCell>

                <!-- Data Input Layer -->
                <mxCell id="data-group" value="Data Input Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="200" y="1500" width="1200" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="config-yaml" value="config.yaml&#10;System Config&#10;- Orbital params&#10;- Energy params&#10;- Comm params" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="50" y="35" width="180" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="orbital-data" value="orbital_updater data&#10;Orbital sync" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="300" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="communication-data" value="communication data&#10;Network sync" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="520" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="task-data" value="task data&#10;Task input" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="740" y="40" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="task-generation-results" value="task_generation_results.json&#10;Task generation results" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" parent="data-group" vertex="1">
                    <mxGeometry x="960" y="40" width="200" height="50" as="geometry"/>
                </mxCell>

                <!-- Core Control Layer -->
                <mxCell id="controller-group" value="Core Control Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="450" y="150" width="700" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-node" value="SatelliteNode&#10;Core Satellite Class&#10;- State management&#10;- Task scheduling&#10;- Resource coordination" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" parent="controller-group" vertex="1">
                    <mxGeometry x="250" y="50" width="200" height="80" as="geometry"/>
                </mxCell>

                <!-- Satellite State Data Layer -->
                <mxCell id="states-group" value="Satellite State Data Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="120" y="350" width="1400" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="position-state" value="Position&#10;Position state&#10;- latitude/longitude&#10;- altitude&#10;- timestamp&#10;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="50" y="40" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="energy-state" value="EnergyState&#10;Energy state&#10;- current_battery_j&#10;- battery_capacity_j&#10;- solar_power_w&#10;- base_power_consumption_w&#10;- is_illuminated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="250" y="40" width="170" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="resource-state" value="ResourceState&#10;Compute resource state&#10;- cpu_frequency_hz&#10;- available_cpu_hz&#10;- memory_total_mb&#10;- memory_used_mb" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="480" y="40" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="communication-state" value="CommunicationState&#10;Communication state&#10;- visible_neighbors&#10;- visible_ground_stations&#10;- active_links" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="680" y="50" width="170" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="performance-metrics" value="PerformanceMetrics&#10;Performance metrics&#10;- total_tasks_received&#10;- total_tasks_completed&#10;- total_tasks_failed&#10;- completion_rate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="900" y="40" width="180" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-status" value="SatelliteStatus&#10;Satellite status enum&#10;- ACTIVE&#10;- INACTIVE&#10;- FAILED&#10;- MAINTENANCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=11" parent="states-group" vertex="1">
                    <mxGeometry x="1130" y="50" width="140" height="100" as="geometry"/>
                </mxCell>

                <!-- Task Management Layer -->
                <mxCell id="task-management-group" value="Task Management Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="200" y="600" width="1200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="receive-task" value="receive_task()&#10;Receive new task&#10;- Queue capacity check&#10;- Satellite status check" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
                    <mxGeometry x="50" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="schedule-next-task" value="schedule_next_task()&#10;Schedule next task&#10;- Priority scheduling&#10;- Timeout check" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
                    <mxGeometry x="250" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="update-processing" value="update_processing()&#10;Update task processing&#10;- Processing progress&#10;- Completion check" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
                    <mxGeometry x="450" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="offload-task" value="offload_task()&#10;Task offloading&#10;- Neighbor check&#10;- Transfer cost calc" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
                    <mxGeometry x="650" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="get-best-offload-target" value="get_best_offload_target()&#10;Select best offload target&#10;- Neighbor selection&#10;- Load assessment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="task-management-group" vertex="1">
                    <mxGeometry x="850" y="40" width="170" height="70" as="geometry"/>
                </mxCell>

                <!-- State Update Layer -->
                <mxCell id="state-update-group" value="State Update Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="200" y="780" width="1200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="update-energy" value="update_energy()&#10;Energy update&#10;- Solar charging&#10;- Base consumption&#10;- Processing consumption" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
                    <mxGeometry x="50" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="update-connectivity" value="update_connectivity()&#10;Connectivity update&#10;- Neighbor list&#10;- Ground station list" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
                    <mxGeometry x="250" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="step" value="step()&#10;Time step execution&#10;- State update&#10;- Task scheduling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
                    <mxGeometry x="450" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="sync-with-orbital" value="sync_with_orbital_state()&#10;Orbital state sync&#10;- Position sync&#10;- Illumination status" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
                    <mxGeometry x="650" y="40" width="170" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="sync-with-communication" value="sync_with_communication_state()&#10;Network state sync&#10;- Neighbor update&#10;- Connection status" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="state-update-group" vertex="1">
                    <mxGeometry x="880" y="40" width="190" height="70" as="geometry"/>
                </mxCell>

                <!-- Data Export Layer -->
                <mxCell id="export-group" value="Data Export Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="300" y="960" width="1000" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="get-status-summary" value="get_status_summary()&#10;Status summary&#10;- Position info&#10;- Energy state&#10;- Task statistics" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
                    <mxGeometry x="50" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="export-for-scheduling" value="export_for_scheduling()&#10;Scheduling data export&#10;- Task acceptance ability&#10;- Resource state" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
                    <mxGeometry x="250" y="40" width="160" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="export-observation-data" value="export_observation_data()&#10;Observation data export&#10;- RL observation&#10;- Complete state data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
                    <mxGeometry x="460" y="40" width="170" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="is-healthy" value="is_healthy()&#10;Health check&#10;- Status check&#10;- Battery check" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
                    <mxGeometry x="680" y="40" width="130" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="can-accept-tasks" value="can_accept_tasks()&#10;Task acceptance check&#10;- Health status&#10;- Queue capacity" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" parent="export-group" vertex="1">
                    <mxGeometry x="860" y="40" width="130" height="70" as="geometry"/>
                </mxCell>

                <!-- Utility Functions Layer -->
                <mxCell id="utility-group" value="Utility Functions Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="300" y="1140" width="1000" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="load-config" value="_load_config()&#10;Config loading&#10;- Config file read&#10;- Default config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="50" y="40" width="140" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="get-default-config" value="_get_default_config()&#10;Default config get&#10;- System params&#10;- Compute params" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="240" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="initialize-from-config" value="_initialize_from_config()&#10;Config initialization&#10;- Parameter setup&#10;- State initialization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="440" y="40" width="160" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="complete-current-task" value="_complete_current_task()&#10;Task completion handler&#10;- Statistics update&#10;- State cleanup" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="650" y="40" width="150" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="fail-current-task" value="_fail_current_task()&#10;Task failure handler&#10;- Failure statistics&#10;- State cleanup" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" parent="utility-group" vertex="1">
                    <mxGeometry x="850" y="40" width="130" height="70" as="geometry"/>
                </mxCell>

                <!-- External Module Interface Layer -->
                <mxCell id="external-interface-group" value="External Module Interface Layer" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="400" y="1320" width="800" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="set-external-modules" value="set_external_modules()&#10;Set external modules&#10;- OrbitalUpdater interface&#10;- CommunicationManager interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
                    <mxGeometry x="50" y="40" width="180" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="sync-with-task-state" value="sync_with_task_state()&#10;Task state sync&#10;- New task reception&#10;- Task state update" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
                    <mxGeometry x="280" y="40" width="160" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="orbital-updater-ref" value="orbital_updater&#10;Orbital updater reference" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
                    <mxGeometry x="500" y="50" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="comm-manager-ref" value="comm_manager&#10;Communication manager reference" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" parent="external-interface-group" vertex="1">
                    <mxGeometry x="670" y="50" width="120" height="50" as="geometry"/>
                </mxCell>

                <!-- Connection Lines -->
                <!-- Main Control Flow -->
                <mxCell id="main-to-satellite" value="Create instance" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="main-function" target="satellite-node" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- State Management Connections -->
                <mxCell id="satellite-to-position" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="position-state" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-to-energy" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="energy-state" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="satellite-to-resource" value="State management" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" parent="1" source="satellite-node" target="resource-state" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Task Management Flow -->
                <mxCell id="receive-to-schedule" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="receive-task" target="schedule-next-task" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="schedule-to-processing" value="Task flow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="schedule-next-task" target="update-processing" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="processing-to-offload" value="Offload decision" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="update-processing" target="offload-task" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- State Update Flow -->
                <mxCell id="step-to-energy" value="State update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="step" target="update-energy" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="step-to-processing-update" value="Processing update" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" parent="1" source="step" target="update-processing" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- External Synchronization -->
                <mxCell id="orbital-data-to-sync" value="Orbital data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="orbital-data" target="sync-with-orbital" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="comm-data-to-sync" value="Communication data" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="communication-data" target="sync-with-communication" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="task-data-to-receive" value="Task input" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="task-data" target="receive-task" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Configuration Initialization -->
                <mxCell id="config-to-load" value="Config read" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" parent="1" source="config-yaml" target="load-config" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="load-to-initialize" value="Initialize" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" parent="1" source="load-config" target="initialize-from-config" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Main Data Flow -->
                <mxCell id="main-data-flow" value="Main data flow&#10;(Time step loop)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" parent="1" source="satellite-node" target="step" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="750" y="350"/>
                            <mxPoint x="750" y="750"/>
                            <mxPoint x="725" y="750"/>
                        </Array>
                    </mxGeometry>
                </mxCell>

                <!-- Legend -->
                <mxCell id="legend-group" value="Legend" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" parent="1" vertex="1">
                    <mxGeometry x="50" y="150" width="200" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="legend-solid" value="Solid arrow: Direct call" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="40" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-dashed" value="Dashed arrow: Data transfer" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="65" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-thick" value="Thick line: Main data flow" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="90" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-blue" value="Dark blue: Main controller" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="115" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-green" value="Green: Data states" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="140" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-orange" value="Orange: Function methods" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="165" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-purple" value="Purple: External interfaces" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="legend-group" vertex="1">
                    <mxGeometry x="10" y="190" width="120" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>