# Communication.py 程序说明文档

## 1. 程序概述

### 1.1 核心功能和用途

Communication.py 是 SPACE-OAAL 卫星边缘计算仿真平台的通信管理模块，专门负责计算和管理LEO卫星星座中所有节点间的通信链路状态。该模块基于真实的射频通信物理模型，为卫星边缘计算系统提供准确的通信性能评估。

### 1.2 解决的主要问题

- **复杂链路计算统一化**：将卫星间、卫星-地面站、卫星-云中心等多种链路类型的计算统一到一个接口
- **物理模型准确性**：基于自由空间路径损耗、香农公式等标准通信理论进行精确计算
- **动态网络状态管理**：实时计算不同时间步下的网络拓扑和链路质量
- **参数化配置**：支持通过配置文件灵活调整通信参数

### 1.3 适用场景和应用领域

- **卫星网络仿真**：LEO/MEO卫星星座通信性能分析
- **边缘计算研究**：卫星边缘计算任务卸载和资源调度算法验证
- **网络优化**：卫星网络拓扑优化和路由算法测试
- **强化学习环境**：为RL算法提供真实的通信环境状态

## 2. 架构说明

### 2.1 程序整体结构

```
CommunicationManager (核心类)
├── 配置管理 (_load_config)
├── 轨道数据接口 (set_orbital_updater)
├── 物理计算模块
│   ├── 信号强度和SNR计算
│   ├── 数据速率计算 (香农公式)
│   ├── 传输延迟计算
│   └── 传输能耗计算
└── 网络状态管理
    ├── 链路状态计算 (get_all_link_states)
    ├── 单链路查询 (get_link_state)
    └── 邻居节点查询 (get_neighbors)
```

### 2.2 主要模块和组件关系

1. **配置层**：从 config.yaml 加载通信参数
2. **物理层**：实现射频通信的物理计算模型
3. **数据层**：与 orbital_updater 集成获取卫星位置和可见性
4. **接口层**：提供统一的链路状态查询接口

### 2.3 数据流向和处理逻辑

```
时间步输入 → 获取卫星位置 → 计算可见性矩阵 → 物理参数计算 → 链路状态输出
     ↓
orbital_updater.get_satellites_at_time()
     ↓
build_visibility_matrices()
     ↓
calculate_signal_strength_and_snr() + calculate_data_rate()
     ↓
{(source_id, target_id): {link_properties}}
```

## 3. 核心功能

### 3.1 支持的链路类型

程序支持5种双向通信链路：

| 链路类型 | 方向 | 功率参数 | 带宽参数 | 应用场景 | 特殊说明 |
|---------|------|----------|----------|----------|----------|
| `inter_satellite` | 卫星↔卫星 | config中p_su_w | 固定50Gbps | 星间数据中继 | 距离仅用于传播延迟 |
| `user_to_satellite` | 用户→卫星 | p_u_w (5W) | b_us_hz (100MHz) | 任务上传 | 支持多用户干扰 |
| `satellite_to_user` | 卫星→用户 | p_su_w (10W) | b_su_hz (120MHz) | 结果下载 | 下行链路 |
| `satellite_to_cloud` | 卫星→云 | p_sc_w (15W) | b_sc_hz (150MHz) | 任务卸载 | 高带宽链路 |
| `cloud_to_satellite` | 云→卫星 | p_c_w (50W) | b_cs_hz (180MHz) | 云端响应 | 大功率上行 |

### 3.2 物理计算模型

#### 3.2.1 信道增益计算 (Paper公式)
根据Paper中的路径损耗公式：
```python
# Paper公式: L_pl = (c/(4πdf))²
path_loss_linear = (light_speed / (4 * π * distance_m * frequency))²
channel_gain = path_loss_linear * antenna_gain_linear
```

#### 3.2.2 数据传输速率 (Paper公式)
考虑多用户干扰的香农公式：
```python
# Paper公式: R_us = B_us * log₂(1 + (P_u * |h_u,s|²) / (∑干扰 + σ²))
snr_linear = received_power / (interference_power + noise_power)
capacity = bandwidth * log2(1 + snr_linear) * coding_efficiency
```

#### 3.2.3 传输延迟模型 (Paper公式)
```python
# Paper公式: T_comm = T_tx + T_prop
# T_tx = S_k / R_us (传输延迟)
# T_prop = d / c (传播延迟)
total_delay = (data_size * 8) / data_rate + (distance * 1000) / light_speed * 1000
```

#### 3.2.4 传输能耗模型 (Paper公式)
```python
# Paper公式: E_comm = P * T_tx = P * (S_k / R_us)
energy = power * transmission_time
```

### 3.3 关键算法流程

1. **链路状态计算流程**：
   - 获取指定时间步的卫星位置
   - 基于orbital_updater构建可见性矩阵
   - 遍历所有可见链路计算物理参数
   - 返回完整的链路状态字典

2. **物理参数计算流程**：
   - 距离计算 → 路径损耗 → 信号强度 → SNR → 数据速率
   - 并行计算延迟和能耗参数

## 4. API接口文档

### 4.1 CommunicationManager 类

#### 4.1.1 初始化方法
```python
def __init__(self, config_file: str = "src/env/config.yaml")
```
**参数**：
- `config_file`: 配置文件路径，包含所有通信参数

**异常**：
- `FileNotFoundError`: 配置文件不存在
- `yaml.YAMLError`: 配置文件格式错误

#### 4.1.2 设置轨道更新器
```python
def set_orbital_updater(self, orbital_updater: 'OrbitalUpdater')
```
**参数**：
- `orbital_updater`: OrbitalUpdater实例，提供卫星位置和可见性数据

### 4.2 核心计算方法

#### 4.2.1 信道增益计算
```python
def calculate_channel_gain(self, distance: float) -> float
```
**参数**：
- `distance`: 链路距离 (km)

**返回值**：
- `float`: 信道增益 (线性值)

#### 4.2.2 信号强度和SNR计算
```python
def calculate_signal_strength_and_snr(self, distance: float, power: float, bandwidth: float, interference_power: float = 0.0) -> Tuple[float, float]
```
**参数**：
- `distance`: 链路距离 (km)
- `power`: 发射功率 (W)
- `bandwidth`: 通信带宽 (Hz)
- `interference_power`: 干扰功率 (W)

**返回值**：
- `Tuple[float, float]`: (信号强度 dBm, 信噪比 dB)

#### 4.2.3 数据速率计算
```python
def calculate_data_rate(self, distance: float, power: float, bandwidth: float = None, interference_power: float = 0.0) -> float
```
**参数**：
- `distance`: 链路距离 (km)
- `power`: 发射功率 (W)  
- `bandwidth`: 通信带宽 (Hz)，默认使用卫星-用户下行带宽
- `interference_power`: 同频干扰功率 (W)

**返回值**：
- `float`: 数据传输速率 (Mbps)

#### 4.2.4 传输延迟计算
```python
def calculate_transmission_delay(self, distance: float, data_size: float, data_rate: float = None) -> float
```
**参数**：
- `distance`: 链路距离 (km)
- `data_size`: 数据大小 (MB)
- `data_rate`: 数据速率 (Mbps)，为None时自动计算

**返回值**：
- `float`: 传输延迟 (ms)

**注意**：按照Paper公式，不包含processing_delay，仅计算传播延迟和传输延迟

#### 4.2.5 传输能耗计算
```python
def calculate_transmission_energy(self, distance: float, data_size: float, power: float, data_rate: float = None) -> float
```
**参数**：
- `distance`: 链路距离 (km)
- `data_size`: 数据大小 (MB)
- `power`: 发射功率 (W) - 根据Paper使用固定功率
- `data_rate`: 数据速率 (Mbps)，为None时自动计算

**返回值**：
- `float`: 传输能耗 (J)

### 4.3 主要接口方法

#### 4.3.1 获取所有链路状态
```python
def get_all_link_states(self, time_step: int) -> Dict[Tuple[str, str], Dict[str, float]]
```
**参数**：
- `time_step`: 时间步索引 (0-999)

**返回值**：
```python
{
    (source_id, target_id): {
        'distance_km': float,
        'data_rate_mbps': float, 
        'transmission_delay_ms': float,
        'transmission_energy_j': float,
        'signal_strength_dbm': float,
        'snr_db': float,
        'link_type': str  # 5种链路类型之一
    }
}
```

#### 4.3.2 获取特定链路状态
```python
def get_link_state(self, source_id: str, target_id: str, time_step: int) -> Optional[Dict[str, float]]
```

#### 4.3.3 获取节点邻居
```python
def get_neighbors(self, node_id: str, time_step: int) -> List[str]
```

### 4.4 配置参数说明

#### 4.4.1 功率参数 (单位: W)
- `p_u_w`: 用户终端发射功率 (5W)
- `p_su_w`: 卫星对用户发射功率 (10W) 
- `p_sc_w`: 卫星对云发射功率 (15W)
- `p_c_w`: 云中心发射功率 (50W)

#### 4.4.2 带宽参数 (单位: Hz)
- `b_us_hz`: 用户-卫星上行带宽 (100MHz)
- `b_su_hz`: 卫星-用户下行带宽 (120MHz)
- `b_sc_hz`: 卫星-云下行带宽 (150MHz)
- `b_cs_hz`: 云-卫星上行带宽 (180MHz)

#### 4.4.3 物理参数
- `rf_carrier_freq_hz`: 射频载波频率 (2.4GHz)
- `antenna_gain_db`: 天线增益 (20dB)
- `system_noise_dbm_hz`: 系统噪声功率密度 (-174dBm/Hz)
- `coding_efficiency`: 编码效率 (0.7)

## 5. 使用指南

### 5.1 环境配置要求

```python
# 依赖库
import math
import numpy as np
import yaml
from typing import Dict, List, Tuple, Optional

# 依赖模块
from orbital_updater import Satellite, GroundStation, OrbitalUpdater
```

### 5.2 快速开始示例

```python
# 1. 创建通信管理器
comm_manager = CommunicationManager("src/env/config.yaml")

# 2. 设置轨道更新器
from orbital_updater import OrbitalUpdater
orbital_updater = OrbitalUpdater()
comm_manager.set_orbital_updater(orbital_updater)

# 3. 获取某时间步的所有链路状态
time_step = 10
link_states = comm_manager.get_all_link_states(time_step)

# 4. 分析链路类型分布
for (source, target), link_data in link_states.items():
    print(f"{source} -> {target}: {link_data['link_type']}, "
          f"速率: {link_data['data_rate_mbps']:.2f} Mbps, "
          f"延迟: {link_data['transmission_delay_ms']:.2f} ms")

# 5. 计算特定任务的传输成本
task_size_mb = 10.0
user_power = 5.0  # W
link_distance = 1500  # km

# 计算传输延迟和能耗
delay = comm_manager.calculate_transmission_delay(link_distance, task_size_mb)
energy = comm_manager.calculate_transmission_energy(link_distance, task_size_mb, user_power)
print(f"任务传输延迟: {delay:.2f} ms, 能耗: {energy:.4f} J")
```

### 5.3 常见使用场景

#### 5.3.1 任务卸载链路选择
```python
def select_offload_target(user_id: str, time_step: int):
    # 获取用户的所有上行链路
    neighbors = comm_manager.get_neighbors(user_id, time_step)
    satellites = [n for n in neighbors if n.startswith('Satellite')]
    
    # 选择最佳卫星
    best_satellite = None
    best_rate = 0
    
    for sat_id in satellites:
        link = comm_manager.get_link_state(user_id, sat_id, time_step)
        if link and link['data_rate_mbps'] > best_rate:
            best_rate = link['data_rate_mbps']
            best_satellite = sat_id
    
    return best_satellite, best_rate
```

#### 5.3.2 网络性能统计
```python
def analyze_network_performance(time_step: int):
    link_states = comm_manager.get_all_link_states(time_step)
    
    # 按链路类型统计
    stats = {}
    for link_data in link_states.values():
        link_type = link_data['link_type']
        if link_type not in stats:
            stats[link_type] = {'count': 0, 'total_rate': 0, 'total_delay': 0}
        
        stats[link_type]['count'] += 1
        stats[link_type]['total_rate'] += link_data['data_rate_mbps']
        stats[link_type]['total_delay'] += link_data['transmission_delay_ms']
    
    # 计算平均值
    for link_type, data in stats.items():
        count = data['count']
        print(f"{link_type}:")
        print(f"  链路数: {count}")
        print(f"  平均速率: {data['total_rate']/count:.2f} Mbps")
        print(f"  平均延迟: {data['total_delay']/count:.2f} ms")
```

### 5.4 最佳实践建议

1. **初始化顺序**：先创建CommunicationManager，再设置OrbitalUpdater
2. **性能优化**：对于相同时间步的多次查询，缓存link_states结果
3. **错误处理**：检查orbital_updater是否已设置，避免返回空结果
4. **参数调优**：根据具体卫星系统调整config.yaml中的功率和带宽参数

## 6. 集成说明

### 6.1 在其他项目中引用

```python
# 方式1: 直接导入
from src.env.communication import CommunicationManager

# 方式2: 作为模块导入
import sys
sys.path.append('path/to/SPACE-DMPO1')
from src.env.communication import CommunicationManager
```

### 6.2 依赖关系和兼容性

**必需依赖**：
- `orbital_updater.py`: 提供卫星位置和可见性数据
- `config.yaml`: 通信参数配置文件
- Python 3.7+, NumPy, PyYAML

**可选依赖**：
- `pandas`: 用于数据分析（如需要）
- `matplotlib`: 用于结果可视化（如需要）

### 6.3 扩展和定制方法

#### 6.3.1 添加新链路类型
1. 在config.yaml中添加相应的功率和带宽参数
2. 在`get_all_link_states()`方法中添加新的链路计算逻辑
3. 更新测试代码中的链路类型统计

#### 6.3.2 自定义物理模型
```python
class CustomCommunicationManager(CommunicationManager):
    def calculate_data_rate(self, distance, power, bandwidth=None):
        # 自定义数据速率计算模型
        # 例如：考虑大气衰减、多径衰落等
        base_rate = super().calculate_data_rate(distance, power, bandwidth)
        atmospheric_factor = self.calculate_atmospheric_loss(distance)
        return base_rate * atmospheric_factor
```

#### 6.3.3 添加缓存机制
```python
from functools import lru_cache

class CachedCommunicationManager(CommunicationManager):
    @lru_cache(maxsize=128)
    def get_all_link_states(self, time_step):
        return super().get_all_link_states(time_step)
```

---

## 7. 更新日志

### v1.1 (2025-01-27)
- **重大更新**：根据Paper公式重构物理计算模型
- 新增`calculate_channel_gain()`方法，使用Paper中的路径损耗公式
- 修改数据速率计算，支持多用户干扰参数
- 调整传输延迟计算，移除processing_delay
- 修改能耗计算，使用固定功率而非距离调整
- 星间链路采用固定速率，距离仅用于传播延迟
- 删除测试主函数，代码更简洁

### v1.0 (2025-01-26)
- 初始版本发布
- 基础通信模型实现
- 支持5种链路类型
- 集成orbital_updater

---

**版本信息**：v1.1  
**最后更新**：2025-01-27  
**维护者**：SPACE-OAAL开发团队