#!/usr/bin/env python3
"""
快速测试修复后的satellite_env.py
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from satellite_env import parallel_env, aec_env

def test_fixed_env():
    """测试修复后的环境"""
    print("=== 测试修复后的SPACE-OAAL PettingZoo环境 ===")
    
    # 使用绝对路径
    config_path = os.path.join(os.path.dirname(__file__), "config.yaml")
    
    try:
        # 测试并行环境
        print("\n1. 测试扩展动作空间的并行环境...")
        env = parallel_env(config_file=config_path)
        observations, infos = env.reset()
        
        print(f"环境创建成功: {len(observations)} 个智能体")
        print(f"观测空间: {env.observation_space}")
        print(f"动作空间: {env.action_space} (大小: {env.action_space.n})")
        
        # 测试动作掩码功能
        for agent in list(env.agents)[:3]:  # 只测试前3个智能体
            valid_actions = infos[agent]['valid_actions']
            action_mask = infos[agent]['valid_actions_mask']
            print(f"{agent} 可用动作: {valid_actions}, 掩码长度: {len(action_mask)}")
        
        # 执行一个时间步，测试奖励是否基于步长增量
        actions = {}
        for agent in env.agents:
            # 从有效动作中随机选择一个
            valid_actions = infos[agent]['valid_actions']
            actions[agent] = valid_actions[0] if valid_actions else 0
        
        observations, rewards, dones, infos = env.step(actions)
        
        print(f"时间步1: 奖励 = {list(rewards.values())[:5]}")  # 显示前5个奖励
        print(f"所有奖励非零: {any(r != 0 for r in rewards.values())}")
        
        env.close()
        print("修复后的并行环境测试完成 ✅")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_fixed_env()
    if success:
        print("\n🎉 所有关键RL机制修复成功验证!")
    else:
        print("\n❌ 测试失败，需要进一步调试")