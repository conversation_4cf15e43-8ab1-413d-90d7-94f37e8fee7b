# Satellite.py 程序说明文档

## 1. 程序概述

### 1.1 核心功能和用途

`satellite.py` 是 SPACE-OAAL（轨道感知自适应学习框架）基础环境中的**卫星实体核心模块**，实现了LEO卫星星座边缘计算仿真平台的基础卫星节点功能。

**主要功能**：
- 卫星物理状态管理（位置、能量、计算资源）
- 任务队列管理和基础调度
- 与轨道、通信、任务模块的集成接口
- 为强化学习算法提供标准化观测接口

### 1.2 解决的主要问题

- **卫星状态建模**：准确模拟LEO卫星的物理特性和资源约束
- **任务处理仿真**：实现边缘计算任务的接收、调度和处理流程
- **模块集成**：提供与orbital_updater、communication、task模块的标准化接口
- **数据标准化**：为上层算法提供一致的状态观测和控制接口

### 1.3 适用场景和应用领域

- **卫星边缘计算仿真**：大规模LEO星座的计算任务调度研究
- **强化学习算法验证**：多智能体决策算法的测试平台
- **系统性能评估**：卫星网络资源利用率和服务质量分析
- **算法对比研究**：不同调度策略的性能基准测试

## 2. 架构说明

### 2.1 程序整体结构

```
SatelliteNode (核心类)
├── 状态管理层
│   ├── Position (位置状态)
│   ├── EnergyState (能量状态)
│   ├── ResourceState (计算资源状态)
│   ├── CommunicationState (通信状态)
│   └── PerformanceMetrics (性能指标)
├── 功能处理层
│   ├── 任务队列管理
│   ├── 能量管理
│   ├── 状态更新机制
│   └── 调度算法
└── 接口集成层
    ├── 轨道数据同步
    ├── 通信模块集成
    ├── 任务对象管理
    └── 观测数据导出
```

### 2.2 主要模块和组件关系

```python
# 外部依赖关系
SatelliteNode
├── depends on → orbital_updater.py (位置和可见性数据)
├── depends on → communication.py (链路状态和邻居信息)
├── depends on → task.py (任务对象和状态管理)
└── depends on → config.yaml (系统参数配置)

# 数据流向
卫星轨道数据 → orbital_updater → SatelliteNode
任务生成数据 → task.py → SatelliteNode
网络拓扑数据 → communication → SatelliteNode
配置参数 → config.yaml → SatelliteNode
```

### 2.3 数据流向和处理逻辑

1. **初始化阶段**：从config.yaml加载配置 → 初始化各状态组件
2. **时间步更新**：接收外部状态更新 → 内部状态计算 → 任务调度处理
3. **数据导出**：聚合内部状态 → 格式化输出 → 提供给上层算法

## 3. 核心功能

### 3.1 状态管理系统

**功能描述**：维护卫星的多维状态信息，包括位置、能量、资源等。

**输入输出规格**：
- 输入：外部模块的状态更新数据
- 输出：标准化的状态查询接口

**关键处理流程**：
```python
def step(self, time_step: int, time_delta: float):
    # 1. 更新时间状态
    self.current_timeslot = time_step
    self.current_time = time_step * time_delta
    
    # 2. 更新各个子状态
    self.update_energy(time_delta)      # 能量管理
    self.update_processing(time_delta)  # 任务处理
    
    # 3. 调度新任务
    self.schedule_next_task()
```

### 3.2 能量管理系统

**功能描述**：模拟卫星的太阳能充电、基础功耗和任务处理能耗。

**关键算法**：
```python
def update_energy(self, time_delta: float):
    # 太阳能充电
    if self.energy_state.is_illuminated:
        charge_energy = self.energy_state.solar_power_w * time_delta
        self.energy_state.current_battery_j = min(
            self.energy_state.current_battery_j + charge_energy,
            self.energy_state.battery_capacity_j
        )
    
    # 功耗计算
    total_consumption = self.base_power + self.processing_power
    self.energy_state.current_battery_j -= total_consumption * time_delta
```

### 3.3 任务队列管理

**功能描述**：实现任务的接收、排队、调度和处理生命周期管理。

**调度算法**：基于优先级的贪心调度
```python
def schedule_next_task(self) -> bool:
    # 筛选可执行任务（未超时）
    eligible_tasks = [t for t in self.task_queue 
                     if t.deadline_timestamp > self.current_time]
    
    # 选择优先级最高的任务
    if eligible_tasks:
        selected_task = max(eligible_tasks, key=lambda t: t.priority)
        self.current_processing_task = selected_task
        return True
    return False
```

## 4. API接口文档

### 4.1 核心类 SatelliteNode

#### 初始化方法

```python
def __init__(self, satellite_id: str, config: Dict = None)
```

**参数**：
- `satellite_id`: 卫星标识符（如"Satellite111"）
- `config`: 配置字典，默认从config.yaml加载

**功能**：创建卫星节点实例并初始化所有状态组件

#### 主要方法

##### 状态更新方法

```python
def step(self, time_step: int, time_delta: float)
```
- **功能**：执行一个仿真时间步
- **参数**：
  - `time_step`: 时间步索引（0-999）
  - `time_delta`: 时间间隔（秒）

```python
def sync_with_orbital_state(self, orbital_satellite)
```
- **功能**：与orbital_updater同步位置和光照状态
- **参数**：`orbital_satellite`: 轨道卫星对象

##### 任务管理方法

```python
def receive_task(self, task: Task) -> bool
```
- **功能**：接收新任务到队列
- **参数**：`task`: Task对象
- **返回值**：成功接收返回True

```python
def offload_task(self, task: Task, target_satellite_id: str) -> bool
```
- **功能**：将任务卸载给其他卫星
- **参数**：
  - `task`: 要卸载的任务
  - `target_satellite_id`: 目标卫星ID
- **返回值**：成功开始卸载返回True

##### 数据导出方法

```python
def export_observation_data(self) -> Dict[str, Any]
```
- **功能**：导出完整的观测数据用于强化学习
- **返回值**：包含位置、能量、资源、任务等信息的字典

```python
def export_for_scheduling(self) -> Dict[str, Any]
```
- **功能**：导出调度算法所需的关键状态
- **返回值**：调度决策相关的状态信息

### 4.2 配置参数说明

**主要配置项**（config.yaml）：

```yaml
system:
  leo_altitude_m: 1200000          # LEO卫星轨道高度
  total_timeslots: 1000            # 总仿真时隙数
  
computation:
  f_leo_hz: 10e9                   # CPU频率 (Hz)
  leo_battery_capacity_j: 3600000  # 电池容量 (J)
  leo_solar_power_w: 500           # 太阳能功率 (W)
```

## 5. 使用指南

### 5.1 环境配置要求

**依赖项**：
```python
pandas >= 1.3.0
numpy >= 1.21.0
pyyaml >= 5.4.0
```

**数据文件**：
- `satellite_processed_data1.csv`: 卫星轨道数据
- `task_generation_results.json`: 任务生成数据
- `config.yaml`: 系统配置文件

### 5.2 快速开始示例

```python
# 基础使用示例
from satellite import SatelliteNode
from task import Task

# 1. 创建卫星实例
satellite = SatelliteNode("Satellite111")

# 2. 创建并接收任务
task_data = {
    "task_id": 1001,
    "type_id": 1,
    "data_size_mb": 10.0,
    "complexity_cycles_per_bit": 1000,
    "deadline_timestamp": 100.0,
    "priority": 5
}
task = Task(task_data, source_location_id=1, generation_timestamp=0.0)
satellite.receive_task(task)

# 3. 执行仿真时间步
for step in range(10):
    satellite.step(step, 10.0)  # 每步10秒
    
    # 获取状态信息
    status = satellite.get_status_summary()
    print(f"Step {step}: Energy={status['energy']['battery_ratio']:.1%}")
```

### 5.3 模块集成示例

```python
# 完整集成示例
from satellite import SatelliteNode
from orbital_updater import OrbitalUpdater
from communication import CommunicationManager

# 1. 创建外部模块
orbital_updater = OrbitalUpdater()
comm_manager = CommunicationManager()
comm_manager.set_orbital_updater(orbital_updater)

# 2. 创建卫星并设置外部模块
satellite = SatelliteNode("Satellite111")
satellite.set_external_modules(orbital_updater, comm_manager)

# 3. 同步状态
satellites_data = orbital_updater.get_satellites_at_time(0)
if "Satellite111" in satellites_data:
    satellite.sync_with_orbital_state(satellites_data["Satellite111"])

# 4. 获取强化学习观测数据
obs_data = satellite.export_observation_data()
```

### 5.4 最佳实践建议

1. **配置管理**：统一通过config.yaml管理参数，避免硬编码
2. **状态同步**：每个时间步都要调用相应的sync方法更新状态
3. **资源监控**：定期检查能量和队列状态，避免资源耗尽
4. **错误处理**：合理处理任务接收失败和处理异常的情况

## 6. 集成说明

### 6.1 项目引用方式

**标准导入**：
```python
from src.env.satellite import SatelliteNode, SatelliteStatus
```

**与SPACE-OAAL环境集成**：
```python
# 在satellite_env.py中使用
class SatelliteEnvironment:
    def __init__(self):
        self.satellites = {}
        for i in range(36):
            sat_id = f"Satellite{111+i}"
            self.satellites[sat_id] = SatelliteNode(sat_id)
```

### 6.2 依赖关系

**核心依赖**：
- `orbital_updater.py`: 必需，提供位置和可见性数据
- `communication.py`: 必需，提供链路状态信息
- `task.py`: 必需，提供任务对象定义
- `config.yaml`: 必需，系统参数配置

**可选依赖**：
- 强化学习框架（如 Gym、Ray）
- 数据分析库（如 matplotlib、seaborn）

### 6.3 扩展和定制方法

#### 自定义调度算法

```python
class CustomSatelliteNode(SatelliteNode):
    def schedule_next_task(self) -> bool:
        # 实现自定义调度逻辑
        # 例如：基于截止时间的EDF调度
        if not self.task_queue:
            return False
        
        # 选择截止时间最近的任务
        selected_task = min(self.task_queue, 
                          key=lambda t: t.deadline_timestamp)
        
        self.task_queue.remove(selected_task)
        self.current_processing_task = selected_task
        return True
```

#### 扩展状态信息

```python
# 添加自定义状态组件
@dataclass
class CustomState:
    custom_metric: float = 0.0

class ExtendedSatelliteNode(SatelliteNode):
    def __init__(self, satellite_id: str, config: Dict = None):
        super().__init__(satellite_id, config)
        self.custom_state = CustomState()
    
    def export_observation_data(self) -> Dict[str, Any]:
        base_data = super().export_observation_data()
        base_data['custom'] = {
            'custom_metric': self.custom_state.custom_metric
        }
        return base_data
```

## 7. 测试和验证

### 7.1 运行测试

```bash
# 基础功能测试
cd src/env
python test_satellite_simple.py

# 直接运行主程序
python satellite.py
```

### 7.2 测试覆盖范围

- ✅ 卫星节点创建和初始化
- ✅ 能量管理系统（充电/放电）
- ✅ 任务接收和队列管理
- ✅ 时间步仿真执行
- ✅ 数据导出接口
- ✅ 模块集成接口

## 8. 常见问题

### 8.1 配置文件加载失败

**问题**：`config.yaml`文件找不到  
**解决**：确保配置文件在正确路径，或使用绝对路径

### 8.2 数据类型错误

**问题**：配置参数类型不匹配  
**解决**：检查config.yaml中数值参数格式，确保数值不被引号包围

### 8.3 模块导入失败

**问题**：无法导入相关模块  
**解决**：检查Python路径设置，确保所有依赖模块在同一目录

---

**版本信息**：v1.0  
**更新日期**：2025-07-27  
**维护团队**：SPACE-OAAL开发组  
**文档位置**：`src/env/SATELLITE_README.md`