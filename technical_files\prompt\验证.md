SPACE-OAAL环境验证测试程序开发提示词
项目背景
你正在为一个名为SPACE-OAAL的卫星网络任务调度仿真系统开发测试程序。该系统是一个复杂的多模块集成环境，包含：

核心模块架构
轨道更新模块 (orbital_updater.py) - 管理36颗LEO卫星的轨道状态和位置更新
卫星节点模块 (satellite.py) - 卫星实体的状态管理、资源分配、任务处理
任务管理模块 (task.py) - 任务生命周期管理、动态优先级计算
通信管理模块 (communication.py) - 卫星间链路、地面站通信、网络状态
任务生成模块 (task_generator.py) - 基于420个地面站的任务生成
环境接口模块 (env_interface.py) - 标准化的RL环境接口
适配器模块 (adapters.py) - 模块间数据转换和集成管理
系统关键参数
卫星数量: 36颗LEO卫星 (6×6 Walker Delta星座)
地面站数量: 420个用户终端
时隙设置: 1000个时隙，每时隙10秒
轨道高度: 1200km
动作空间: 3种动作 (本地处理、卫星卸载、云卸载)
测试需求
主要目标
请帮我开发一个Jupyter Notebook测试程序，要求：

模块化验证 - 分别验证各个模块的核心功能
集成验证 - 验证模块间的协同工作
直观展示 - 通过可视化和数据表格展示验证结果
任务处理验证 - 给出具体任务，验证系统是否正确处理
时隙计算验证 - 检查多个时隙的计算结果正确性
具体验证内容
1. 轨道更新验证
验证36颗卫星在不同时隙的位置更新
检查卫星间可见性矩阵计算
验证卫星-地面站连接状态
可视化卫星轨道和覆盖范围
2. 任务生成与管理验证
验证420个地面站的任务生成
检查任务优先级计算和排队机制
验证任务状态转换 (生成→排队→处理→完成/失败)
展示任务分布统计
3. 卫星节点功能验证
验证卫星资源状态 (CPU、内存、能量)
检查任务队列管理和调度
验证能量消耗和充电机制
测试任务卸载功能
4. 通信网络验证
验证星间链路建立和质量计算
检查地面站通信连接
验证数据传输速率和延迟计算
展示网络拓扑变化
5. 环境接口验证
验证状态空间格式化
检查动作执行和奖励计算
验证环境步进逻辑
测试重置和终止条件
6. 集成系统验证
运行完整的多时隙仿真
验证模块间数据流转
检查系统性能指标
展示端到端的任务处理流程
验证方式要求
直观验证示例
任务处理验证:

给定任务: [task_001: 10MB, 优先级3, 截止时间100s]
预期结果: 分配到卫星SAT_001, 处理时间45s, 成功完成
实际结果: [显示实际处理过程和结果]
时隙计算验证:

时隙0: 生成15个任务, 8个卫星活跃, 网络连接度0.75
时隙1: 生成12个任务, 8个卫星活跃, 网络连接度0.68
时隙2: 生成18个任务, 7个卫星活跃, 网络连接度0.72
资源状态验证:

SAT_001: CPU利用率65%, 内存使用2.1GB, 电池85%, 队列长度3
SAT_002: CPU利用率42%, 内存使用1.8GB, 电池92%, 队列长度1
代码结构要求
Notebook结构
1. 环境设置和导入
2. 配置参数加载
3. 模块单元测试
   3.1 轨道更新模块测试
   3.2 任务生成模块测试  
   3.3 卫星节点模块测试
   3.4 通信管理模块测试
   3.5 环境接口模块测试
4. 集成测试
   4.1 短期仿真 (10个时隙)
   4.2 中期仿真 (100个时隙)
   4.3 性能压力测试
5. 结果分析和可视化
6. 问题诊断和建议
关键验证点
数据一致性: 模块间数据传递无丢失
时序正确性: 时隙推进和状态更新同步
资源约束: 能量、CPU、内存限制有效
任务完整性: 任务从生成到完成的全流程
网络连通性: 通信链路建立和断开正确
性能指标: 任务完成率、平均延迟、能耗效率
输出要求
每个测试模块都要有通过/失败的明确结论
提供数值对比表格展示关键指标
使用matplotlib/seaborn进行数据可视化
对发现的问题提供具体的修复建议
生成测试报告摘要便于快速了解系统状态
技术细节
使用项目现有的配置文件 config.yaml
利用 tests/utils/ 下的辅助工具
遵循现有的代码风格和错误处理机制
确保测试程序可以独立运行，不依赖外部数据
请基于以上要求，开发一个完整的Jupyter Notebook测试程序，帮助验证SPACE-OAAL环境的各个组件功能是否正常工作。
