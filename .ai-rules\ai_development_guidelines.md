# AI开发规范提示词 - 卫星边缘计算科研项目

## 项目背景
你正在协助完成一个关于**轨道感知自适应学习框架（OAAL）**的大型科研项目。该项目专注于大规模低地球轨道（LEO）卫星星座的边缘计算环境中的资源调度挑战，涉及分布式多智能体决策、强化学习、图神经网络等多个技术领域。

## 核心开发规范

### 1. 文件组织规范

#### 测试文件规范
- **位置**: 所有测试文件必须保存在 `tests/` 文件夹中
- **格式**: 测试文件必须使用 **Jupyter Notebook格式** (.ipynb)
- **命名**: 测试文件命名格式为 `test_[模块名].ipynb`
- **目的**: 方便程序中各个模块的单独测试和验证

#### 测试内容要求
- 每个测试文件应包含**多个测试用例**，覆盖不同场景
- 测试用例应该**直观易懂**，能够清晰展示测试结果
- 包含**预期输出**和**实际输出**的对比
- 添加必要的**可视化图表**来展示测试结果

### 2. 开发前必读文档

在编写任何程序之前，你必须仔细阅读以下文档：

1. **项目架构文档** (`paper.md`) - 了解整体系统设计和理论基础
2. **各模块开发指南** - 理解具体模块的实现要求
3. **技术规范文档** - 掌握编码标准和接口定义

### 3. 程序开发规范

#### 代码结构
- 遵循模块化设计原则
- 每个模块应有清晰的接口定义
- 添加详细的**中文注释**和**docstring**
- 使用类型提示（Type Hints）

#### 核心模块说明
基于项目结构，重点关注以下模块：

**智能体模块** (`src/agent/`)
- 实现LEO卫星智能体的决策逻辑
- 包含Actor-Critic网络架构
- 实现DPPO算法和策略域机制

**环境模块** (`src/env/`)
- 卫星网络环境仿真
- 任务生成和通信建模
- 故障模型和拓扑变化处理

#### 编程最佳实践
- 使用面向对象编程
- 实现适当的错误处理和日志记录
- 确保代码的可重现性
- 优化计算性能，特别是大规模仿真场景

### 4. 测试驱动开发

#### 测试用例设计
为每个模块创建comprehensive测试用例：

**示例测试场景**：
```python
# 测试用例1: 基本功能验证
def test_basic_functionality():
    # 创建测试环境
    # 执行基本操作
    # 验证预期结果
    pass

# 测试用例2: 边界条件测试
def test_edge_cases():
    # 测试极端参数
    # 验证错误处理
    pass

# 测试用例3: 性能测试
def test_performance():
    # 测试大规模场景
    # 验证时间复杂度
    pass
```

#### 可视化测试结果
- 使用matplotlib/seaborn创建测试结果图表
- 展示算法收敛曲线
- 对比不同参数设置的性能
- 生成测试报告和总结

### 5. 文档和总结规范

#### 程序总结文档
每完成一个模块后，在 `tests/` 文件夹中创建对应的总结文档：

**文档内容包括**：
- 模块功能概述
- 关键算法实现说明
- 测试结果分析
- 性能评估
- 已知问题和改进建议

#### 文档格式
- 使用Markdown格式
- 包含代码示例和运行结果
- 添加必要的数学公式（LaTeX格式）
- 提供清晰的图表和可视化

### 6. 项目特定要求

#### 卫星网络仿真
- 实现36颗LEO卫星的Walker Delta星座
- 模拟动态网络拓扑变化
- 考虑星间链路（ISL）的激光通信特性

#### 强化学习实现
- 基于DPPO的多智能体框架
- 实现策略域机制和GEO-LEO分层架构
- 包含故障恢复和容错机制

#### 性能指标
重点关注以下性能指标：
- 任务完成率
- 端到端时延
- 系统能效
- 负载均衡度

### 7. 代码质量保证

#### 代码审查清单
- [ ] 代码符合PEP 8规范
- [ ] 包含完整的中文注释
- [ ] 实现了错误处理
- [ ] 通过了所有测试用例
- [ ] 性能满足要求
- [ ] 文档完整且准确

#### 版本控制
- 使用有意义的commit消息
- 定期提交代码更改
- 为重要功能创建分支

## 开发流程

1. **需求分析** - 理解具体模块的功能需求
2. **设计阶段** - 设计模块架构和接口
3. **实现阶段** - 编写核心功能代码
4. **测试阶段** - 创建测试用例并验证功能
5. **文档阶段** - 编写测试总结和程序文档
6. **优化阶段** - 性能优化和代码重构

## 注意事项

- 始终考虑代码的**可维护性**和**可扩展性**
- 重视**代码复用**，避免重复实现
- 确保所有实现都有对应的**测试验证**
- 保持与项目整体架构的**一致性**
- 及时更新文档，确保文档与代码同步

---

**记住**: 这是一个复杂的科研项目，需要严格的工程规范来保证代码质量和研究结果的可靠性。每个模块的实现都应该经过充分的测试和验证。