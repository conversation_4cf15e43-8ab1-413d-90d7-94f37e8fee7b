# 任务模块程序架构图

## 1. 程序架构概述

任务模块是一个完整的任务生命周期管理系统，包含任务生成、任务实体管理和任务调度支持。系统采用分层架构设计，以 `Task` 为核心实体，`TaskManager` 为管理控制器，`TaskGenerator` 为任务生成器。架构特点包括：

- **完整生命周期管理**：从任务生成到完成/失败的全流程管理
- **动态优先级计算**：基于时间紧迫性和资源成本的动态优先级算法
- **状态机管理**：严格的任务状态转换控制和历史记录
- **多维度统计**：处理记录、传输记录、重试记录等详细统计
- **调度器接口**：为上层调度器提供标准化的任务信息接口
- **数据驱动设计**：基于JSON任务生成数据和YAML配置文件

## 2. Draw.io架构图绘制指导

### 2.1 图形元素和颜色方案

#### 主要模块形状建议：
- **核心实体类**：深绿色矩形 (#2d7600, 宽度200px, 高度85px)
- **管理控制器类**：深蓝色矩形 (#1f4e79, 宽度190px, 高度80px)
- **生成器类**：深橙色矩形 (#d9730d, 宽度180px, 高度75px)
- **数据结构类**：浅绿色圆角矩形 (#70ad47, 宽度150px, 高度65px, 圆角半径10px)
- **枚举类**：紫色菱形 (#7030a0, 宽度110px, 高度50px)
- **核心功能函数**：橙色圆角矩形 (#f4b942, 宽度170px, 高度50px, 圆角半径8px)
- **工具函数**：灰色圆角矩形 (#7f7f7f, 宽度140px, 高度45px, 圆角半径6px)
- **数据文件**：黄色平行四边形 (#ffc000, 宽度140px, 高度50px)
- **配置文件**：浅黄色平行四边形 (#fff2cc, 宽度130px, 高度45px)
- **主函数入口**：红色六边形 (#c5504b, 宽度100px, 高度60px)

#### 连接线类型：
- **实线箭头**：直接函数调用 (线宽2px, 黑色 #000000)
- **虚线箭头**：数据传递/依赖 (虚线, 线宽1.5px, 深灰色 #404040)
- **双向箭头**：相互调用关系 (线宽2px, 蓝色 #0066cc)
- **粗线**：主要数据流 (线宽3px, 绿色 #00aa00)
- **点线**：状态转换 (点线, 线宽1.5px, 紫色 #7030a0)

### 2.2 布局建议（从上到下）

```
第1层：程序入口层 (Y坐标: 50px)
├── main() 函数 [红色六边形]

第2层：数据源层 (Y坐标: 150px)
├── task_generation_results.json [黄色平行四边形]
├── config.yaml [浅黄色平行四边形]
└── updated_global_ground_stations.csv [黄色平行四边形]

第3层：枚举和数据结构层 (Y坐标: 250px)
├── TaskState枚举 [紫色菱形]
├── ProcessingRecord数据类 [浅绿色圆角矩形]
├── TransferRecord数据类 [浅绿色圆角矩形]
└── RetryRecord数据类 [浅绿色圆角矩形]

第4层：核心实体层 (Y坐标: 360px)
├── Task类 [深绿色矩形]
├── Location类 [深绿色矩形]

第5层：管理控制层 (Y坐标: 480px)
├── TaskManager类 [深蓝色矩形]
├── TaskLoader类 [深蓝色矩形]
└── TaskGenerator类 [深橙色矩形]

第6层：核心功能层 (Y坐标: 600px)
├── calculate_dynamic_priority() [橙色圆角矩形]
├── update_state() [橙色圆角矩形]
├── start_processing() [橙色圆角矩形]
├── transfer_to_satellite() [橙色圆角矩形]
├── load_tasks_for_timeslot() [橙色圆角矩形]
└── generate_tasks_for_location() [橙色圆角矩形]

第7层：业务服务层 (Y坐标: 720px)
├── get_high_priority_tasks() [橙色圆角矩形]
├── get_tasks_by_satellite() [橙色圆角矩形]
├── export_tasks_for_scheduling() [橙色圆角矩形]
├── calculate_lambda() [橙色圆角矩形]
└── sample_task_type() [橙色圆角矩形]

第8层：工具函数层 (Y坐标: 840px)
├── _load_config() [灰色圆角矩形]
├── get_statistics() [灰色圆角矩形]
├── to_dict() [灰色圆角矩形]
├── get_resource_requirements() [灰色圆角矩形]
└── save_results_to_file() [灰色圆角矩形]
```

## 3. 程序模块详细描述

### 3.1 核心实体模块 - Task类
- **程序模块名称**：任务实体核心类
- **文件位置**：`src/env/task.py` (第45-350行)
- **主要功能和职责**：
  - 任务生命周期管理和状态转换
  - 动态优先级计算和截止时间检查
  - 处理进度跟踪和资源消耗统计
  - 传输记录和重试机制管理

- **关键方法**：
  ```python
  calculate_dynamic_priority(current_time, estimated_time) -> float
  update_state(new_state, satellite_id) -> bool
  start_processing(satellite_id, start_time) -> bool
  update_processing_progress(cycles, energy, time)
  transfer_to_satellite(from_sat, to_sat, time, energy, success)
  get_resource_requirements() -> Dict
  ```

### 3.2 管理控制模块 - TaskManager类
- **程序模块名称**：任务管理器
- **文件位置**：`src/env/task.py` (第420-550行)
- **主要功能和职责**：
  - 任务集合管理和状态跟踪
  - 按卫星、状态、优先级分组查询
  - 调度器接口和系统统计

- **关键方法**：
  ```python
  load_tasks_for_timeslot(timeslot, config) -> List[Task]
  get_high_priority_tasks(current_time, top_n) -> List[Task]
  export_tasks_for_scheduling(satellite_id, current_time) -> List[Dict]
  get_system_statistics() -> Dict[str, Any]
  ```

### 3.3 任务生成模块 - TaskGenerator类
- **程序模块名称**：任务生成器
- **文件位置**：`src/env/task_generator.py` (第50-200行)
- **主要功能和职责**：
  - 基于地理位置的任务生成模拟
  - 泊松分布任务数量生成
  - 任务类型和参数采样

- **关键方法**：
  ```python
  generate_tasks_for_location(location, current_time) -> List[Task]
  calculate_lambda(location) -> float
  sample_task_type(functional_type) -> int
  run_simulation() -> Dict[str, Any]
  ```

## 4. 程序执行流程说明

### 4.1 任务加载和管理流程
```
load_tasks_for_timeslot(timeslot) [核心接口]
    ↓ [实线箭头]
    1. TaskLoader.get_tasks_for_timeslot()
       ├── load_task_data() 加载JSON数据
       └── 提取指定时隙数据
    ↓ [实线箭头]
    2. 创建Task实例
       ├── Task.__init__() 初始化任务
       ├── _load_config() 加载配置
       └── 初始化状态为GENERATED
    ↓ [实线箭头]
    3. 添加到TaskManager.active_tasks
```

### 4.2 任务状态管理流程
```
update_task_state(task_id, new_state) [状态管理核心]
    ↓ [实线箭头]
    1. 获取任务实例
    ↓ [点线箭头]
    2. 检查状态转换有效性
    ↓ [实线箭头]
    3. 更新状态和统计信息
    ↓ [实线箭头]
    4. 根据新状态移动任务
```

### 4.3 动态优先级计算流程
```
calculate_dynamic_priority(current_time) [优先级计算]
    ↓ [实线箭头]
    1. 计算优先级因子 (priority)
    ↓ [实线箭头]
    2. 计算紧迫性因子 (1/(deadline-now))
    ↓ [实线箭头]
    3. 计算成本因子 (estimated_processing_time)
    ↓ [实线箭头]
    4. 综合计算最终分数
```

## 5. 接口调用分析

### 5.1 主要接口调用关系

| 调用方 | 被调用方 | 接口函数名称 | 调用频率 | 数据传递内容 |
|--------|----------|-------------|----------|-------------|
| 外部调用 | TaskManager | `load_tasks_for_timeslot()` | 每时隙1次 | 时隙号→任务列表 |
| 外部调用 | TaskManager | `get_high_priority_tasks()` | 高频调用 | 当前时间→高优先级任务 |
| 外部调用 | Task | `calculate_dynamic_priority()` | 极高频 | 当前时间→优先级分数 |
| 外部调用 | Task | `update_state()` | 高频调用 | 新状态→操作结果 |
| TaskManager | TaskLoader | `get_tasks_for_timeslot()` | 每时隙1次 | 时隙号→任务列表 |

### 5.2 关键性能瓶颈分析

1. **动态优先级计算瓶颈**：每次调度时被大量调用
2. **状态转换瓶颈**：频繁的状态检查和转换操作
3. **任务查询瓶颈**：按卫星、状态、优先级的复杂查询操作

这个架构图展示了任务模块的完整结构、复杂的状态管理机制、动态优先级算法和完整的生命周期管理流程。