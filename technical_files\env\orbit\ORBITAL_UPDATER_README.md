# Orbital Updater Module 说明文档

## 1. 程序概述

### 1.1 核心功能和用途
Orbital Updater Module 是 SPACE-OAAL 仿真环境中的核心轨道管理组件，负责统一管理卫星轨道信息和地面设施（地面用户站+云中心），提供完整的可见性分析和地面覆盖计算能力。该模块为整个卫星网络仿真提供基础的时空数据支撑。

### 1.2 解决的主要问题
- **统一轨道管理**：集中管理卫星轨道数据和位置更新
- **地面设施统一管理**：统一处理地面用户站和云中心的位置和属性
- **分层可见性计算**：提供卫星间、卫星-地面用户、卫星-云中心的差异化可见性分析
- **动态覆盖分析**：实时计算卫星对地面区域的覆盖能力
- **性能优化**：高效的矩阵运算和批量处理能力

### 1.3 适用场景和应用领域
- **LEO/MEO卫星星座仿真**：支持大规模卫星网络建模
- **混合地面设施网络**：统一管理用户终端和云计算中心
- **通信覆盖分析**：多层次的地面通信覆盖评估
- **网络拓扑优化**：基于真实轨道数据的网络设计
- **任务调度系统**：为卫星任务分配提供可见性依据

## 2. 架构说明

### 2.1 整体结构
```
Orbital Updater Module
├── 核心控制层
│   └── OrbitalUpdater           # 主轨道管理器
├── 数据模型层
│   ├── Satellite               # 卫星实体类
│   └── GroundStation           # 地面设施实体类（统一）
├── 核心功能层
│   ├── get_satellites_at_time()              # 卫星状态获取
│   ├── build_inter_satellite_visibility_matrix()     # 卫星间可见性
│   ├── build_satellite_ground_visibility_matrix()    # 卫星-地面用户可见性
│   ├── build_satellite_cloud_visibility_matrix()     # 卫星-云中心可见性
│   └── build_complete_ground_visibility_matrix()     # 完整地面设施可见性
├── 计算服务层
│   ├── calculate_satellite_visibility()              # 卫星间可见性计算
│   ├── calculate_satellite_ground_visibility()       # 地面用户可见性计算
│   ├── calculate_satellite_cloud_visibility()        # 云中心可见性计算
│   └── get_all_ground_facilities()                   # 地面设施统一访问
├── 工具函数层
│   ├── _load_config()           # 配置加载
│   ├── _load_satellite_data()   # 卫星数据加载
│   ├── _create_ground_stations() # 地面用户站创建
│   ├── _create_cloud_stations()  # 云中心创建
│   └── _calculate_distance()     # 距离计算
└── 数据源
    ├── config.yaml              # 系统配置
    ├── satellite_processed_data.csv    # 卫星轨道数据
    ├── updated_global_ground_stations.csv # 地面用户站数据
    └── cloud_station.csv        # 云中心数据
```

### 2.2 主要模块和组件关系
- **OrbitalUpdater**：核心管理器，协调所有轨道和地面设施相关计算
- **统一地面设施管理**：地面用户站和云中心使用相同的 GroundStation 类
- **分层可见性计算**：不同类型设施使用不同的可见性阈值和算法
- **矩阵运算优化**：提供单独和组合的可见性矩阵构建能力

### 2.3 数据流向和处理逻辑
```
[配置加载] → [数据初始化] → [轨道计算] → [分层可见性分析] → [覆盖计算]
     ↓            ↓           ↓            ↓              ↓
[参数设置]   [地面设施统一]  [卫星位置]   [可见性矩阵]    [覆盖信息]
                                          ├─卫星间
                                          ├─卫星-地面用户  
                                          ├─卫星-云中心
                                          └─完整地面设施
```

## 3. 核心功能

### 3.1 轨道数据管理
**功能**：管理大规模卫星轨道数据的加载、解析和时间索引查询  
**输入**：CSV格式轨道数据文件，包含时间戳、经纬度、光照状态  
**输出**：按时间步组织的卫星状态字典  
**特点**：支持数千颗卫星、数千个时间步的高效查询

### 3.2 地面设施统一管理
**地面用户站管理**：
- 数据源：`updated_global_ground_stations.csv`
- 数量：420个地面用户终端
- 属性：ID、位置、区域类型、规模、用途

**云中心管理**：
- 数据源：`cloud_station.csv`
- 数量：5个云计算中心
- 属性：ID、位置、名称、类型标识

**统一接口**：`get_all_ground_facilities()` 提供所有地面设施的统一访问

### 3.3 分层可见性计算
**卫星间可见性**：
```python
# 基于3D距离和卫星间可见性阈值
distance_3d = sqrt(surface_distance² + height_difference²)
is_visible = distance_3d <= visibility_threshold
```

**卫星-地面用户可见性**：
```python
# 使用地面用户专用阈值
distance = sqrt(surface_distance² + satellite_altitude²)
is_visible = distance <= ground_visibility_threshold
```

**卫星-云中心可见性**：
```python
# 使用云中心专用阈值（可与地面用户不同）
distance = sqrt(surface_distance² + satellite_altitude²)
is_visible = distance <= cloud_visibility_threshold
```

### 3.4 可见性矩阵构建
- **卫星间矩阵**：N×N对称布尔矩阵，N为卫星数量
- **卫星-地面用户矩阵**：M×420布尔矩阵，M为卫星数量
- **卫星-云中心矩阵**：M×5布尔矩阵
- **完整地面设施矩阵**：M×425布尔矩阵（地面用户+云中心）

### 3.5 地面覆盖计算
```python
# 基于几何关系计算覆盖范围
coverage_radius = satellite_altitude * tan(coverage_angle)
coverage_area = π * coverage_radius²
```

## 4. API接口文档

### 4.1 OrbitalUpdater 类

#### 初始化
```python
OrbitalUpdater(data_file: str = "src/env/satellite_processed_data.csv", 
               config_file: str = "src/env/config.yaml")
```

**主要属性**：
- `self.ground_stations`: 地面用户站字典
- `self.cloud_stations`: 云中心字典
- `self.satellite_data`: 卫星轨道数据
- `self.visibility_threshold`: 卫星间可见性阈值
- `self.ground_visibility_threshold`: 地面用户可见性阈值
- `self.cloud_visibility_threshold`: 云中心可见性阈值

#### 主要方法

##### get_satellites_at_time()
```python
get_satellites_at_time(time_step: int) -> Dict[str, Satellite]
```
获取指定时间步的所有卫星状态。  
**参数**：`time_step` - 时间步索引(0到总时隙数-1)  
**返回**：卫星ID到Satellite对象的字典映射  
**异常**：时间步超出范围时返回空字典并打印警告

##### calculate_velocity()
```python
calculate_velocity(satellite_id: str, time_step: int) -> Optional[Tuple[float, float]]
```
计算指定卫星的运动速度。  
**返回**：(经度速度, 纬度速度) 单位：度/秒  
**特殊情况**：时间步为0时返回(0.0, 0.0)

##### 可见性计算方法组
```python
calculate_satellite_visibility(sat1: Satellite, sat2: Satellite) -> bool
calculate_satellite_ground_visibility(satellite: Satellite, ground_station: GroundStation) -> bool
calculate_satellite_cloud_visibility(satellite: Satellite, cloud_station: GroundStation) -> bool
```

**用途**：计算不同类型节点间的可见性  
**返回**：布尔值表示是否可见  
**区别**：使用不同的距离阈值参数

##### 可见性矩阵构建方法组
```python
build_inter_satellite_visibility_matrix(satellites: Dict[str, Satellite]) -> np.ndarray
build_satellite_ground_visibility_matrix(satellites: Dict[str, Satellite]) -> np.ndarray
build_satellite_cloud_visibility_matrix(satellites: Dict[str, Satellite]) -> np.ndarray
build_complete_ground_visibility_matrix(satellites: Dict[str, Satellite]) -> np.ndarray
```

**返回维度**：
- 卫星间：N×N（N为卫星数）
- 卫星-地面用户：N×420
- 卫星-云中心：N×5
- 完整地面设施：N×425

##### get_all_ground_facilities()
```python
get_all_ground_facilities() -> Dict[str, GroundStation]
```
获取所有地面设施（地面用户站+云中心）的统一字典。

##### 覆盖分析方法组
```python
get_ground_coverage(satellite: Satellite) -> Dict[str, float]
get_all_ground_coverage(satellites: Dict[str, Satellite]) -> Dict[str, Dict[str, float]]
```

**返回字典包含**：
- `center_longitude/latitude`: 覆盖中心坐标
- `coverage_radius_km`: 覆盖半径(km)
- `coverage_area_km2`: 覆盖面积(km²)
- `illuminated`: 光照状态

### 4.2 数据类

#### Satellite
```python
class Satellite:
    satellite_id: str          # 卫星唯一标识
    longitude: float           # 经度(度)
    latitude: float            # 纬度(度)
    illuminated: bool          # 光照状态
    timestamp: datetime        # 时间戳
    total_timeslots: int       # 总时隙数
    velocity: Tuple = None     # 速度向量(可选)
```

#### GroundStation
```python
class GroundStation:
    station_id: str            # 设施ID
    longitude: float           # 经度(度)
    latitude: float            # 纬度(度)
    name: str = None           # 设施名称(可选)
    type: str = None           # 设施类型(可选)
```

### 4.3 配置参数说明
主要配置位于 `config.yaml` 的 `system` 节：

```yaml
system:
  # 轨道参数
  leo_altitude_m: 1200000              # LEO卫星高度(m)
  earth_radius_m: 6371000              # 地球半径(m)
  
  # 可见性阈值（关键差异化参数）
  visibility_threshold_m: 5000000      # 卫星间可见性阈值(m)
  visibility_earth_m: 2500000          # 地面用户可见性阈值(m)
  cloud_visibility_threshold_m: 3000000 # 云中心可见性阈值(m)
  
  # 时间参数
  timeslot_duration_s: 10              # 时隙持续时间(s)
  total_timeslots: 1000                # 总时隙数
```

## 5. 使用指南

### 5.1 环境配置要求
```python
# 必需依赖
pandas >= 1.3.0
numpy >= 1.20.0
pyyaml >= 5.4.0

# 数据文件（必需）
satellite_processed_data.csv          # 卫星轨道数据
updated_global_ground_stations.csv    # 地面用户站数据
cloud_station.csv                     # 云中心数据
config.yaml                           # 配置文件
```

### 5.2 快速开始示例
```python
from orbital_updater import OrbitalUpdater

# 1. 创建轨道更新器
updater = OrbitalUpdater()

# 2. 查看地面设施信息
print(f"地面用户站: {len(updater.ground_stations)}")
print(f"云中心: {len(updater.cloud_stations)}")
print(f"总地面设施: {len(updater.get_all_ground_facilities())}")

# 3. 获取卫星状态
satellites = updater.get_satellites_at_time(0)
print(f"卫星数量: {len(satellites)}")

# 4. 构建不同类型可见性矩阵
inter_sat_matrix = updater.build_inter_satellite_visibility_matrix(satellites)
ground_matrix = updater.build_satellite_ground_visibility_matrix(satellites)
cloud_matrix = updater.build_satellite_cloud_visibility_matrix(satellites)
complete_matrix = updater.build_complete_ground_visibility_matrix(satellites)

print(f"卫星间连接: {np.sum(inter_sat_matrix)//2}")
print(f"卫星-地面用户连接: {np.sum(ground_matrix)}")
print(f"卫星-云中心连接: {np.sum(cloud_matrix)}")
print(f"总地面设施连接: {np.sum(complete_matrix)}")
```

### 5.3 常见使用场景

#### 场景1：地面设施差异化分析
```python
# 分析不同类型地面设施的连接特性
def analyze_ground_facility_connectivity(updater, time_step):
    satellites = updater.get_satellites_at_time(time_step)
    
    # 分别计算不同类型设施的可见性
    ground_matrix = updater.build_satellite_ground_visibility_matrix(satellites)
    cloud_matrix = updater.build_satellite_cloud_visibility_matrix(satellites)
    
    # 统计连接数
    ground_connections = np.sum(ground_matrix)
    cloud_connections = np.sum(cloud_matrix)
    
    # 计算平均连接数
    avg_ground_per_sat = ground_connections / len(satellites)
    avg_cloud_per_sat = cloud_connections / len(satellites)
    
    print(f"时间步 {time_step}:")
    print(f"  地面用户连接: {ground_connections} (平均 {avg_ground_per_sat:.2f}/卫星)")
    print(f"  云中心连接: {cloud_connections} (平均 {avg_cloud_per_sat:.2f}/卫星)")
    print(f"  连接密度比: 地面用户 {avg_ground_per_sat:.2f} : 云中心 {avg_cloud_per_sat:.2f}")

# 分析多个时间步
for t in range(10):
    analyze_ground_facility_connectivity(updater, t)
```

#### 场景2：动态可见性变化追踪
```python
# 追踪特定卫星的可见性变化
def track_satellite_visibility(updater, satellite_id, time_range):
    visibility_history = []
    
    for t in range(time_range):
        satellites = updater.get_satellites_at_time(t)
        if satellite_id not in satellites:
            continue
            
        target_sat = satellites[satellite_id]
        
        # 计算与不同设施的连接数
        ground_count = sum(
            1 for gs in updater.ground_stations.values()
            if updater.calculate_satellite_ground_visibility(target_sat, gs)
        )
        
        cloud_count = sum(
            1 for cs in updater.cloud_stations.values()
            if updater.calculate_satellite_cloud_visibility(target_sat, cs)
        )
        
        visibility_history.append({
            'timestep': t,
            'ground_connections': ground_count,
            'cloud_connections': cloud_count,
            'total_connections': ground_count + cloud_count
        })
    
    return visibility_history

# 示例使用
history = track_satellite_visibility(updater, "satellite_001", 50)
for record in history[:10]:  # 显示前10个时间步
    print(f"t={record['timestep']}: 地面{record['ground_connections']} + 云{record['cloud_connections']} = {record['total_connections']}")
```

#### 场景3：网络拓扑优化分析
```python
# 分析不同可见性阈值对网络连通性的影响
def analyze_threshold_impact(updater):
    satellites = updater.get_satellites_at_time(0)
    original_thresholds = {
        'ground': updater.ground_visibility_threshold,
        'cloud': updater.cloud_visibility_threshold
    }
    
    # 测试不同阈值设置
    test_scenarios = [
        {'ground': 2000, 'cloud': 2000},  # 保守设置
        {'ground': 2500, 'cloud': 3000},  # 默认设置
        {'ground': 3000, 'cloud': 3500},  # 激进设置
    ]
    
    for scenario in test_scenarios:
        # 临时修改阈值
        updater.ground_visibility_threshold = scenario['ground']
        updater.cloud_visibility_threshold = scenario['cloud']
        
        # 重新计算可见性
        ground_matrix = updater.build_satellite_ground_visibility_matrix(satellites)
        cloud_matrix = updater.build_satellite_cloud_visibility_matrix(satellites)
        
        ground_links = np.sum(ground_matrix)
        cloud_links = np.sum(cloud_matrix)
        
        print(f"阈值设置 地面:{scenario['ground']}km 云:{scenario['cloud']}km")
        print(f"  地面连接: {ground_links}, 云连接: {cloud_links}")
        print(f"  连接密度: {(ground_links + cloud_links)/len(satellites):.2f}")
    
    # 恢复原始阈值
    updater.ground_visibility_threshold = original_thresholds['ground']
    updater.cloud_visibility_threshold = original_thresholds['cloud']

analyze_threshold_impact(updater)
```

### 5.4 最佳实践建议

#### 性能优化
- **批量处理**：一次性处理多个时间步，避免重复初始化
- **矩阵缓存**：对重复使用的可见性矩阵进行缓存
- **内存管理**：大规模仿真时使用分批处理

#### 参数调优
```python
# 根据应用需求调整可见性阈值
def optimize_visibility_thresholds(updater, target_connectivity):
    """
    动态调整可见性阈值以达到目标连接密度
    """
    satellites = updater.get_satellites_at_time(0)
    
    for ground_threshold in range(2000, 4000, 500):
        for cloud_threshold in range(2500, 4500, 500):
            updater.ground_visibility_threshold = ground_threshold
            updater.cloud_visibility_threshold = cloud_threshold
            
            complete_matrix = updater.build_complete_ground_visibility_matrix(satellites)
            connectivity = np.sum(complete_matrix) / len(satellites)
            
            if abs(connectivity - target_connectivity) < 0.5:
                print(f"最优阈值: 地面{ground_threshold}km, 云{cloud_threshold}km")
                print(f"实际连接密度: {connectivity:.2f}")
                return ground_threshold, cloud_threshold
    
    return None, None
```

#### 错误处理
```python
def safe_orbital_operations(updater, time_step):
    """安全的轨道操作示例"""
    try:
        satellites = updater.get_satellites_at_time(time_step)
        if not satellites:
            print(f"警告: 时间步 {time_step} 无卫星数据")
            return None
        
        # 验证地面设施数据
        if not updater.ground_stations:
            print("错误: 地面用户站数据未加载")
            return None
        
        if not updater.cloud_stations:
            print("警告: 云中心数据未加载，部分功能受限")
        
        # 执行计算
        complete_matrix = updater.build_complete_ground_visibility_matrix(satellites)
        return complete_matrix
        
    except Exception as e:
        print(f"轨道操作错误: {e}")
        return None
```

## 6. 集成说明

### 6.1 模块依赖关系
```
Orbital Updater Module 依赖：
├── 数据文件 (必需)
│   ├── satellite_processed_data.csv     # 卫星轨道数据
│   ├── updated_global_ground_stations.csv # 地面用户站数据  
│   ├── cloud_station.csv                # 云中心数据
│   └── config.yaml                      # 配置文件
├── Python库 (必需)
│   ├── pandas >= 1.3.0                 # 数据处理
│   ├── numpy >= 1.20.0                 # 矩阵运算
│   └── pyyaml >= 5.4.0                 # 配置解析
└── 系统要求
    ├── Python 3.7+                     # 运行环境
    └── 4GB+ RAM (推荐)                  # 大数据集处理
```

### 6.2 与其他模块的协作

#### 与Communication模块
```python
# orbital_updater为communication提供统一的地面设施接口
from orbital_updater import OrbitalUpdater
from communication import CommunicationManager

updater = OrbitalUpdater()
comm_manager = CommunicationManager()

# 设置轨道更新器
comm_manager.set_orbital_updater(updater)

# communication模块现在可以访问：
# - updater.ground_stations (地面用户站)
# - updater.cloud_stations (云中心)
# - updater.calculate_satellite_cloud_visibility() (云中心可见性)
```

### 6.3 扩展和定制方法

#### 添加新的地面设施类型
```python
class ExtendedOrbitalUpdater(OrbitalUpdater):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.edge_stations = self._create_edge_stations()
        self.edge_visibility_threshold = 2800  # 边缘计算站专用阈值
    
    def _create_edge_stations(self):
        """创建边缘计算站数据"""
        # 实现边缘计算站数据加载逻辑
        edge_stations = {}
        # ... 加载逻辑 ...
        return edge_stations
    
    def calculate_satellite_edge_visibility(self, satellite, edge_station):
        """计算卫星与边缘计算站的可见性"""
        distance = self._calculate_distance(
            satellite.latitude, satellite.longitude,
            edge_station.latitude, edge_station.longitude,
            self.satellite_altitude, 0
        )
        return distance <= self.edge_visibility_threshold
    
    def build_satellite_edge_visibility_matrix(self, satellites):
        """构建卫星-边缘计算站可见性矩阵"""
        # 实现边缘计算站可见性矩阵构建
        pass
```

#### 自定义可见性算法
```python
class AdvancedOrbitalUpdater(OrbitalUpdater):
    def calculate_satellite_ground_visibility(self, satellite, ground_station):
        """增强的地面可见性计算，考虑仰角限制"""
        # 基础距离检查
        basic_visibility = super().calculate_satellite_ground_visibility(satellite, ground_station)
        
        if not basic_visibility:
            return False
        
        # 添加仰角限制检查
        elevation_angle = self._calculate_elevation_angle(satellite, ground_station)
        min_elevation = 10.0  # 最小仰角10度
        
        return elevation_angle >= min_elevation
    
    def _calculate_elevation_angle(self, satellite, ground_station):
        """计算仰角"""
        # 实现仰角计算逻辑
        distance = self._calculate_distance(
            satellite.latitude, satellite.longitude,
            ground_station.latitude, ground_station.longitude,
            self.satellite_altitude, 0
        )
        
        # 简化仰角计算
        elevation = math.degrees(math.atan(self.satellite_altitude / distance))
        return elevation
```

---

## 技术支持

### 常见问题
1. **数据文件格式错误**：确认CSV文件列顺序和数据类型
2. **内存不足**：使用分批处理或减少时间步范围
3. **可见性结果异常**：检查阈值参数设置和单位转换
4. **矩阵维度不匹配**：验证卫星和地面设施数量一致性

### 调试建议
- 使用小数据集验证功能正确性
- 检查配置文件参数的合理性
- 监控内存使用情况
- 验证输入数据的完整性

---

*最后更新：2025年1月*