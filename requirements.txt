# ================================================================
# SPACE-DMPO 项目依赖配置文件
# 
# 项目: 分布式多智能体卫星网络计算任务卸载系统
# 版本: MVP-1.0
# 更新日期: 2025-01
# 
# 安装方法:
#   pip install -r requirements.txt
# 
# 推荐使用虚拟环境:
#   python -m venv venv
#   source venv/bin/activate  # Linux/Mac
#   venv\Scripts\activate     # Windows
# ================================================================

# SPACE-DMPO Project Dependencies
# Core Requirements for MADDPG Algorithm MVP

# Deep Learning Framework
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Reinforcement Learning
gym>=0.21.0
gymnasium>=0.26.0
stable-baselines3>=1.6.0

# Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Satellite Computing
pyorbital>=1.7.0
pyephem>=4.1.0

# ML Experiment Tracking
wandb>=0.13.0
tensorboard>=2.8.0

# Configuration Management
pyyaml>=6.0
hydra-core>=1.2.0

# System Tools
multiprocessing-logging>=0.3.0
tqdm>=4.62.0
rich>=12.0.0

# Testing
pytest>=7.0.0
pytest-cov>=3.0.0

# ================================================================
# 额外说明:
# 
# 1. 如果您使用CUDA GPU，请确保安装与您的CUDA版本兼容的PyTorch
#    访问 https://pytorch.org/ 获取正确的安装命令
# 
# 2. 如果安装过程中遇到网络问题，可以使用国内镜像源:
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
# 
# 3. 对于Windows用户，某些包可能需要Microsoft Visual C++ Build Tools
# 
# 4. 建议的最小Python版本: 3.8+
# ================================================================ 