import pandas as pd
import numpy as np
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import math
import os
import logging


class Satellite:
    """卫星类，存储卫星的基本信息"""
    def __init__(self, satellite_id: str, longitude: float, latitude: float, 
                 illuminated: bool, timestamp: datetime,total_timeslots: int):
        self.satellite_id = satellite_id
        self.longitude = longitude
        self.latitude = latitude
        self.illuminated = illuminated
        self.timestamp = timestamp
        self.total_timeslots = total_timeslots
        self.velocity = None  # 速度向量 (经度速度, 纬度速度)
        
    def __repr__(self):
        return f"Satellite({self.satellite_id}, lon={self.longitude:.3f}, lat={self.latitude:.3f}, ill={self.illuminated})"


class GroundStation:
    """地面站类，存储地面站的基本信息"""
    def __init__(self, station_id: str, longitude: float, latitude: float, 
                 name: str = None, type: str = None):
        self.station_id = station_id
        self.longitude = longitude
        self.latitude = latitude
        self.name = name
        self.type = type
        
    def __repr__(self):
        return f"GroundStation({self.station_id}, lon={self.longitude:.3f}, lat={self.latitude:.3f})"


class OrbitalUpdater:
    """轨道更新模块 - 性能优化版本"""
    
    def __init__(self, data_file: str = "src/env/satellite_processed_data1.csv", 
                 config_file: str = "src/env/config.yaml"):
        """
        初始化轨道更新器
        
        Args:
            data_file: 卫星数据文件路径
            config_file: 配置文件路径
        """
        self.data_file = data_file
        self.config_file = config_file
        self.config = self._load_config()
        self.satellite_data = self._load_satellite_data()
        self.ground_stations = self._create_ground_stations()
        self.cloud_stations = self._create_cloud_stations()
        
        # 性能优化：添加缓存机制
        self._visibility_cache = {}
        self._distance_cache = {}
        self._satellites_cache = {}
        
        # 从配置文件中获取参数
        self.earth_radius = self.config['system']['earth_radius_m'] / 1000.0  # 转换为km
        self.satellite_altitude = self.config['system']['leo_altitude_m'] / 1000.0  # 转换为km
        self.visibility_threshold = self.config['system']['visibility_threshold_m'] / 1000.0  # 转换为km
        self.ground_visibility_threshold = self.config['system']['visibility_earth_m'] / 1000.0  # 转换为km
        # 云中心可见性阈值（可以与地面用户不同）
        self.cloud_visibility_threshold = self.config['system'].get('cloud_visibility_threshold_m', self.config['system']['visibility_earth_m']) / 1000.0  # 转换为km
        self.timeslot_duration = self.config['system']['timeslot_duration_s']
        self.num_leo_satellites = self.config['system']['num_leo_satellites']
        self.num_users = self.config['system']['num_users']
        
        # 地面覆盖角度（使用默认值）
        self.coverage_angle = 60.0  # 度
        
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.critical(f"致命错误：加载配置文件失败: {self.config_file} - {e}")
            raise
            
    def _load_satellite_data(self) -> pd.DataFrame:
        """加载卫星数据并优化索引"""
        try:
            # 跳过标题行，正确的列顺序是：卫星id,时隙,lat,lon,是否有光照
            df = pd.read_csv(self.data_file, skiprows=1, 
                           names=['satellite_id', 'timestamp', 'latitude', 'longitude', 'illuminated'])
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 优化查询性能：创建基于时间戳的索引
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
        except Exception as e:
            logging.error(f"加载卫星数据失败: {self.data_file} - {e}")
            return pd.DataFrame()
    
    def _create_ground_stations(self) -> Dict[str, GroundStation]:
        """创建地面站数据（加载420个地面用户终端）"""
        ground_stations = {}
        ground_station_file = "src/env/updated_global_ground_stations.csv"
        
        if os.path.exists(ground_station_file):
            try:
                df = pd.read_csv(ground_station_file)
                for _, row in df.iterrows():
                    station = GroundStation(
                        station_id=str(row['ID']),
                        longitude=row['Longitude'],
                        latitude=row['Latitude'],
                        name=f"Station_{row['ID']}_{row['RegionType']}_{row['Size']}",
                        type=row['PurposeType']
                    )
                    ground_stations[station.station_id] = station
            except Exception as e:
                print(f"加载地面站数据失败: {e}")
        
        return ground_stations
    
    def _create_cloud_stations(self) -> Dict[str, GroundStation]:
        """创建云中心数据"""
        cloud_stations = {}
        cloud_station_file = "src/env/cloud_station.csv"
        
        if os.path.exists(cloud_station_file):
            try:
                df = pd.read_csv(cloud_station_file)
                for _, row in df.iterrows():
                    station_id = f"cloud_{int(row['ID'])}"
                    cloud_station = GroundStation(
                        station_id=station_id,
                        longitude=row['Longitude'],
                        latitude=row['Latitude'],
                        name=f"Cloud Center {int(row['ID'])}",
                        type="cloud_center"
                    )
                    cloud_stations[station_id] = cloud_station
                print(f"成功加载 {len(cloud_stations)} 个云中心")
            except Exception as e:
                print(f"加载云中心数据失败: {e}")
        else:
            print(f"云中心数据文件不存在: {cloud_station_file}")
        
        return cloud_stations
    
    def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]:
        """
        获取指定时间步的所有卫星状态 - 缓存和索引优化版本
        
        Args:
            time_step: 时间步 (0-999)
            
        Returns:
            Dict[str, Satellite]: 卫星ID到卫星对象的映射
        """
        # 性能优化：检查缓存
        if time_step in self._satellites_cache:
            return self._satellites_cache[time_step]
            
        if self.satellite_data.empty:
            return {}
            
        # 获取所有唯一的时间戳
        unique_times = sorted(self.satellite_data.index.unique())
        
        if time_step >= len(unique_times):
            logging.warning(f"时间步 {time_step} 超出范围 (0-{len(unique_times)-1})")
            return {}
            
        target_time = unique_times[time_step]
        
        # 使用索引进行高效切片查询
        time_data = self.satellite_data.loc[target_time]
        
        satellites = {}
        # 如果只有一个卫星，time_data是Series，需要转换为DataFrame
        if isinstance(time_data, pd.Series):
            time_data = time_data.to_frame().T
            
        for _, row in time_data.iterrows():
            satellite = Satellite(
                satellite_id=row['satellite_id'],
                longitude=row['longitude'],
                latitude=row['latitude'],
                illuminated=row['illuminated'],
                timestamp=target_time,
                total_timeslots=self.get_total_timeslots()  
            )
            satellites[row['satellite_id']] = satellite
        
        # 性能优化：缓存结果
        self._satellites_cache[time_step] = satellites
        return satellites
    
    def calculate_velocity(self, satellite_id: str, time_step: int) -> Optional[Tuple[float, float]]:
        """
        计算卫星速度 (经度速度, 纬度速度)
        
        Args:
            satellite_id: 卫星ID
            time_step: 当前时间步
            
        Returns:
            Tuple[float, float]: (经度速度, 纬度速度) 单位: 度/秒
        """
        # 边界条件处理：使用向前差分计算初始速度
        if time_step == 0:
            current_sats = self.get_satellites_at_time(time_step)
            next_sats = self.get_satellites_at_time(time_step + 1)
            
            if satellite_id not in current_sats or satellite_id not in next_sats:
                return None
                
            current_sat = current_sats[satellite_id]
            next_sat = next_sats[satellite_id]
            
            # 计算时间差 (秒)
            time_diff = (next_sat.timestamp - current_sat.timestamp).total_seconds()
            
            if time_diff == 0:
                return (0.0, 0.0)
                
            # 使用向前差分计算速度
            lon_velocity = (next_sat.longitude - current_sat.longitude) / time_diff
            lat_velocity = (next_sat.latitude - current_sat.latitude) / time_diff
            
            return (lon_velocity, lat_velocity)
        
        # 常规情况：使用后向差分
        current_sats = self.get_satellites_at_time(time_step)
        prev_sats = self.get_satellites_at_time(time_step - 1)
        
        if satellite_id not in current_sats or satellite_id not in prev_sats:
            return None
            
        current_sat = current_sats[satellite_id]
        prev_sat = prev_sats[satellite_id]
        
        # 计算时间差 (秒)
        time_diff = (current_sat.timestamp - prev_sat.timestamp).total_seconds()
        
        if time_diff == 0:
            return (0.0, 0.0)
            
        # 计算速度
        lon_velocity = (current_sat.longitude - prev_sat.longitude) / time_diff
        lat_velocity = (current_sat.latitude - prev_sat.latitude) / time_diff
        
        return (lon_velocity, lat_velocity)
    
    def update_satellite_positions(self, satellites: Dict[str, Satellite], time_step: int) -> Dict[str, Satellite]:
        """
        更新所有卫星位置
        
        Args:
            satellites: 卫星字典
            time_step: 时间步
            
        Returns:
            Dict[str, Satellite]: 更新后的卫星字典
        """
        updated_satellites = {}
        
        for satellite_id, satellite in satellites.items():
            # 计算速度
            velocity = self.calculate_velocity(satellite_id, time_step)
            if velocity:
                satellite.velocity = velocity
            
            updated_satellites[satellite_id] = satellite
            
        return updated_satellites
    
    def _lat_lon_alt_to_ecef(self, lat: float, lon: float, alt: float = 0.0) -> Tuple[float, float, float]:
        """
        将经纬高坐标转换为地心固连坐标系(ECEF)
        
        Args:
            lat: 纬度(度)
            lon: 经度(度)  
            alt: 高度(km)
            
        Returns:
            Tuple[float, float, float]: ECEF坐标 (X, Y, Z) in km
        """
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        
        # 地球半径 + 高度
        r = self.earth_radius + alt
        
        x = r * math.cos(lat_rad) * math.cos(lon_rad)
        y = r * math.cos(lat_rad) * math.sin(lon_rad)
        z = r * math.sin(lat_rad)
        
        return x, y, z
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float, 
                           alt1: float = None, alt2: float = None) -> float:
        """
        计算两点之间的真实3D距离
        
        Args:
            lat1, lon1: 第一个点的纬度、经度
            lat2, lon2: 第二个点的纬度、经度
            alt1, alt2: 高度（可选，单位km）
            
        Returns:
            float: 距离 (km)
        """
        # 设置默认高度
        if alt1 is None:
            alt1 = 0.0
        if alt2 is None:
            alt2 = 0.0
            
        # 转换为ECEF坐标
        x1, y1, z1 = self._lat_lon_alt_to_ecef(lat1, lon1, alt1)
        x2, y2, z2 = self._lat_lon_alt_to_ecef(lat2, lon2, alt2)
        
        # 计算欧几里得距离
        distance = math.sqrt((x2 - x1)**2 + (y2 - y1)**2 + (z2 - z1)**2)
        
        return distance
    
    def calculate_satellite_visibility(self, sat1: Satellite, sat2: Satellite) -> bool:
        """
        计算两颗卫星间的可见性
        
        Args:
            sat1, sat2: 两颗卫星
            
        Returns:
            bool: 是否可见
        """
        if sat1.satellite_id == sat2.satellite_id:
            return False
            
        # 计算距离
        distance = self._calculate_distance(
            sat1.latitude, sat1.longitude, 
            sat2.latitude, sat2.longitude,
            self.satellite_altitude, self.satellite_altitude
        )
        
        # 基于距离判断可见性
        is_visible = distance <= self.visibility_threshold
        
        # 可以根据需要添加其他可见性条件，比如光照条件
        # both_illuminated = sat1.illuminated and sat2.illuminated
        
        return is_visible
    
    def calculate_satellite_ground_visibility(self, satellite: Satellite, ground_station: GroundStation) -> bool:
        """
        计算卫星与地面站间的可见性
        
        Args:
            satellite: 卫星对象
            ground_station: 地面站对象
            
        Returns:
            bool: 是否可见
        """
        # 计算距离
        distance = self._calculate_distance(
            satellite.latitude, satellite.longitude,
            ground_station.latitude, ground_station.longitude,
            self.satellite_altitude, 0
        )
        
        # 基于距离判断可见性
        is_visible = distance <= self.ground_visibility_threshold
        
        # 可以根据需要添加其他可见性条件，比如仰角限制
        return is_visible
    
    def calculate_satellite_cloud_visibility(self, satellite: Satellite, cloud_station: GroundStation) -> bool:
        """
        计算卫星与云中心间的可见性
        
        Args:
            satellite: 卫星对象
            cloud_station: 云中心对象
            
        Returns:
            bool: 是否可见
        """
        # 计算距离
        distance = self._calculate_distance(
            satellite.latitude, satellite.longitude,
            cloud_station.latitude, cloud_station.longitude,
            self.satellite_altitude, 0
        )
        
        # 基于距离判断可见性（使用云中心专用阈值）
        is_visible = distance <= self.cloud_visibility_threshold
        
        # 可以根据需要添加其他可见性条件
        return is_visible
    
    def build_inter_satellite_visibility_matrix(self, satellites: Dict[str, Satellite], time_step: int = None) -> np.ndarray:
        """
        构建卫星间可见性矩阵 - 缓存优化版本
        
        Args:
            satellites: 卫星字典
            time_step: 时间步（用于缓存）
            
        Returns:
            np.ndarray: 可见性矩阵 (N x N)
        """
        # 性能优化：检查缓存
        cache_key = f"inter_sat_vis_{time_step}" if time_step is not None else None
        if cache_key and cache_key in self._visibility_cache:
            return self._visibility_cache[cache_key]
            
        satellite_list = list(satellites.values())
        n = len(satellite_list)
        visibility_matrix = np.zeros((n, n), dtype=bool)
        
        for i in range(n):
            for j in range(i+1, n):
                is_visible = self.calculate_satellite_visibility(satellite_list[i], satellite_list[j])
                visibility_matrix[i, j] = is_visible
                visibility_matrix[j, i] = is_visible  # 对称矩阵
        
        # 性能优化：缓存结果
        if cache_key:
            self._visibility_cache[cache_key] = visibility_matrix
                
        return visibility_matrix
    
    def build_satellite_ground_visibility_matrix(self, satellites: Dict[str, Satellite], time_step: int = None) -> np.ndarray:
        """
        构建卫星-地面站可见性矩阵 - 缓存优化版本
        
        Args:
            satellites: 卫星字典
            time_step: 时间步（用于缓存）
            
        Returns:
            np.ndarray: 可见性矩阵 (N_satellites x N_ground_stations)
        """
        # 性能优化：检查缓存
        cache_key = f"sat_ground_vis_{time_step}" if time_step is not None else None
        if cache_key and cache_key in self._visibility_cache:
            return self._visibility_cache[cache_key]
            
        satellite_list = list(satellites.values())
        ground_station_list = list(self.ground_stations.values())
        
        n_satellites = len(satellite_list)
        n_ground_stations = len(ground_station_list)
        
        visibility_matrix = np.zeros((n_satellites, n_ground_stations), dtype=bool)
        
        for i, satellite in enumerate(satellite_list):
            for j, ground_station in enumerate(ground_station_list):
                is_visible = self.calculate_satellite_ground_visibility(satellite, ground_station)
                visibility_matrix[i, j] = is_visible
        
        # 性能优化：缓存结果
        if cache_key:
            self._visibility_cache[cache_key] = visibility_matrix
            
        return visibility_matrix
    
    def build_satellite_cloud_visibility_matrix(self, satellites: Dict[str, Satellite], time_step: int = None) -> np.ndarray:
        """
        构建卫星-云中心可见性矩阵 - 缓存优化版本
        
        Args:
            satellites: 卫星字典
            time_step: 时间步（用于缓存）
            
        Returns:
            np.ndarray: 可见性矩阵 (N_satellites x N_cloud_stations)
        """
        # 性能优化：检查缓存
        cache_key = f"sat_cloud_vis_{time_step}" if time_step is not None else None
        if cache_key and cache_key in self._visibility_cache:
            return self._visibility_cache[cache_key]
            
        satellite_list = list(satellites.values())
        cloud_station_list = list(self.cloud_stations.values())
        
        n_satellites = len(satellite_list)
        n_cloud_stations = len(cloud_station_list)
        
        visibility_matrix = np.zeros((n_satellites, n_cloud_stations), dtype=bool)
        
        for i, satellite in enumerate(satellite_list):
            for j, cloud_station in enumerate(cloud_station_list):
                is_visible = self.calculate_satellite_cloud_visibility(satellite, cloud_station)
                visibility_matrix[i, j] = is_visible
        
        # 性能优化：缓存结果
        if cache_key:
            self._visibility_cache[cache_key] = visibility_matrix
                
        return visibility_matrix
    
    def build_complete_ground_visibility_matrix(self, satellites: Dict[str, Satellite], time_step: int = None) -> np.ndarray:
        """
        构建完整的卫星-地面设施可见性矩阵（包含地面用户和云中心）
        通过合并已有矩阵来避免重复计算
        
        Args:
            satellites: 卫星字典
            time_step: 时间步（用于缓存）
            
        Returns:
            np.ndarray: 可见性矩阵 (N_satellites x (N_ground_stations + N_cloud_stations))
        """
        ground_matrix = self.build_satellite_ground_visibility_matrix(satellites, time_step)
        cloud_matrix = self.build_satellite_cloud_visibility_matrix(satellites, time_step)
        
        return np.hstack((ground_matrix, cloud_matrix))
    
    def get_all_ground_facilities(self) -> Dict[str, GroundStation]:
        """
        获取所有地面设施（地面用户 + 云中心）
        
        Returns:
            Dict[str, GroundStation]: 所有地面设施的字典
        """
        all_facilities = {}
        all_facilities.update(self.ground_stations)
        all_facilities.update(self.cloud_stations)
        return all_facilities
    
    def get_ground_coverage(self, satellite: Satellite) -> Dict[str, float]:
        """
        计算卫星对地面的覆盖范围
        
        Args:
            satellite: 卫星对象
            
        Returns:
            Dict[str, float]: 覆盖信息，包含覆盖半径和覆盖面积
        """
        # 检查覆盖角度范围，防止数值溢出
        if self.coverage_angle >= 90.0:
            raise ValueError(f"覆盖角度不能大于等于90度，当前值: {self.coverage_angle}")
        if self.coverage_angle <= 0.0:
            raise ValueError(f"覆盖角度必须为正数，当前值: {self.coverage_angle}")
            
        # 计算地面覆盖半径
        # 使用几何关系: tan(coverage_angle) = coverage_radius / satellite_altitude
        coverage_radius_km = self.satellite_altitude * math.tan(math.radians(self.coverage_angle))
        
        # 计算覆盖面积
        coverage_area_km2 = math.pi * coverage_radius_km**2
        
        # 转换为地面角度范围 (度)
        coverage_radius_deg = coverage_radius_km / (self.earth_radius * math.pi / 180)
        
        return {
            'center_longitude': satellite.longitude,
            'center_latitude': satellite.latitude,
            'coverage_radius_km': coverage_radius_km,
            'coverage_radius_deg': coverage_radius_deg,
            'coverage_area_km2': coverage_area_km2,
            'illuminated': satellite.illuminated
        }
    
    def get_all_ground_coverage(self, satellites: Dict[str, Satellite]) -> Dict[str, Dict[str, float]]:
        """
        获取所有卫星的地面覆盖信息
        
        Args:
            satellites: 卫星字典
            
        Returns:
            Dict[str, Dict[str, float]]: 每颗卫星的覆盖信息
        """
        coverage_info = {}
        for satellite_id, satellite in satellites.items():
            coverage_info[satellite_id] = self.get_ground_coverage(satellite)
        return coverage_info
    
    def get_total_timeslots(self) -> int:
        """
        获取总时隙数
        
        Returns:
            int: 总时隙数
        """
        return self.config['system']['total_timeslots']
    
def main():
    """主函数，演示轨道更新模块的使用"""
    print("=== 轨道更新模块测试 ===")
    
    # 创建轨道更新器
    updater = OrbitalUpdater()
    
    print(f"地面用户站数量: {len(updater.ground_stations)}")
    print(f"云中心数量: {len(updater.cloud_stations)}")
    print(f"总地面设施数量: {len(updater.get_all_ground_facilities())}")
    
    # 测试前几个时间步
    for time_step in range(min(3, updater.get_total_timeslots())):
        print(f"\n--- 时间步 {time_step} ---")
        satellites = updater.get_satellites_at_time(time_step)
        
        if satellites:
            print(f"卫星数量: {len(satellites)}")
            
            # 构建各种可见性矩阵
            inter_sat_matrix = updater.build_inter_satellite_visibility_matrix(satellites, time_step)
            ground_matrix = updater.build_satellite_ground_visibility_matrix(satellites, time_step)
            cloud_matrix = updater.build_satellite_cloud_visibility_matrix(satellites, time_step)
            complete_matrix = updater.build_complete_ground_visibility_matrix(satellites, time_step)
            
            # 统计连接数
            inter_sat_links = np.sum(inter_sat_matrix) // 2
            ground_links = np.sum(ground_matrix)
            cloud_links = np.sum(cloud_matrix)
            total_ground_links = np.sum(complete_matrix)
            
            print(f"卫星间连接数: {inter_sat_links}")
            print(f"卫星-地面用户连接数: {ground_links}")
            print(f"卫星-云中心连接数: {cloud_links}")
            print(f"卫星-地面设施总连接数: {total_ground_links}")
            
            # 验证完整矩阵
            assert total_ground_links == ground_links + cloud_links, "完整矩阵连接数计算错误"
            
            # 覆盖信息
            coverage_info = updater.get_all_ground_coverage(satellites)
            total_coverage = sum(info['coverage_area_km2'] for info in coverage_info.values())
            print(f"总覆盖面积: {total_coverage:.2f} km²")
            
        else:
            print("未找到卫星数据")
    
    print("\n=== 轨道更新模块测试完成 ===")


if __name__ == "__main__":
    main()


