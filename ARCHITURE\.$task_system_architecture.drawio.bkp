<mxfile host="app.diagrams.net" modified="2025-01-27T15:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" etag="test" version="24.0.0" type="device">
  <diagram name="任务模块架构图" id="task-system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- 程序入口 -->
        <mxCell id="2" value="main()&#xa;程序入口" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#c5504b;fontColor=#ffffff;strokeColor=#432D57;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="770" y="50" width="100" height="60" as="geometry"/>
        </mxCell>
        
        <!-- 数据输入层 -->
        <mxCell id="3" value="数据输入层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="200" y="1200" width="1200" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="4" value="task_generation_results.json&#xa;预生成任务数据&#xa;包含1000时隙任务" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="3">
          <mxGeometry x="50" y="40" width="220" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="5" value="config.yaml&#xa;系统配置文件&#xa;排队/计算/通信参数" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#ffc000;fontColor=#000000;strokeColor=#d6b656;" vertex="1" parent="3">
          <mxGeometry x="320" y="40" width="180" height="50" as="geometry"/>
        </mxCell>
        
        <!-- 管理控制层 -->
        <mxCell id="6" value="管理控制层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6f2ff;strokeColor=#6c8ebf;fontColor=#1f4e79;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="350" y="160" width="900" height="140" as="geometry"/>
        </mxCell>
        
        <mxCell id="7" value="TaskLoader&#xa;任务数据加载器&#xa;- JSON数据解析&#xa;- 时隙任务提取&#xa;- Task实例创建" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" vertex="1" parent="6">
          <mxGeometry x="150" y="50" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <mxCell id="8" value="TaskManager&#xa;任务管理器&#xa;- 活跃任务管理&#xa;- 状态跟踪&#xa;- 调度器接口&#xa;- 系统统计" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1f4e79;fontColor=#ffffff;strokeColor=#1f4e79;fontSize=12;fontStyle=1" vertex="1" parent="6">
          <mxGeometry x="450" y="50" width="200" height="80" as="geometry"/>
        </mxCell>
        
        <!-- 数据模型层 -->
        <mxCell id="9" value="数据模型层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#82b366;fontColor=#2d7600;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="250" y="340" width="1100" height="200" as="geometry"/>
        </mxCell>
        
        <mxCell id="10" value="TaskState&#xa;任务状态枚举&#xa;- GENERATED&#xa;- QUEUED&#xa;- PROCESSING&#xa;- TRANSFERRING&#xa;- RETRYING&#xa;- RETURNING&#xa;- COMPLETED&#xa;- FAILED" style="rhombus;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="9">
          <mxGeometry x="50" y="50" width="120" height="140" as="geometry"/>
        </mxCell>
        
        <mxCell id="11" value="Task&#xa;任务实体核心类&#xa;- task_id: str&#xa;- source_location_id: int&#xa;- data_size_mb: float&#xa;- complexity_cycles_per_bit: int&#xa;- deadline_timestamp: float&#xa;- priority: int&#xa;- state: TaskState&#xa;- processing_records: List&#xa;- transfer_records: List&#xa;- retry_record: RetryRecord" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="9">
          <mxGeometry x="220" y="40" width="200" height="150" as="geometry"/>
        </mxCell>
        
        <mxCell id="12" value="ProcessingRecord&#xa;@dataclass&#xa;处理记录&#xa;- satellite_id: str&#xa;- start_time: float&#xa;- end_time: float&#xa;- cpu_cycles_processed: int&#xa;- energy_consumed: float&#xa;- completion_ratio: float&#xa;- is_partial: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="9">
          <mxGeometry x="460" y="40" width="180" height="150" as="geometry"/>
        </mxCell>
        
        <mxCell id="13" value="TransferRecord&#xa;@dataclass&#xa;传输记录&#xa;- from_satellite_id: Optional[str]&#xa;- to_satellite_id: Optional[str]&#xa;- transfer_time: float&#xa;- transfer_energy: float&#xa;- success: bool" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="9">
          <mxGeometry x="680" y="40" width="180" height="150" as="geometry"/>
        </mxCell>
        
        <mxCell id="14" value="RetryRecord&#xa;@dataclass&#xa;重试记录&#xa;- retry_count: int = 0&#xa;- last_retry_time: float = 0.0&#xa;- retry_reason: str = &quot;&quot;&#xa;- max_retries: int = 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#70ad47;fontColor=#ffffff;strokeColor=#70ad47;fontSize=10" vertex="1" parent="9">
          <mxGeometry x="900" y="40" width="150" height="150" as="geometry"/>
        </mxCell>
        
        <!-- 核心功能层 -->
        <mxCell id="15" value="核心功能层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="127" y="580" width="1400" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="16" value="load_tasks_for_timeslot()&#xa;按时隙加载任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="50" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="17" value="update_state()&#xa;任务状态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="250" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="18" value="calculate_dynamic_priority()&#xa;动态优先级计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="450" y="40" width="180" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="19" value="start_processing()&#xa;开始任务处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="670" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="20" value="transfer_to_satellite()&#xa;卫星间传输" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="870" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="21" value="attempt_retry()&#xa;重试尝试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="1070" y="40" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="22" value="complete_processing()&#xa;完成处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="15">
          <mxGeometry x="1250" y="40" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <!-- 业务服务层 -->
        <mxCell id="23" value="业务服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#d79b00;fontColor=#8c4a00;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="180" y="720" width="1200" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="24" value="get_high_priority_tasks()&#xa;高优先级任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="50" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="25" value="get_tasks_by_satellite()&#xa;按卫星分组查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="250" y="40" width="160" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="26" value="export_tasks_for_scheduling()&#xa;调度器接口导出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="450" y="40" width="180" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="27" value="get_overdue_tasks()&#xa;超期任务查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="670" y="40" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="28" value="get_tasks_by_state()&#xa;按状态查询任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="850" y="40" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <mxCell id="29" value="get_system_statistics()&#xa;系统统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d9730d;fontColor=#ffffff;strokeColor=#d9730d;fontSize=10" vertex="1" parent="23">
          <mxGeometry x="1030" y="40" width="140" height="50" as="geometry"/>
        </mxCell>
        
        <!-- 工具函数层 -->
        <mxCell id="30" value="工具函数层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f8f8;strokeColor=#666666;fontColor=#333333;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="250" y="860" width="1000" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="31" value="_load_config()&#xa;配置文件加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="30">
          <mxGeometry x="80" y="40" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="32" value="to_dict()&#xa;字典格式转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="30">
          <mxGeometry x="280" y="40" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="33" value="get_statistics()&#xa;任务统计信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="30">
          <mxGeometry x="480" y="40" width="140" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="34" value="get_resource_requirements()&#xa;资源需求查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7f7f7f;fontColor=#ffffff;strokeColor=#7f7f7f;fontSize=10" vertex="1" parent="30">
          <mxGeometry x="680" y="40" width="160" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 系统配置层 -->
        <mxCell id="35" value="系统配置层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f0e6ff;strokeColor=#9673a6;fontColor=#432d57;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="-100" width="900" height="100" as="geometry"/>
        </mxCell>
        
        <mxCell id="36" value="w_priority&#xa;优先级权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="50" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="37" value="w_urgency&#xa;紧迫性权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="180" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="38" value="w_cost&#xa;成本权重" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="310" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="39" value="f_leo_hz&#xa;LEO计算频率" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="440" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="40" value="max_retries&#xa;最大重试次数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="570" y="40" width="100" height="40" as="geometry"/>
        </mxCell>
        
        <mxCell id="41" value="epsilon_urgency&#xa;紧迫性防零参数" style="ellipse;whiteSpace=wrap;html=1;fillColor=#7030a0;fontColor=#ffffff;strokeColor=#7030a0;fontSize=10" vertex="1" parent="35">
          <mxGeometry x="700" y="40" width="120" height="40" as="geometry"/>
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="42" value="创建实例" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="2" target="8">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="43" value="数据读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="4" target="7">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="44" value="配置读取" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#404040;dashed=1;fontSize=10" edge="1" parent="1" source="5" target="31">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="45" value="创建任务对象" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="7" target="11">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="46" value="管理任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="8" target="11">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="47" value="记录处理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="11" target="12">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="48" value="记录传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="11" target="13">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="49" value="记录重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#0066cc;fontSize=10" edge="1" parent="1" source="11" target="14">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- 主数据流 -->
        <mxCell id="50" value="主要数据流&#xa;(任务生命周期)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#00aa00;fontSize=12;fontStyle=1" edge="1" parent="1" source="7" target="16">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="350"/>
              <mxPoint x="207" y="350"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 功能调用连接 -->
        <mxCell id="51" value="状态管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="16" target="17">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="52" value="优先级计算" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="17" target="18">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="53" value="处理决策" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="18" target="19">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="54" value="任务传输" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="19" target="20">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="55" value="失败重试" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="20" target="21">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <mxCell id="56" value="任务完成" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="21" target="22">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- 业务服务调用 -->
        <mxCell id="57" value="查询调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="8" target="24">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="400"/>
              <mxPoint x="310" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="58" value="分组查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="8" target="25">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="400"/>
              <mxPoint x="460" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="59" value="调度接口" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10;fontStyle=1" edge="1" parent="1" source="8" target="26">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="400"/>
              <mxPoint x="720" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="60" value="统计查询" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#000000;fontSize=10" edge="1" parent="1" source="8" target="29">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="750" y="400"/>
              <mxPoint x="1280" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 高频调用连接 -->
        <mxCell id="61" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="18" target="34">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="717" y="840"/>
              <mxPoint x="1010" y="840"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="62" value="高频调用" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00aa00;fontSize=10" edge="1" parent="1" source="11" target="33">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="840"/>
              <mxPoint x="800" y="840"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 图例说明 -->
        <mxCell id="63" value="图例说明" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontColor=#000000;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="-100" width="200" height="220" as="geometry"/>
        </mxCell>
        
        <mxCell id="64" value="实线箭头：直接调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="40" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="65" value="虚线箭头：数据传递" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="65" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="66" value="粗线：主要数据流" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="90" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="67" value="深蓝色：管理控制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="115" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="68" value="绿色：数据实体" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="140" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="69" value="橙色：功能函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="165" width="120" height="20" as="geometry"/>
        </mxCell>
        
        <mxCell id="70" value="紫色：配置参数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" vertex="1" parent="63">
          <mxGeometry x="10" y="190" width="120" height="20" as="geometry"/>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>