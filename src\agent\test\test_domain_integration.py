"""
策略域集成测试
验证策略域与TransformerScheduler、HybridLearningAgent的集成
"""

import torch
import numpy as np
import unittest
import sys
import os
from unittest.mock import MagicMock, patch

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_domain import StrategyDomain, DomainManager
from transformer_scheduler import TransformerScheduler


class TestDomainTransformerIntegration(unittest.TestCase):
    """策略域与TransformerScheduler集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.domain = StrategyDomain(
            domain_id=0,
            bounds=(-180, -165, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
        # 创建独立的TransformerScheduler用于对比
        self.independent_transformer = TransformerScheduler(
            obs_dim=15 + 11,  # 观测 + 域特征
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
    def test_architecture_consistency(self):
        """测试架构一致性"""
        domain_transformer = self.domain.policy_network
        
        # 检查两个transformer的架构是否一致
        self.assertEqual(
            domain_transformer.d_model, 
            self.independent_transformer.d_model
        )
        self.assertEqual(
            domain_transformer.num_actions, 
            self.independent_transformer.num_actions
        )
        
        # 检查参数数量是否相同
        domain_params = sum(p.numel() for p in domain_transformer.parameters())
        independent_params = sum(p.numel() for p in self.independent_transformer.parameters())
        
        self.assertEqual(domain_params, independent_params)
        
    def test_input_output_compatibility(self):
        """测试输入输出兼容性"""
        observation = np.random.randn(15)
        task_features = np.random.randn(5, 8)
        
        # 域策略网络的输出
        domain_output = self.domain.get_action_probs(observation, task_features)
        
        # 独立TransformerScheduler的输出
        domain_features = self.domain.features.to_vector()
        combined_obs = np.concatenate([observation, domain_features])
        
        obs_tensor = torch.FloatTensor(combined_obs).unsqueeze(0)
        task_tensor = torch.FloatTensor(task_features).unsqueeze(0)
        
        with torch.no_grad():
            independent_output = self.independent_transformer(obs_tensor, task_tensor)
        
        # 检查输出形状一致
        self.assertEqual(domain_output.shape, independent_output.squeeze().numpy().shape)
        
        # 检查输出都是有效的概率分布
        self.assertTrue(np.all(domain_output >= 0))
        self.assertTrue(np.all(domain_output <= 1))
        np.testing.assert_allclose(np.sum(domain_output, axis=1), np.ones(5), atol=1e-6)
        
    def test_knowledge_distillation_compatibility(self):
        """测试知识蒸馏兼容性"""
        observation = np.random.randn(15)
        task_features = np.random.randn(3, 8)
        
        # 获取教师（域）输出
        teacher_probs = self.domain.get_action_probs(observation, task_features)
        
        # 模拟学生输出
        student_logits = torch.randn(3, 42)
        student_probs = torch.softmax(student_logits, dim=-1)
        
        # 计算KL散度损失（验证可以正常计算）
        teacher_probs_tensor = torch.FloatTensor(teacher_probs)
        kl_loss = torch.nn.functional.kl_div(
            torch.log(student_probs + 1e-8),
            teacher_probs_tensor,
            reduction='batchmean'
        )
        
        # 检查损失是有限值
        self.assertTrue(torch.isfinite(kl_loss))
        self.assertGreaterEqual(kl_loss.item(), 0)
        
    def test_parameter_transfer_compatibility(self):
        """测试参数传递兼容性"""
        # 创建两个域用于测试参数传递
        source_domain = StrategyDomain(
            domain_id=1,
            bounds=(-165, -150, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
        target_domain = StrategyDomain(
            domain_id=2,
            bounds=(-150, -135, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
        # 检查参数结构兼容性
        source_params = dict(source_domain.policy_network.named_parameters())
        target_params = dict(target_domain.policy_network.named_parameters())
        
        # 参数名称应该完全匹配
        self.assertEqual(set(source_params.keys()), set(target_params.keys()))
        
        # 参数形状应该完全匹配
        for name in source_params.keys():
            self.assertEqual(
                source_params[name].shape,
                target_params[name].shape
            )
        
        # 测试实际的参数复制
        with torch.no_grad():
            for name, target_param in target_domain.policy_network.named_parameters():
                if name in source_params:
                    target_param.data.copy_(source_params[name].data)
        
        # 验证复制成功
        for name, target_param in target_domain.policy_network.named_parameters():
            if name in source_params:
                self.assertTrue(torch.equal(target_param.data, source_params[name].data))


class TestDomainManagerIntegration(unittest.TestCase):
    """DomainManager集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.manager = DomainManager(
            num_domains=6,
            obs_dim=15,
            task_feature_dim=8,
            d_model=64,  # 减小模型用于测试
            nhead=4,
            num_encoder_layers=1,
            num_decoder_layers=1,
            num_actions=42
        )
        
    def test_cross_domain_knowledge_transfer(self):
        """测试跨域知识传递"""
        # 选择两个相邻的域
        source_id = 0
        neighbor_ids = self.manager.domain_neighbors.get(source_id, [])
        
        if neighbor_ids:
            target_id = neighbor_ids[0]
            
            # 记录传递前的性能
            source_domain = self.manager.domains[source_id]
            target_domain = self.manager.domains[target_id]
            
            # 模拟测试数据
            observation = np.random.randn(15)
            task_features = np.random.randn(2, 8)
            
            # 获取传递前的输出
            target_output_before = target_domain.get_action_probs(observation, task_features)
            
            # 执行知识传递
            self.manager.transfer_knowledge_between_domains(
                source_id, target_id, transfer_rate=0.2
            )
            
            # 获取传递后的输出
            target_output_after = target_domain.get_action_probs(observation, task_features)
            
            # 检查输出发生了变化
            self.assertFalse(np.allclose(target_output_before, target_output_after, atol=1e-6))
        else:
            self.skipTest(f"域 {source_id} 没有邻居，跳过跨域知识传递测试")
            
    def test_policy_fusion_across_domains(self):
        """测试跨域策略融合"""
        domain = self.manager.domains[0]
        
        # 创建模拟的多个智能体经验
        agent_experiences = []
        for i in range(5):
            exp = {
                'performance': 0.6 + i * 0.1,
                'policy_params': {}
            }
            
            # 使用其他域的参数作为智能体参数
            source_domain_id = (i + 1) % len(self.manager.domains)
            source_domain = self.manager.domains[source_domain_id]
            
            for name, param in source_domain.policy_network.named_parameters():
                exp['policy_params'][name] = param.data.clone()
            
            agent_experiences.append(exp)
        
        # 记录更新前的参数
        old_params = {}
        for name, param in domain.policy_network.named_parameters():
            old_params[name] = param.data.clone()
        
        # 执行策略融合
        domain.update_policy(agent_experiences, learning_rate=0.1)
        
        # 检查参数确实发生了变化
        params_changed = False
        for name, param in domain.policy_network.named_parameters():
            if not torch.equal(old_params[name], param.data):
                params_changed = True
                break
        
        self.assertTrue(params_changed, "跨域策略融合应该改变域参数")
        
    def test_neighbor_relationship_consistency(self):
        """测试邻居关系一致性"""
        # 检查邻居关系是对称的
        for domain_id, neighbors in self.manager.domain_neighbors.items():
            for neighbor_id in neighbors:
                # 检查反向关系
                reverse_neighbors = self.manager.domain_neighbors.get(neighbor_id, [])
                self.assertIn(domain_id, reverse_neighbors, 
                            f"域 {neighbor_id} 应该将域 {domain_id} 作为邻居")
        
        # 检查没有域将自己作为邻居
        for domain_id, neighbors in self.manager.domain_neighbors.items():
            self.assertNotIn(domain_id, neighbors, 
                           f"域 {domain_id} 不应该将自己作为邻居")
            
    def test_collective_intelligence_simulation(self):
        """测试集体智慧模拟"""
        # 模拟多个时间步的集体学习过程
        num_timesteps = 10
        
        for timestep in range(num_timesteps):
            # 模拟全局统计更新
            global_stats = {}
            for domain_id in self.manager.domains.keys():
                global_stats[domain_id] = {
                    'arrival_rate': np.random.random(),
                    'avg_priority': np.random.random(),
                    'completion_rate': np.random.random()
                }
            
            # 更新所有域的特征
            current_time = timestep * 3600  # 每小时更新一次
            self.manager.update_all_domains(current_time, global_stats)
            
            # 模拟智能体访问和知识贡献
            for domain_id, domain in self.manager.domains.items():
                # 随机添加访问智能体
                if np.random.random() > 0.5:
                    agent_id = np.random.randint(1000, 9999)
                    domain.add_visiting_agent(agent_id)
                
                # 模拟智能体经验贡献
                if len(domain.visiting_agents) > 0:
                    agent_experiences = []
                    for _ in range(min(3, len(domain.visiting_agents))):
                        exp = {
                            'performance': np.random.random(),
                            'policy_params': {}
                        }
                        
                        # 创建随机参数变化
                        for name, param in domain.policy_network.named_parameters():
                            exp['policy_params'][name] = param.data + torch.randn_like(param.data) * 0.01
                        
                        agent_experiences.append(exp)
                    
                    # 更新域策略
                    domain.update_policy(agent_experiences, learning_rate=0.05)
            
            # 执行邻居间知识传递
            if timestep % 3 == 0:  # 每3个时间步执行一次知识传递
                for domain_id in self.manager.domains.keys():
                    neighbors = self.manager.get_neighbor_domains(domain_id)
                    for neighbor in neighbors[:1]:  # 只与第一个邻居传递
                        self.manager.transfer_knowledge_between_domains(
                            domain_id, neighbor.domain_id, transfer_rate=0.05
                        )
        
        # 验证系统经过多轮更新后仍然稳定
        observation = np.random.randn(15)
        task_features = np.random.randn(2, 8)
        
        for domain in self.manager.domains.values():
            action_probs = domain.get_action_probs(observation, task_features)
            
            # 检查输出仍然是有效的概率分布
            self.assertTrue(np.all(action_probs >= 0))
            self.assertTrue(np.all(action_probs <= 1))
            np.testing.assert_allclose(np.sum(action_probs, axis=1), np.ones(2), atol=1e-5)


class TestTeacherStudentInterface(unittest.TestCase):
    """教师-学生接口测试"""
    
    def setUp(self):
        """测试初始化"""
        self.teacher_domain = StrategyDomain(
            domain_id=0,
            bounds=(-180, -165, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
        # 模拟学生网络（相同架构）
        self.student_network = TransformerScheduler(
            obs_dim=15,  # 学生不包含域特征
            task_feature_dim=8,
            d_model=128,
            nhead=4,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
    def test_knowledge_distillation_interface(self):
        """测试知识蒸馏接口"""
        observation = np.random.randn(15)
        task_features = np.random.randn(4, 8)
        
        # 教师输出
        teacher_probs = self.teacher_domain.get_action_probs(observation, task_features)
        
        # 学生输出
        obs_tensor = torch.FloatTensor(observation).unsqueeze(0)
        task_tensor = torch.FloatTensor(task_features).unsqueeze(0)
        
        with torch.no_grad():
            student_probs = self.student_network(obs_tensor, task_tensor)
        
        # 验证可以计算蒸馏损失
        teacher_tensor = torch.FloatTensor(teacher_probs)
        student_log_probs = torch.log(student_probs.squeeze() + 1e-8)
        
        kl_loss = torch.nn.functional.kl_div(
            student_log_probs, teacher_tensor, reduction='batchmean'
        )
        
        self.assertTrue(torch.isfinite(kl_loss))
        self.assertGreaterEqual(kl_loss.item(), 0)
        
        # 测试带温度的软标签
        temperature = 3.0
        teacher_logits = torch.log(teacher_tensor + 1e-8)
        teacher_soft = torch.softmax(teacher_logits / temperature, dim=-1)
        student_soft = torch.softmax(student_log_probs / temperature, dim=-1)
        
        soft_kl_loss = torch.nn.functional.kl_div(
            torch.log(student_soft + 1e-8), teacher_soft, reduction='batchmean'
        )
        
        self.assertTrue(torch.isfinite(soft_kl_loss))
        
    def test_parameter_sharing_interface(self):
        """测试参数共享接口"""
        # 模拟学生参数传递给教师
        student_state_dict = self.student_network.state_dict()
        
        # 创建模拟的智能体经验
        agent_experience = {
            'performance': 0.8,
            'policy_params': student_state_dict
        }
        
        # 检查参数兼容性
        teacher_params = dict(self.teacher_domain.policy_network.named_parameters())
        
        # 由于学生网络obs_dim较小，某些参数可能不匹配
        # 测试部分参数的兼容性
        compatible_params = 0
        total_params = 0
        
        for name, student_param in student_state_dict.items():
            total_params += 1
            if name in teacher_params:
                teacher_param = teacher_params[name]
                if student_param.shape == teacher_param.shape:
                    compatible_params += 1
        
        # 至少应该有一部分参数是兼容的
        compatibility_ratio = compatible_params / total_params
        print(f"参数兼容比例: {compatibility_ratio:.2%}")
        
        # 对于transformer的大部分层，参数应该是兼容的
        self.assertGreater(compatibility_ratio, 0.5)


def run_integration_tests():
    """运行集成测试"""
    print("=== StrategyDomain集成测试 ===\n")
    
    test_classes = [
        TestDomainTransformerIntegration,
        TestDomainManagerIntegration,
        TestTeacherStudentInterface
    ]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    
    for test_class in test_classes:
        print(f"运行 {test_class.__name__} 测试...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        total_failures += len(result.failures)
        total_errors += len(result.errors)
        
        print(f"{test_class.__name__}: {result.testsRun}个测试, {len(result.failures)}个失败, {len(result.errors)}个错误\n")
    
    # 总结
    print("=== 集成测试总结 ===")
    print(f"总测试数: {total_tests}")
    print(f"成功: {total_tests - total_failures - total_errors}")
    print(f"失败: {total_failures}")
    print(f"错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ 所有集成测试通过！")
    else:
        print("❌ 存在集成测试失败或错误")
    
    return total_failures == 0 and total_errors == 0


if __name__ == "__main__":
    run_integration_tests()