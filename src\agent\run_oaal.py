"""
运行OAAL框架的主程序
输出延迟、能耗、任务完成率等关键指标
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import numpy as np
import torch
from collections import defaultdict
import time
import json
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from src.env.satellite_env import parallel_env
from src.agent.oaal_framework import OAALFramework


class OAALTrainer:
    """OAAL训练器 - 管理训练过程和指标收集"""
    
    def __init__(self, env_config_path: str = None):
        # 设置默认配置路径
        if env_config_path is None:
            import os
            env_config_path = os.path.join(os.path.dirname(__file__), "..", "env", "config.yaml")
        
        # 创建环境
        print("正在创建卫星环境...")
        print(f"使用配置文件: {env_config_path}")
        self.env = parallel_env(config_file=env_config_path)
        print(f"环境创建成功: {self.env.num_agents} 个卫星智能体")
        
        # 创建OAAL框架
        print("\n正在初始化OAAL框架...")
        self.oaal = OAALFramework(
            num_satellites=self.env.num_agents,
            num_domains=24,
            hidden_dim=256,
            learning_rate=3e-4,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        
        # 性能指标收集
        self.metrics = {
            'episode': [],
            'step': [],
            'task_completion_rate': [],
            'average_delay': [],
            'energy_consumption': [],
            'energy_efficiency': [],
            'queue_length': [],
            'coordination_rate': [],
            'offload_rate': [],
            'rewards': []
        }
        
        # 实时指标
        self.current_metrics = defaultdict(list)
        
    def collect_step_metrics(self, infos: Dict, rewards: Dict, actions: Dict):
        """收集每步的指标"""
        # 任务完成情况
        completed_tasks = 0
        total_tasks = 0
        total_delay = 0
        total_energy = 0
        total_queue_length = 0
        offload_count = 0
        
        for agent_id, info in infos.items():
            # 任务完成率
            if 'tasks_completed' in info:
                completed_tasks += info['tasks_completed']
            if 'tasks_total' in info:
                total_tasks += info['tasks_total']
            
            # 延迟
            if 'task_delays' in info:
                total_delay += sum(info['task_delays'])
            
            # 能耗
            if 'energy_consumed' in info:
                total_energy += info['energy_consumed']
            
            # 队列长度
            if 'queue_length' in info:
                total_queue_length += info['queue_length']
            
            # 卸载率
            if agent_id in actions and actions[agent_id] > 0:
                offload_count += 1
        
        # 计算平均值
        num_agents = len(infos)
        
        self.current_metrics['completed_tasks'].append(completed_tasks)
        self.current_metrics['total_tasks'].append(total_tasks)
        self.current_metrics['total_delay'].append(total_delay)
        self.current_metrics['avg_energy'].append(total_energy / num_agents if num_agents > 0 else 0)
        self.current_metrics['avg_queue_length'].append(total_queue_length / num_agents if num_agents > 0 else 0)
        self.current_metrics['offload_rate'].append(offload_count / num_agents if num_agents > 0 else 0)
        self.current_metrics['avg_reward'].append(np.mean(list(rewards.values())))
    
    def compute_episode_metrics(self) -> Dict[str, float]:
        """计算回合指标"""
        metrics = {}
        
        # 任务完成率
        total_completed = sum(self.current_metrics['completed_tasks'])
        total_tasks = sum(self.current_metrics['total_tasks'])
        metrics['task_completion_rate'] = total_completed / total_tasks if total_tasks > 0 else 0
        
        # 平均延迟
        total_delay = sum(self.current_metrics['total_delay'])
        metrics['average_delay'] = total_delay / total_completed if total_completed > 0 else 0
        
        # 能耗
        metrics['energy_consumption'] = np.mean(self.current_metrics['avg_energy'])
        
        # 能源效率 (完成任务数/能耗)
        metrics['energy_efficiency'] = total_completed / metrics['energy_consumption'] if metrics['energy_consumption'] > 0 else 0
        
        # 平均队列长度
        metrics['queue_length'] = np.mean(self.current_metrics['avg_queue_length'])
        
        # 卸载率
        metrics['offload_rate'] = np.mean(self.current_metrics['offload_rate'])
        
        # 平均奖励
        metrics['average_reward'] = np.mean(self.current_metrics['avg_reward'])
        
        # 协同率
        coord_stats = self.oaal.coordination_manager.coordination_stats
        if coord_stats['requests_sent'] > 0:
            metrics['coordination_rate'] = coord_stats['requests_accepted'] / coord_stats['requests_sent']
        else:
            metrics['coordination_rate'] = 0
        
        return metrics
    
    def train(self, num_episodes: int = 100, max_steps: int = 100):
        """训练OAAL"""
        print(f"\n开始训练: {num_episodes} 回合, 每回合最多 {max_steps} 步")
        print("=" * 80)
        
        for episode in range(num_episodes):
            # 每个episode开始时重置环境
            observations, infos = self.env.reset()
            print(f"\n=== 回合 {episode + 1}/{num_episodes} ===")
            print(f"环境重置完成: {len(observations)}个卫星")
            
            self.oaal.reset_episode_stats()
            self.current_metrics.clear()
            
            episode_start_time = time.time()
            
            for step in range(max_steps):
                # 1. 更新卫星位置
                satellite_positions = {}
                for agent_id, info in infos.items():
                    # 从SatelliteXXX格式提取数字ID
                    if agent_id.startswith('Satellite'):
                        agent_idx = int(agent_id[9:])  # 跳过'Satellite'部分
                        if 'satellite' in info and hasattr(info['satellite'], 'position'):
                            pos = info['satellite'].position
                            satellite_positions[agent_idx] = (pos.latitude, pos.longitude)
                
                if satellite_positions:
                    self.oaal.update_agent_positions(satellite_positions)
                
                # 2. 准备任务队列和观测
                task_queues = {}
                valid_actions = {}
                obs_dict = {}
                
                for agent_id, info in infos.items():
                    # 从SatelliteXXX格式提取数字ID
                    if not agent_id.startswith('Satellite'):
                        continue
                    agent_idx = int(agent_id[9:])
                    
                    # 获取任务队列
                    if 'satellite' in info and hasattr(info['satellite'], 'task_queue'):
                        task_queue = []
                        for task in info['satellite'].task_queue:
                            task_dict = {
                                'task_id': getattr(task, 'task_id', f'task_{step}'),
                                'type_id': getattr(task, 'type_id', 1),
                                'data_size_mb': getattr(task, 'data_size_mb', 50),
                                'complexity_cycles_per_bit': getattr(task, 'complexity_cycles_per_bit', 100),
                                'priority': getattr(task, 'priority', 2),
                                'deadline_timestamp': getattr(task, 'deadline_timestamp', step + 100),
                                'arrival_time': getattr(task, 'arrival_time_seconds', step),
                                'current_time': step * 10  # 假设每步10秒
                            }
                            task_queue.append(task_dict)
                        task_queues[agent_idx] = task_queue
                    else:
                        task_queues[agent_idx] = []
                    
                    # 有效动作
                    valid_acts = []
                    if agent_idx in task_queues:
                        for _ in task_queues[agent_idx]:
                            valid_acts.append(info.get('valid_actions', [0, 1, 2]))
                    valid_actions[agent_idx] = valid_acts
                    
                    # 观测
                    obs_dict[agent_idx] = observations[agent_id]
                
                # 3. 选择动作
                if any(len(q) > 0 for q in task_queues.values()):
                    oaal_actions = self.oaal.select_actions(obs_dict, task_queues, valid_actions)
                    
                    # 转换为环境动作
                    env_actions = {}
                    for agent_id in self.env.agents:
                        if agent_id.startswith('Satellite'):
                            agent_idx = int(agent_id[9:])
                            if agent_idx in oaal_actions and oaal_actions[agent_idx]:
                                env_actions[agent_id] = oaal_actions[agent_idx][0]
                            else:
                                env_actions[agent_id] = 0
                        else:
                            env_actions[agent_id] = 0
                else:
                    env_actions = {agent: 0 for agent in self.env.agents}
                
                # 4. 执行环境步骤
                next_observations, rewards, dones, next_infos = self.env.step(env_actions)
                
                # 5. 收集指标
                self.collect_step_metrics(infos, rewards, env_actions)
                
                # 6. 收集经验
                experiences = {}
                for agent_id, reward in rewards.items():
                    if not agent_id.startswith('Satellite'):
                        continue
                    agent_idx = int(agent_id[9:])
                    
                    if agent_idx in task_queues and task_queues[agent_idx]:
                        task_features = []
                        for task in task_queues[agent_idx]:
                            feat = np.zeros(8)
                            feat[0] = task.get('type_id', 0) / 5.0
                            feat[1] = min(task.get('data_size_mb', 0) / 100.0, 1.0)
                            feat[2] = min(task.get('complexity_cycles_per_bit', 100) / 1000.0, 1.0)
                            feat[3] = task.get('priority', 1) / 3.0
                            task_features.append(feat)
                        
                        exp = {
                            'observation': observations[agent_id],
                            'task_features': task_features,
                            'actions': [env_actions[agent_id]],
                            'reward': reward,
                            'next_observation': next_observations[agent_id],
                            'done': dones[agent_id],
                            'info': {}
                        }
                        
                        if agent_idx not in experiences:
                            experiences[agent_idx] = []
                        experiences[agent_idx].append(exp)
                
                # 7. 协同决策
                if step % 5 == 0:
                    satellite_states = {}
                    communication_links = []
                    
                    for agent_id, info in infos.items():
                        if not agent_id.startswith('Satellite'):
                            continue
                        agent_idx = int(agent_id[9:])
                        satellite_states[agent_idx] = {
                            'observation': observations[agent_id],
                            'queue_length': len(task_queues.get(agent_idx, []))
                        }
                    
                    # 简化的通信链路
                    for i in range(0, min(10, len(self.env.agents)), 2):
                        for j in range(i+1, min(i+3, len(self.env.agents))):
                            communication_links.append({
                                'source_id': i,
                                'target_id': j,
                                'distance_km': 1000,
                                'data_rate_mbps': 50,
                                'delay_ms': 10,
                                'link_quality': 0.8
                            })
                    
                    if satellite_states and communication_links:
                        self.oaal.coordinate_agents(satellite_states, communication_links)
                
                # 8. 更新智能体
                if experiences and step > 0 and step % 10 == 0:
                    self.oaal.update_agents(experiences)
                
                # 9. 更新域
                if step % 20 == 0:
                    self.oaal.update_domains(step * 10.0, {})
                
                # 更新状态
                observations = next_observations
                infos = next_infos
                
                if all(dones.values()):
                    print(f"  环境在第{step+1}步结束")
                    break
            
            # 计算回合指标
            episode_time = time.time() - episode_start_time
            episode_metrics = self.compute_episode_metrics()
            
            # 保存指标
            self.metrics['episode'].append(episode + 1)
            self.metrics['task_completion_rate'].append(episode_metrics['task_completion_rate'])
            self.metrics['average_delay'].append(episode_metrics['average_delay'])
            self.metrics['energy_consumption'].append(episode_metrics['energy_consumption'])
            self.metrics['energy_efficiency'].append(episode_metrics['energy_efficiency'])
            self.metrics['queue_length'].append(episode_metrics['queue_length'])
            self.metrics['coordination_rate'].append(episode_metrics['coordination_rate'])
            self.metrics['offload_rate'].append(episode_metrics['offload_rate'])
            self.metrics['rewards'].append(episode_metrics['average_reward'])
            
            # 打印回合总结
            print(f"\n回合 {episode + 1}/{num_episodes} 完成 (用时: {episode_time:.2f}秒)")
            print(f"  任务完成率: {episode_metrics['task_completion_rate']:.2%}")
            print(f"  平均延迟: {episode_metrics['average_delay']:.2f} 秒")
            print(f"  能耗: {episode_metrics['energy_consumption']:.2f} J")
            print(f"  能源效率: {episode_metrics['energy_efficiency']:.2f} 任务/J")
            print(f"  平均队列长度: {episode_metrics['queue_length']:.2f}")
            print(f"  卸载率: {episode_metrics['offload_rate']:.2%}")
            print(f"  协同成功率: {episode_metrics['coordination_rate']:.2%}")
            print(f"  平均奖励: {episode_metrics['average_reward']:.3f}")
            
            # 定期保存
            if (episode + 1) % 10 == 0:
                self.save_results(episode + 1)
                self.oaal.save_checkpoint(f"checkpoints/oaal_ep{episode + 1}.pt")
            
            # 检查环境是否已结束
            if all(dones.values()):
                print(f"\n环境已达到最大步数限制，训练提前结束于第{episode + 1}回合")
                break
        
        # 训练结束
        print("\n" + "=" * 80)
        print("训练完成!")
        self.print_final_summary()
        self.plot_metrics()
        self.env.close()
    
    def print_final_summary(self):
        """打印最终总结"""
        print("\n最终性能总结:")
        print("-" * 40)
        
        # 计算最后10个回合的平均值
        last_n = min(10, len(self.metrics['episode']))
        
        avg_completion = np.mean(self.metrics['task_completion_rate'][-last_n:])
        avg_delay = np.mean(self.metrics['average_delay'][-last_n:])
        avg_energy = np.mean(self.metrics['energy_consumption'][-last_n:])
        avg_efficiency = np.mean(self.metrics['energy_efficiency'][-last_n:])
        avg_coordination = np.mean(self.metrics['coordination_rate'][-last_n:])
        
        print(f"平均任务完成率: {avg_completion:.2%}")
        print(f"平均延迟: {avg_delay:.2f} 秒")
        print(f"平均能耗: {avg_energy:.2f} J")
        print(f"平均能源效率: {avg_efficiency:.2f} 任务/J")
        print(f"平均协同成功率: {avg_coordination:.2%}")
    
    def save_results(self, episode: int):
        """保存结果"""
        # 创建结果目录
        os.makedirs("results/oaal", exist_ok=True)
        
        # 保存指标
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"results/oaal/metrics_ep{episode}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.metrics, f, indent=2)
        
        print(f"  结果已保存至: {filename}")
    
    def plot_metrics(self):
        """绘制性能曲线"""
        if len(self.metrics['episode']) < 2:
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('OAAL训练性能指标', fontsize=16)
        
        # 任务完成率
        axes[0, 0].plot(self.metrics['episode'], self.metrics['task_completion_rate'])
        axes[0, 0].set_title('任务完成率')
        axes[0, 0].set_xlabel('回合')
        axes[0, 0].set_ylabel('完成率')
        axes[0, 0].grid(True)
        
        # 平均延迟
        axes[0, 1].plot(self.metrics['episode'], self.metrics['average_delay'])
        axes[0, 1].set_title('平均延迟')
        axes[0, 1].set_xlabel('回合')
        axes[0, 1].set_ylabel('延迟 (秒)')
        axes[0, 1].grid(True)
        
        # 能耗
        axes[0, 2].plot(self.metrics['episode'], self.metrics['energy_consumption'])
        axes[0, 2].set_title('能源消耗')
        axes[0, 2].set_xlabel('回合')
        axes[0, 2].set_ylabel('能耗 (J)')
        axes[0, 2].grid(True)
        
        # 能源效率
        axes[1, 0].plot(self.metrics['episode'], self.metrics['energy_efficiency'])
        axes[1, 0].set_title('能源效率')
        axes[1, 0].set_xlabel('回合')
        axes[1, 0].set_ylabel('任务/J')
        axes[1, 0].grid(True)
        
        # 协同率
        axes[1, 1].plot(self.metrics['episode'], self.metrics['coordination_rate'])
        axes[1, 1].set_title('协同成功率')
        axes[1, 1].set_xlabel('回合')
        axes[1, 1].set_ylabel('成功率')
        axes[1, 1].grid(True)
        
        # 平均奖励
        axes[1, 2].plot(self.metrics['episode'], self.metrics['rewards'])
        axes[1, 2].set_title('平均奖励')
        axes[1, 2].set_xlabel('回合')
        axes[1, 2].set_ylabel('奖励')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        
        # 保存图表
        os.makedirs("results/oaal/plots", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f"results/oaal/plots/metrics_{timestamp}.png", dpi=150)
        print(f"\n性能曲线已保存至: results/oaal/plots/metrics_{timestamp}.png")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='OAAL框架训练程序')
    parser.add_argument('--episodes', type=int, default=50, help='训练回合数')
    parser.add_argument('--steps', type=int, default=100, help='每回合最大步数')
    parser.add_argument('--config', type=str, default=None, help='环境配置文件路径')
    parser.add_argument('--checkpoint', type=str, default=None, help='从检查点恢复')
    
    args = parser.parse_args()
    
    # 创建必要的目录
    os.makedirs("checkpoints", exist_ok=True)
    os.makedirs("results/oaal", exist_ok=True)
    
    # 创建训练器
    trainer = OAALTrainer(env_config_path=args.config)
    
    # 加载检查点
    if args.checkpoint:
        trainer.oaal.load_checkpoint(args.checkpoint)
        print(f"从检查点恢复: {args.checkpoint}")
    
    # 开始训练
    trainer.train(num_episodes=args.episodes, max_steps=args.steps)


if __name__ == "__main__":
    main()