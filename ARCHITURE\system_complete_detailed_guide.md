# SPACE-OAAL 系统超详细架构说明

## 1. 架构全景概述

这是SPACE-OAAL系统的**完整详细架构图**，包含了系统的每一个重要组件：

### 1.1 完整性检查清单
- ✅ **所有类定义**: 包含属性、方法、参数类型
- ✅ **所有枚举类型**: SatelliteStatus, ResourceType, TaskState, TaskPriority
- ✅ **所有数据结构**: Position, EnergyState, ResourceState, CommunicationState等
- ✅ **所有配置参数**: config.yaml的完整结构
- ✅ **所有数据文件**: CSV和JSON文件的详细格式
- ✅ **所有方法调用**: 包含参数和返回值类型
- ✅ **所有数据流向**: 从文件到对象的完整路径
- ✅ **所有性能指标**: 时间、内存、CPU使用率
- ✅ **所有缓存机制**: LRU缓存、时间窗口缓存等
- ✅ **所有错误处理**: 异常类型和容错策略

## 2. 系统组件完整清单

### 2.1 文件依赖 (4个)
1. **config.yaml** - 系统配置文件
2. **satellite_processed_data.csv** - 36颗卫星×1000时隙轨道数据
3. **updated_global_ground_stations.csv** - 420个地面站坐标
4. **task_generation_results.json** - 任务生成数据

### 2.2 核心类 (15个)
1. **IntegrationManager** - 集成管理器
2. **SatelliteAdapter** - 卫星适配器
3. **TaskAdapter** - 任务适配器
4. **OrbitalUpdater** - 轨道更新器
5. **CommunicationManager** - 通信管理器
6. **TaskGenerator** - 任务生成器
7. **TaskManager** - 任务管理器
8. **SatelliteNode** - 卫星节点
9. **Satellite** - 轨道卫星
10. **GroundStation** - 地面站
11. **Task** - 生成任务
12. **EnhancedTask** - 增强任务
13. **LinkState** - 链路状态
14. **NetworkState** - 网络状态
15. **TaskLoader** - 任务加载器

### 2.3 数据结构类 (8个)
1. **Position** - 位置状态
2. **EnergyState** - 能量状态
3. **ResourceState** - 资源状态
4. **CommunicationState** - 通信状态
5. **PerformanceMetrics** - 性能指标
6. **LinkInfo** - 链路信息
7. **TaskQueue** - 任务队列状态
8. **SystemState** - 系统状态

### 2.4 枚举类 (4个)
1. **SatelliteStatus** - 卫星状态枚举
2. **ResourceType** - 资源类型枚举
3. **TaskState** - 任务状态枚举
4. **TaskPriority** - 任务优先级枚举

### 2.5 核心方法 (50+个)
详见架构图中每个类的方法列表

## 3. 详细数据流分析

### 3.1 初始化数据流
```
程序启动
├── main() 创建 IntegrationManager
├── IntegrationManager.__init__()
│   ├── _load_config("config.yaml") → Dict[str, Any]
│   ├── 创建 SatelliteAdapter(config)
│   │   ├── 创建 OrbitalUpdater(config)
│   │   │   ├── _load_satellite_data() ← satellite_processed_data.csv
│   │   │   │   └── pandas.read_csv() → 36,000行数据
│   │   │   └── _create_ground_stations() ← updated_global_ground_stations.csv
│   │   │       └── 创建420个GroundStation对象
│   │   └── 创建 CommunicationManager(orbital_updater, config)
│   └── 创建 TaskAdapter()
│       └── 创建 TaskGenerator() ← task_generation_results.json
└── 初始化完成，准备仿真循环
```

### 3.2 仿真循环数据流 (每个时隙)
```
IntegrationManager.step(timeslot)
├── [阶段1] 卫星状态同步
│   ├── SatelliteAdapter.sync_all_satellites(timeslot)
│   │   ├── OrbitalUpdater.get_satellites_at_time(timeslot)
│   │   │   └── 