"""
StrategyDomain测试文件
验证修改后的策略域管理组件功能和性能
"""

import torch
import numpy as np
import unittest
import tempfile
import json
import os
import sys
from unittest.mock import patch, MagicMock
from typing import Dict, List

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_domain import StrategyDomain, DomainManager, DomainFeatures
from transformer_scheduler import TransformerScheduler


class TestDomainFeatures(unittest.TestCase):
    """DomainFeatures测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.features = DomainFeatures(
            domain_id=0,
            geographic_bounds=(-180, -165, -90, 90)
        )
        
    def test_feature_vector_generation(self):
        """测试特征向量生成"""
        vector = self.features.to_vector()
        
        # 检查向量长度
        self.assertEqual(len(vector), 11)
        
        # 检查所有值都在合理范围内
        self.assertTrue(np.all(vector >= 0))
        self.assertTrue(np.all(vector <= 1))
        
        # 检查特定特征
        self.assertEqual(vector[3], 0.0)  # local_time / 24.0
        self.assertEqual(vector[4], 0.0)  # is_business_hours
        
    def test_feature_updates(self):
        """测试特征更新"""
        # 更新特征
        self.features.local_time = 12.0
        self.features.is_business_hours = True
        self.features.task_arrival_rate = 0.8
        
        vector = self.features.to_vector()
        
        # 检查更新后的值
        self.assertEqual(vector[3], 0.5)  # 12.0 / 24.0
        self.assertEqual(vector[4], 1.0)  # True -> 1.0
        self.assertEqual(vector[5], 0.8)  # task_arrival_rate


class TestStrategyDomain(unittest.TestCase):
    """StrategyDomain测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.domain = StrategyDomain(
            domain_id=0,
            bounds=(-180, -165, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=128,  # 减小模型用于测试
            nhead=4,
            num_encoder_layers=1,
            num_decoder_layers=1,
            num_actions=42
        )
        
    def test_initialization(self):
        """测试域初始化"""
        # 检查基本属性
        self.assertEqual(self.domain.domain_id, 0)
        self.assertEqual(self.domain.bounds, (-180, -165, -90, 90))
        self.assertEqual(self.domain.obs_dim, 15)
        self.assertEqual(self.domain.num_actions, 42)
        
        # 检查策略网络类型
        self.assertIsInstance(self.domain.policy_network, TransformerScheduler)
        
        # 检查特征对象
        self.assertIsInstance(self.domain.features, DomainFeatures)
        
    def test_position_checking(self):
        """测试位置检查"""
        # 测试在域内的位置
        self.assertTrue(self.domain.is_position_in_domain(-170, 0))
        self.assertTrue(self.domain.is_position_in_domain(-175, -45))
        
        # 测试在域外的位置
        self.assertFalse(self.domain.is_position_in_domain(-160, 0))
        self.assertFalse(self.domain.is_position_in_domain(10, 0))
        
        # 测试边界情况
        self.assertTrue(self.domain.is_position_in_domain(-180, 0))
        self.assertTrue(self.domain.is_position_in_domain(-165, 0))
        
    def test_visiting_agents_management(self):
        """测试访问智能体管理"""
        # 添加智能体
        self.domain.add_visiting_agent(1)
        self.domain.add_visiting_agent(2)
        
        self.assertEqual(len(self.domain.visiting_agents), 2)
        self.assertIn(1, self.domain.visiting_agents)
        self.assertIn(2, self.domain.visiting_agents)
        
        # 移除智能体
        self.domain.remove_visiting_agent(1)
        self.assertEqual(len(self.domain.visiting_agents), 1)
        self.assertNotIn(1, self.domain.visiting_agents)
        
        # 移除不存在的智能体（应该不报错）
        self.domain.remove_visiting_agent(999)
        self.assertEqual(len(self.domain.visiting_agents), 1)
        
    def test_feature_updates(self):
        """测试特征更新"""
        task_stats = {
            'arrival_rate': 0.5,
            'avg_priority': 0.8,
            'completion_rate': 0.9
        }
        
        self.domain.update_features(
            current_time=43200,  # 12小时 = 43200秒
            task_stats=task_stats,
            satellite_count=5,
            link_quality=0.85
        )
        
        # 检查时间特征
        self.assertEqual(self.domain.features.local_time, 12.0)
        self.assertTrue(self.domain.features.is_business_hours)
        
        # 检查任务统计
        self.assertEqual(self.domain.features.task_arrival_rate, 0.5)
        self.assertEqual(self.domain.features.avg_task_priority, 0.8)
        self.assertEqual(self.domain.features.task_completion_rate, 0.9)
        
        # 检查网络特征
        self.assertEqual(self.domain.features.satellite_density, 0.5)  # 5/10
        self.assertEqual(self.domain.features.avg_link_quality, 0.85)
        
    def test_action_probability_generation(self):
        """测试动作概率生成"""
        observation = np.random.randn(15)
        task_features = np.random.randn(3, 8)  # 3个任务，每个8维特征
        
        # 测试基本调用
        action_probs = self.domain.get_action_probs(observation, task_features)
        
        # 检查输出形状
        self.assertEqual(action_probs.shape, (3, 42))  # 3个任务，42个动作
        
        # 检查概率和为1
        prob_sums = np.sum(action_probs, axis=1)
        np.testing.assert_allclose(prob_sums, np.ones(3), atol=1e-6)
        
        # 检查所有概率非负
        self.assertTrue(np.all(action_probs >= 0))
        self.assertTrue(np.all(action_probs <= 1))
        
    def test_action_probability_with_mask(self):
        """测试带掩码的动作概率生成"""
        observation = np.random.randn(15)
        task_features = np.random.randn(2, 8)
        
        # 创建动作掩码
        action_mask = np.zeros((2, 42), dtype=bool)
        action_mask[0, [0, 1, 2]] = True  # 第一个任务只允许动作0,1,2
        action_mask[1, [5, 10]] = True    # 第二个任务只允许动作5,10
        
        action_probs = self.domain.get_action_probs(observation, task_features, action_mask)
        
        # 检查被掩码的动作概率为0
        self.assertTrue(np.all(action_probs[0, 3:] == 0))
        self.assertTrue(np.all(action_probs[1, [0,1,2,3,4,6,7,8,9]] == 0))
        
        # 检查允许的动作概率大于0
        self.assertTrue(np.all(action_probs[0, :3] > 0))
        self.assertTrue(action_probs[1, 5] > 0)
        self.assertTrue(action_probs[1, 10] > 0)
        
    def test_policy_update_logic(self):
        """测试策略更新逻辑"""
        # 创建模拟的智能体经验
        agent_experiences = []
        for i in range(3):
            exp = {
                'performance': 0.8 + i * 0.1,  # 不同的性能
                'policy_params': {}
            }
            
            # 创建模拟的策略参数
            for name, param in self.domain.policy_network.named_parameters():
                exp['policy_params'][name] = param.data.clone() + torch.randn_like(param.data) * 0.1
            
            agent_experiences.append(exp)
        
        # 记录更新前的参数
        old_params = {}
        for name, param in self.domain.policy_network.named_parameters():
            old_params[name] = param.data.clone()
        
        # 执行策略更新
        self.domain.update_policy(agent_experiences, learning_rate=0.1)
        
        # 检查参数是否发生变化
        params_changed = False
        for name, param in self.domain.policy_network.named_parameters():
            if not torch.equal(old_params[name], param.data):
                params_changed = True
                break
        
        self.assertTrue(params_changed, "策略参数应该在更新后发生变化")
        
    def test_policy_update_empty_experiences(self):
        """测试空经验列表的策略更新"""
        # 记录更新前的参数
        old_params = {}
        for name, param in self.domain.policy_network.named_parameters():
            old_params[name] = param.data.clone()
        
        # 使用空经验列表更新
        self.domain.update_policy([])
        
        # 检查参数没有变化
        for name, param in self.domain.policy_network.named_parameters():
            self.assertTrue(torch.equal(old_params[name], param.data))


class TestDomainManager(unittest.TestCase):
    """DomainManager测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.manager = DomainManager(
            num_domains=4,  # 减少域数量用于测试
            obs_dim=15,
            task_feature_dim=8,
            d_model=64,  # 减小模型用于测试
            nhead=4,
            num_encoder_layers=1,
            num_decoder_layers=1,
            num_actions=42
        )
        
    def test_initialization(self):
        """测试管理器初始化"""
        self.assertEqual(self.manager.num_domains, 4)
        self.assertEqual(len(self.manager.domains), 4)
        
        # 检查所有域都正确初始化
        for domain_id, domain in self.manager.domains.items():
            self.assertIsInstance(domain, StrategyDomain)
            self.assertEqual(domain.domain_id, domain_id)
            
        # 检查邻居关系已预计算
        self.assertIsInstance(self.manager.domain_neighbors, dict)
        
    @patch('builtins.open')
    @patch('json.load')
    def test_initialization_with_regions_file(self, mock_json_load, mock_open):
        """测试使用regions.json文件初始化"""
        # 模拟regions.json文件内容
        mock_regions_data = [
            {
                'region_id': 1,
                'longitude_range': {'min': -180, 'max': -90},
                'latitude_range': {'min': -90, 'max': 90}
            },
            {
                'region_id': 2, 
                'longitude_range': {'min': -90, 'max': 0},
                'latitude_range': {'min': -90, 'max': 90}
            }
        ]
        
        mock_json_load.return_value = mock_regions_data
        mock_open.return_value.__enter__.return_value = MagicMock()
        
        # 创建管理器
        manager = DomainManager(num_domains=2)
        
        # 检查域是否正确创建
        self.assertEqual(len(manager.domains), 2)
        self.assertIn(0, manager.domains)  # region_id 1 -> domain_id 0
        self.assertIn(1, manager.domains)  # region_id 2 -> domain_id 1
        
    def test_position_to_domain_mapping(self):
        """测试位置到域的映射"""
        # 由于使用了回退策略（均分经度），测试具体位置
        domain = self.manager.get_domain_for_position(-170, 0)
        self.assertIsNotNone(domain)
        
        domain = self.manager.get_domain_for_position(30, 45)
        self.assertIsNotNone(domain)
        
        # 测试经度归一化
        domain1 = self.manager.get_domain_for_position(190, 0)  # 应该归一化为-170
        domain2 = self.manager.get_domain_for_position(-170, 0)
        self.assertEqual(domain1.domain_id, domain2.domain_id)
        
    def test_neighbor_computation(self):
        """测试邻居计算"""
        # 检查每个域都有邻居信息
        for domain_id in self.manager.domains.keys():
            neighbors = self.manager.get_neighbor_domains(domain_id)
            self.assertIsInstance(neighbors, list)
            
            # 检查邻居都是有效的域
            for neighbor in neighbors:
                self.assertIsInstance(neighbor, StrategyDomain)
                self.assertIn(neighbor.domain_id, self.manager.domains)
                
    def test_domain_adjacency_logic(self):
        """测试域相邻逻辑"""
        # 创建两个相邻的域进行测试
        domain_a = StrategyDomain(0, (-180, -90), obs_dim=15, task_feature_dim=8)
        domain_b = StrategyDomain(1, (-90, 0), obs_dim=15, task_feature_dim=8)
        domain_c = StrategyDomain(2, (0, 90), obs_dim=15, task_feature_dim=8)
        
        # 测试相邻关系
        self.assertTrue(self.manager._are_domains_adjacent(domain_a, domain_b))
        self.assertTrue(self.manager._are_domains_adjacent(domain_b, domain_c))
        self.assertFalse(self.manager._are_domains_adjacent(domain_a, domain_c))
        
    def test_longitude_wraparound_adjacency(self):
        """测试跨越180度经线的相邻关系"""
        domain_a = StrategyDomain(0, (170, 180), obs_dim=15, task_feature_dim=8)
        domain_b = StrategyDomain(1, (-180, -170), obs_dim=15, task_feature_dim=8)
        
        # 这两个域在180度经线处相邻
        self.assertTrue(self.manager._are_domains_adjacent(domain_a, domain_b))
        
    def test_all_domains_update(self):
        """测试所有域的更新"""
        global_stats = {
            0: {
                'arrival_rate': 0.5,
                'avg_priority': 0.8,
                'completion_rate': 0.9
            },
            1: {
                'arrival_rate': 0.3,
                'avg_priority': 0.6,
                'completion_rate': 0.7
            }
        }
        
        # 添加一些访问智能体
        self.manager.domains[0].add_visiting_agent(1)
        self.manager.domains[0].add_visiting_agent(2)
        
        # 执行更新
        self.manager.update_all_domains(current_time=43200, global_stats=global_stats)
        
        # 检查域0的特征是否正确更新
        domain_0 = self.manager.domains[0]
        self.assertEqual(domain_0.features.task_arrival_rate, 0.5)
        self.assertEqual(domain_0.features.avg_task_priority, 0.8)
        self.assertEqual(domain_0.features.task_completion_rate, 0.9)
        self.assertEqual(domain_0.features.satellite_density, 0.2)  # 2/10
        
    def test_knowledge_transfer(self):
        """测试域间知识传递"""
        source_id = 0
        target_id = 1
        
        # 记录传递前的参数
        source_params = {}
        target_params_before = {}
        
        for name, param in self.manager.domains[source_id].policy_network.named_parameters():
            source_params[name] = param.data.clone()
        
        for name, param in self.manager.domains[target_id].policy_network.named_parameters():
            target_params_before[name] = param.data.clone()
        
        # 执行知识传递
        self.manager.transfer_knowledge_between_domains(source_id, target_id, transfer_rate=0.1)
        
        # 检查目标域参数是否发生变化
        params_changed = False
        for name, param in self.manager.domains[target_id].policy_network.named_parameters():
            if not torch.equal(target_params_before[name], param.data):
                params_changed = True
                break
        
        self.assertTrue(params_changed, "目标域参数应该在知识传递后发生变化")


class TestStrategyDomainPerformance(unittest.TestCase):
    """StrategyDomain性能测试类"""
    
    def setUp(self):
        """性能测试初始化"""
        self.domain = StrategyDomain(
            domain_id=0,
            bounds=(-180, -165, -90, 90),
            obs_dim=15,
            task_feature_dim=8,
            d_model=256,
            nhead=8,
            num_encoder_layers=2,
            num_decoder_layers=2,
            num_actions=42
        )
        
    def test_action_generation_performance(self):
        """测试动作生成性能"""
        import time
        
        observation = np.random.randn(15)
        task_features = np.random.randn(10, 8)  # 10个任务
        
        # 预热
        for _ in range(5):
            self.domain.get_action_probs(observation, task_features)
        
        # 计时测试
        start_time = time.time()
        num_iterations = 100
        
        for _ in range(num_iterations):
            action_probs = self.domain.get_action_probs(observation, task_features)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / num_iterations
        
        print(f"Average action generation time: {avg_time:.4f}s")
        
        # 基本性能检查
        self.assertLess(avg_time, 0.1)  # 应该少于100ms
        
    def test_policy_update_performance(self):
        """测试策略更新性能"""
        import time
        
        # 创建大量智能体经验
        agent_experiences = []
        for i in range(50):  # 50个智能体经验
            exp = {
                'performance': np.random.random(),
                'policy_params': {}
            }
            
            for name, param in self.domain.policy_network.named_parameters():
                exp['policy_params'][name] = param.data.clone() + torch.randn_like(param.data) * 0.01
            
            agent_experiences.append(exp)
        
        # 计时测试
        start_time = time.time()
        self.domain.update_policy(agent_experiences, learning_rate=0.01)
        end_time = time.time()
        
        update_time = end_time - start_time
        print(f"Policy update time for 50 agents: {update_time:.4f}s")
        
        # 基本性能检查
        self.assertLess(update_time, 5.0)  # 应该少于5秒


def run_comprehensive_test():
    """运行综合测试"""
    print("=== StrategyDomain综合测试 ===\n")
    
    test_classes = [
        TestDomainFeatures,
        TestStrategyDomain,
        TestDomainManager,
        TestStrategyDomainPerformance
    ]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    
    for test_class in test_classes:
        print(f"运行 {test_class.__name__} 测试...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        total_failures += len(result.failures)
        total_errors += len(result.errors)
        
        print(f"{test_class.__name__}: {result.testsRun}个测试, {len(result.failures)}个失败, {len(result.errors)}个错误\n")
    
    # 总结
    print("=== 测试总结 ===")
    print(f"总测试数: {total_tests}")
    print(f"成功: {total_tests - total_failures - total_errors}")
    print(f"失败: {total_failures}")
    print(f"错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("✅ 所有测试通过！")
    else:
        print("❌ 存在测试失败或错误")
    
    return total_failures == 0 and total_errors == 0


if __name__ == "__main__":
    run_comprehensive_test()