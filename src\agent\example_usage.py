"""
OAAL框架使用示例
演示如何在卫星环境中使用OAAL框架
"""

import sys
sys.path.append('../..')

import numpy as np
from typing import Dict, List
import logging

from src.agent.oaal_framework import OAALFramework
from src.env.satellite_env import parallel_env

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_task_features(task_queue: List[Dict]) -> List[np.ndarray]:
    """从任务队列提取特征"""
    features = []
    for task in task_queue:
        feat = np.zeros(8)
        feat[0] = task.get('type_id', 0) / 5.0
        feat[1] = min(task.get('data_size_mb', 0) / 100.0, 1.0)
        feat[2] = min(task.get('complexity_cycles_per_bit', 100) / 1000.0, 1.0)
        feat[3] = task.get('priority', 1) / 3.0
        feat[4] = 0.5  # 简化的紧急度
        feat[5] = 0.0  # 等待时间
        feat[6] = 0.2  # 预计完成时间
        feat[7] = 0.0  # 是否可分割
        features.append(feat)
    return features


def run_oaal_training():
    """运行OAAL训练示例"""
    
    # 创建环境
    logger.info("创建卫星仿真环境...")
    env = parallel_env()
    
    # 创建OAAL框架
    logger.info("初始化OAAL框架...")
    oaal = OAALFramework(
        num_satellites=env.num_agents,
        num_domains=24,
        hidden_dim=256,
        learning_rate=3e-4
    )
    
    # 训练参数
    num_episodes = 10
    max_steps_per_episode = 100
    
    logger.info(f"开始训练 ({num_episodes} 回合)...")
    
    for episode in range(num_episodes):
        # 重置环境
        observations, infos = env.reset()
        oaal.reset_episode_stats()
        
        episode_rewards = {agent: 0.0 for agent in env.agents}
        
        for step in range(max_steps_per_episode):
            # 1. 更新卫星位置和域映射
            satellite_positions = {}
            for agent_id, info in infos.items():
                if 'position' in info:
                    satellite_positions[int(agent_id.split('_')[1])] = (
                        info['position']['latitude'],
                        info['position']['longitude']
                    )
            
            if satellite_positions:
                oaal.update_agent_positions(satellite_positions)
            
            # 2. 准备任务队列和有效动作
            task_queues = {}
            valid_actions = {}
            
            for agent_id, info in infos.items():
                agent_idx = int(agent_id.split('_')[1])
                
                # 获取任务队列
                task_queue = info.get('task_queue', [])
                task_queues[agent_idx] = task_queue
                
                # 获取有效动作
                valid_acts = []
                for _ in task_queue:
                    # 简化：每个任务都可以选择本地处理或卸载
                    valid_acts.append([0, 1, 2])  # 本地、卫星卸载、云卸载
                valid_actions[agent_idx] = valid_acts
            
            # 3. 准备观测数据
            obs_dict = {}
            for agent_id, obs in observations.items():
                agent_idx = int(agent_id.split('_')[1])
                obs_dict[agent_idx] = obs
            
            # 4. 选择动作
            if obs_dict and task_queues:
                actions_dict = oaal.select_actions(obs_dict, task_queues, valid_actions)
                
                # 转换动作格式（OAAL返回序列，环境期望单个动作）
                env_actions = {}
                for agent_id in env.agents:
                    agent_idx = int(agent_id.split('_')[1])
                    if agent_idx in actions_dict and actions_dict[agent_idx]:
                        # 使用第一个动作
                        env_actions[agent_id] = actions_dict[agent_idx][0]
                    else:
                        env_actions[agent_id] = 0  # 默认本地处理
            else:
                # 无任务时选择默认动作
                env_actions = {agent: 0 for agent in env.agents}
            
            # 5. 执行环境步骤
            next_observations, rewards, dones, next_infos = env.step(env_actions)
            
            # 6. 收集经验
            experiences = {}
            for agent_id, reward in rewards.items():
                agent_idx = int(agent_id.split('_')[1])
                
                if agent_idx in task_queues and task_queues[agent_idx]:
                    exp = {
                        'observation': observations[agent_id],
                        'task_features': extract_task_features(task_queues[agent_idx]),
                        'actions': [env_actions[agent_id]],
                        'reward': reward,
                        'next_observation': next_observations[agent_id],
                        'done': dones[agent_id],
                        'info': {}
                    }
                    
                    if agent_idx not in experiences:
                        experiences[agent_idx] = []
                    experiences[agent_idx].append(exp)
                
                episode_rewards[agent_id] += reward
            
            # 7. 执行智能体间协同
            if step % 5 == 0:  # 每5步执行一次协同
                satellite_states = {}
                communication_links = []
                
                # 准备协同数据（简化版）
                for agent_id, info in infos.items():
                    agent_idx = int(agent_id.split('_')[1])
                    satellite_states[agent_idx] = {
                        'observation': observations[agent_id],
                        'queue_length': len(info.get('task_queue', []))
                    }
                
                # 模拟通信链路
                for i in range(min(5, len(env.agents))):
                    for j in range(i+1, min(i+3, len(env.agents))):
                        communication_links.append({
                            'source_id': i,
                            'target_id': j,
                            'distance_km': np.random.uniform(500, 2000),
                            'data_rate_mbps': np.random.uniform(10, 100),
                            'delay_ms': np.random.uniform(5, 50),
                            'link_quality': np.random.uniform(0.5, 1.0)
                        })
                
                if satellite_states and communication_links:
                    coordination_decisions = oaal.coordinate_agents(
                        satellite_states, communication_links
                    )
            
            # 8. 更新智能体
            if experiences and step % 10 == 0:  # 每10步更新一次
                oaal.update_agents(experiences)
            
            # 9. 更新策略域
            if step % 20 == 0:  # 每20步更新一次
                global_stats = {}  # 简化的全局统计
                oaal.update_domains(step * 10.0, global_stats)
            
            # 更新状态
            observations = next_observations
            infos = next_infos
            
            # 检查是否结束
            if all(dones.values()):
                break
        
        # 回合结束，记录统计
        avg_reward = np.mean(list(episode_rewards.values()))
        logger.info(f"回合 {episode + 1}/{num_episodes} 完成, 平均奖励: {avg_reward:.2f}")
        
        # 获取并显示统计信息
        if episode % 5 == 0:
            stats = oaal.get_episode_stats()
            logger.info(f"统计信息: {stats}")
        
        # 保存检查点
        if (episode + 1) % 10 == 0:
            oaal.save_checkpoint(f"oaal_checkpoint_ep{episode+1}.pt")
    
    # 关闭环境
    env.close()
    logger.info("训练完成!")
    
    return oaal


def test_oaal_components():
    """测试OAAL各个组件"""
    logger.info("=== 测试OAAL组件 ===")
    
    # 1. 测试策略域
    logger.info("\n1. 测试策略域管理...")
    from src.agent.strategy_domain import DomainManager
    
    dm = DomainManager(num_domains=24)
    
    # 测试位置映射
    test_positions = [(-180, "Domain 12"), (0, "Domain 0"), (90, "Domain 18"), (179, "Domain 23")]
    for lon, expected in test_positions:
        domain = dm.get_domain_for_position(lon)
        if domain:
            logger.info(f"  经度 {lon}° -> 域 {domain.domain_id}")
    
    # 2. 测试Transformer调度器
    logger.info("\n2. 测试Transformer调度器...")
    from src.agent.transformer_scheduler import TransformerScheduler
    
    scheduler = TransformerScheduler()
    
    # 模拟输入
    obs = np.random.randn(15)
    task_queue = [
        {'type_id': 1, 'data_size_mb': 50, 'priority': 2},
        {'type_id': 2, 'data_size_mb': 30, 'priority': 3}
    ]
    valid_actions = [[0, 1, 2], [0, 1]]  # 每个任务的有效动作
    
    actions = scheduler.generate_action_sequence(obs, task_queue, valid_actions)
    logger.info(f"  生成的动作序列: {actions}")
    
    # 3. 测试图注意力协同
    logger.info("\n3. 测试图注意力协同...")
    from src.agent.graph_attention import CoordinationManager
    
    coord_mgr = CoordinationManager(num_satellites=4)
    
    # 模拟卫星状态
    sat_states = {
        0: {'observation': np.random.randn(15), 'queue_length': 8},
        1: {'observation': np.random.randn(15), 'queue_length': 2},
        2: {'observation': np.random.randn(15), 'queue_length': 9},
        3: {'observation': np.random.randn(15), 'queue_length': 1}
    }
    
    # 模拟通信链路
    comm_links = [
        {'source_id': 0, 'target_id': 1, 'distance_km': 1000, 'link_quality': 0.9},
        {'source_id': 1, 'target_id': 2, 'distance_km': 1500, 'link_quality': 0.7},
        {'source_id': 2, 'target_id': 3, 'distance_km': 800, 'link_quality': 0.8}
    ]
    
    logger.info("  卫星负载情况:")
    for sat_id, state in sat_states.items():
        logger.info(f"    卫星{sat_id}: 队列长度={state['queue_length']}")
    
    logger.info("\n=== 组件测试完成 ===")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='OAAL框架示例')
    parser.add_argument('--mode', type=str, default='test', 
                       choices=['test', 'train'],
                       help='运行模式: test(测试组件) 或 train(训练示例)')
    
    args = parser.parse_args()
    
    if args.mode == 'test':
        test_oaal_components()
    else:
        run_oaal_training()